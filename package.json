{"name": "myvirgin-preauth-setup", "version": "1.0.0", "description": "", "main": "dist/widget.js", "private": true, "scripts": {"linklocal": "linklocal", "dev": "webpack -w", "build": "webpack", "build:dev": "webpack --env -d", "build:prod": "webpack --env -p", "start": "http-server", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "VIRGIN", "license": "MIT", "devDependencies": {"@types/react-router-dom": "*", "@virgin/virgin-react-ui-library": "^3.11.12", "bwtk": "git+https://gitlab.int.bell.ca/uxp/bwtk.git#v6.1.0", "css-loader": "^6.2.0", "husky": "4.3.8", "prettier": "2.3.2", "myvirgin-preauth-common": "file:../myvirgin-preauth-common"}, "husky": {"hooks": {"pre-commit": "node pre-commit.js"}}}