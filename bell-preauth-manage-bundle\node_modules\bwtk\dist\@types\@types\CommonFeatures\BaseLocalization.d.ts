import { Localization, LocalizationState, MessagesDictionary } from "../LocalizationServices";
import { ConfigServices } from "../ConfigServices";
export declare abstract class BaseLocalization {
    protected localization: Localization;
    private config;
    private disableNetworkGlobal;
    private showKeys;
    constructor(localization: Localization, config: ConfigServices);
    protected get defaultMessages(): MessagesDictionary;
    protected disableNetwork: boolean;
    createReducer(): (state: LocalizationState | undefined, action: any) => {
        messages: {
            [x: string]: {};
        } | {
            [x: string]: string;
        };
        locale: import("../LocalizationServices").LocaleValue;
        fullLocale: import("../LocalizationServices").FullLocaleValue;
        formats: {
            [key: string]: any;
        };
        loaded: boolean;
    };
}
