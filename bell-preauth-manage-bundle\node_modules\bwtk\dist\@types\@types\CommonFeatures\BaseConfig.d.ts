import { Id } from "../Id";
import { Name } from "../Name";
import { Namespace } from "../Namespace";
import { ConfigServices } from "../ConfigServices";
import { ParamsProvider } from "../ParamsServices";
export declare abstract class BaseConfig<TProps> {
    private id;
    private name;
    private namespace;
    private config;
    private paramsProvider;
    private defaultValues;
    constructor(id: Id, name: Name, namespace: Namespace, config: ConfigServices, paramsProvider: ParamsProvider<TProps, {}>);
    getConfig<T>(prop: keyof TProps): T | (TProps[keyof TProps] & ({} | null));
    protected configKeyOf(prop: keyof TProps): string;
    protected configKeyFormat(prop: keyof TProps): string;
    setConfig(prop: string, value: any): void;
    setDefaultConfig(prop: Extract<keyof TProps, string>, value: any): void;
    private setDelayedDefaultConfig;
}
export declare const configProperty: any;
export interface ConfigDecoratorFactory {
    (options: ConfigOptions): PropertyDecorator;
}
export interface ConfigOptions {
    defaultValue: any;
}
