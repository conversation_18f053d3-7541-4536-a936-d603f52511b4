!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("rxjs"),require("redux")):"function"==typeof define&&define.amd?define(["exports","rxjs","redux"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).ReduxObservable={},t.rxjs,t.Redux)}(this,(function(t,e,r){"use strict";function n(t){return"function"==typeof t}function o(t){return function(e){if(function(t){return n(null==t?void 0:t.lift)}(e))return e.lift((function(e){try{return t(e,this)}catch(t){this.error(t)}}));throw new TypeError("Unable to lift unknown Observable type")}}var i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},i(t,e)};function u(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function c(t,e,r,n){return new(r||(r=Promise))((function(o,i){function u(t){try{s(n.next(t))}catch(t){i(t)}}function c(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(u,c)}s((n=n.apply(t,e||[])).next())}))}function s(t,e){var r,n,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},u=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return u.next=c(0),u.throw=c(1),u.return=c(2),"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function c(c){return function(s){return function(c){if(r)throw new TypeError("Generator is already executing.");for(;u&&(u=0,c[0]&&(i=0)),i;)try{if(r=1,n&&(o=2&c[0]?n.return:c[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,c[1])).done)return o;switch(n=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return i.label++,{value:c[1],done:!1};case 5:i.label++,n=c[1],c=[0];continue;case 7:c=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){i=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){i.label=c[1];break}if(6===c[0]&&i.label<o[1]){i.label=o[1],o=c;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(c);break}o[2]&&i.ops.pop(),i.trys.pop();continue}c=e.call(t,i)}catch(t){c=[6,t],n=0}finally{r=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}}function a(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function l(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u}function f(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))}function p(t){return this instanceof p?(this.v=t,this):new p(t)}function h(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,o=r.apply(t,e||[]),i=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),u("next"),u("throw"),u("return",(function(t){return function(e){return Promise.resolve(e).then(t,a)}})),n[Symbol.asyncIterator]=function(){return this},n;function u(t,e){o[t]&&(n[t]=function(e){return new Promise((function(r,n){i.push([t,e,r,n])>1||c(t,e)}))},e&&(n[t]=e(n[t])))}function c(t,e){try{(r=o[t](e)).value instanceof p?Promise.resolve(r.value.v).then(s,a):l(i[0][2],r)}catch(t){l(i[0][3],t)}var r}function s(t){c("next",t)}function a(t){c("throw",t)}function l(t,e){t(e),i.shift(),i.length&&c(i[0][0],i[0][1])}}function y(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,r=t[Symbol.asyncIterator];return r?r.call(t):(t=a(t),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);function n(r){e[r]=t[r]&&function(e){return new Promise((function(n,o){(function(t,e,r,n){Promise.resolve(n).then((function(e){t({value:e,done:r})}),e)})(n,o,(e=t[r](e)).done,e.value)}))}}}"function"==typeof SuppressedError&&SuppressedError;var b,d=((b=function(t){return function(e){t(this),this.message=e?e.length+" errors occurred during unsubscription:\n"+e.map((function(t,e){return e+1+") "+t.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=e}}((function(t){Error.call(t),t.stack=(new Error).stack}))).prototype=Object.create(Error.prototype),b.prototype.constructor=b,b);function v(t,e){if(t){var r=t.indexOf(e);0<=r&&t.splice(r,1)}}var m=function(){function t(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}var e;return t.prototype.unsubscribe=function(){var t,e,r,o,i;if(!this.closed){this.closed=!0;var u=this._parentage;if(u)if(this._parentage=null,Array.isArray(u))try{for(var c=a(u),s=c.next();!s.done;s=c.next()){s.value.remove(this)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(e=c.return)&&e.call(c)}finally{if(t)throw t.error}}else u.remove(this);var p=this.initialTeardown;if(n(p))try{p()}catch(t){i=t instanceof d?t.errors:[t]}var h=this._finalizers;if(h){this._finalizers=null;try{for(var y=a(h),b=y.next();!b.done;b=y.next()){var v=b.value;try{x(v)}catch(t){i=null!=i?i:[],t instanceof d?i=f(f([],l(i)),l(t.errors)):i.push(t)}}}catch(t){r={error:t}}finally{try{b&&!b.done&&(o=y.return)&&o.call(y)}finally{if(r)throw r.error}}}if(i)throw new d(i)}},t.prototype.add=function(e){var r;if(e&&e!==this)if(this.closed)x(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=null!==(r=this._finalizers)&&void 0!==r?r:[]).push(e)}},t.prototype._hasParent=function(t){var e=this._parentage;return e===t||Array.isArray(e)&&e.includes(t)},t.prototype._addParent=function(t){var e=this._parentage;this._parentage=Array.isArray(e)?(e.push(t),e):e?[e,t]:t},t.prototype._removeParent=function(t){var e=this._parentage;e===t?this._parentage=null:Array.isArray(e)&&v(e,t)},t.prototype.remove=function(e){var r=this._finalizers;r&&v(r,e),e instanceof t&&e._removeParent(this)},t.EMPTY=((e=new t).closed=!0,e),t}();function w(t){return t instanceof m||t&&"closed"in t&&n(t.remove)&&n(t.add)&&n(t.unsubscribe)}function x(t){n(t)?t():t.unsubscribe()}m.EMPTY;var _={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},S={setTimeout:function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return setTimeout.apply(void 0,f([t,e],l(r)))},clearTimeout:function(t){var e=S.delegate;return((null==e?void 0:e.clearTimeout)||clearTimeout)(t)},delegate:void 0};function g(t){S.setTimeout((function(){throw t}))}function E(){}var T=function(t){function e(e){var r=t.call(this)||this;return r.isStopped=!1,e?(r.destination=e,w(e)&&e.add(r)):r.destination=A,r}return u(e,t),e.create=function(t,e,r){return new O(t,e,r)},e.prototype.next=function(t){this.isStopped||this._next(t)},e.prototype.error=function(t){this.isStopped||(this.isStopped=!0,this._error(t))},e.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},e.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this),this.destination=null)},e.prototype._next=function(t){this.destination.next(t)},e.prototype._error=function(t){try{this.destination.error(t)}finally{this.unsubscribe()}},e.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},e}(m),j=function(){function t(t){this.partialObserver=t}return t.prototype.next=function(t){var e=this.partialObserver;if(e.next)try{e.next(t)}catch(t){P(t)}},t.prototype.error=function(t){var e=this.partialObserver;if(e.error)try{e.error(t)}catch(t){P(t)}else P(t)},t.prototype.complete=function(){var t=this.partialObserver;if(t.complete)try{t.complete()}catch(t){P(t)}},t}(),O=function(t){function e(e,r,o){var i,u=t.call(this)||this;return i=n(e)||!e?{next:null!=e?e:void 0,error:null!=r?r:void 0,complete:null!=o?o:void 0}:e,u.destination=new j(i),u}return u(e,t),e}(T);function P(t){g(t)}var A={closed:!0,next:E,error:function(t){throw t},complete:E},I="function"==typeof Symbol&&Symbol.observable||"@@observable";function k(t){return t}var z=function(){function t(t){t&&(this._subscribe=t)}return t.prototype.lift=function(e){var r=new t;return r.source=this,r.operator=e,r},t.prototype.subscribe=function(t,e,r){var o,i=this,u=(o=t)&&o instanceof T||function(t){return t&&n(t.next)&&n(t.error)&&n(t.complete)}(o)&&w(o)?t:new O(t,e,r);return function(){var t=i,e=t.operator,r=t.source;u.add(e?e.call(u,r):r?i._subscribe(u):i._trySubscribe(u))}(),u},t.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(e){t.error(e)}},t.prototype.forEach=function(t,e){var r=this;return new(e=R(e))((function(e,n){var o=new O({next:function(e){try{t(e)}catch(t){n(t),o.unsubscribe()}},error:n,complete:e});r.subscribe(o)}))},t.prototype._subscribe=function(t){var e;return null===(e=this.source)||void 0===e?void 0:e.subscribe(t)},t.prototype[I]=function(){return this},t.prototype.pipe=function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return(0===(t=e).length?k:1===t.length?t[0]:function(e){return t.reduce((function(t,e){return e(t)}),e)})(this)},t.prototype.toPromise=function(t){var e=this;return new(t=R(t))((function(t,r){var n;e.subscribe((function(t){return n=t}),(function(t){return r(t)}),(function(){return t(n)}))}))},t.create=function(e){return new t(e)},t}();function R(t){var e;return null!==(e=null!=t?t:_.Promise)&&void 0!==e?e:Promise}var U="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function D(t){if(t instanceof z)return t;if(null!=t){if(function(t){return n(t[I])}(t))return c=t,new z((function(t){var e=c[I]();if(n(e.subscribe))return e.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")}));if((u=t)&&"number"==typeof u.length&&"function"!=typeof u)return i=t,new z((function(t){for(var e=0;e<i.length&&!t.closed;e++)t.next(i[e]);t.complete()}));if(n(null==(o=t)?void 0:o.then))return r=t,new z((function(t){r.then((function(e){t.closed||(t.next(e),t.complete())}),(function(e){return t.error(e)})).then(null,g)}));if(function(t){return Symbol.asyncIterator&&n(null==t?void 0:t[Symbol.asyncIterator])}(t))return Y(t);if(function(t){return n(null==t?void 0:t[U])}(t))return e=t,new z((function(t){var r,n;try{for(var o=a(e),i=o.next();!i.done;i=o.next()){var u=i.value;if(t.next(u),t.closed)return}}catch(t){r={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}t.complete()}));if(function(t){return n(null==t?void 0:t.getReader)}(t))return Y(function(t){return h(this,arguments,(function(){var e,r,n;return s(this,(function(o){switch(o.label){case 0:e=t.getReader(),o.label=1;case 1:o.trys.push([1,,9,10]),o.label=2;case 2:return[4,p(e.read())];case 3:return r=o.sent(),n=r.value,r.done?[4,p(void 0)]:[3,5];case 4:return[2,o.sent()];case 5:return[4,p(n)];case 6:return[4,o.sent()];case 7:return o.sent(),[3,2];case 8:return[3,10];case 9:return e.releaseLock(),[7];case 10:return[2]}}))}))}(t))}var e,r,o,i,u,c;throw function(t){return new TypeError("You provided "+(null!==t&&"object"==typeof t?"an invalid object":"'"+t+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}(t)}function Y(t){return new z((function(e){(function(t,e){var r,n,o,i;return c(this,void 0,void 0,(function(){var u,c;return s(this,(function(s){switch(s.label){case 0:s.trys.push([0,5,6,11]),r=y(t),s.label=1;case 1:return[4,r.next()];case 2:if((n=s.sent()).done)return[3,4];if(u=n.value,e.next(u),e.closed)return[2];s.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return c=s.sent(),o={error:c},[3,11];case 6:return s.trys.push([6,,9,10]),n&&!n.done&&(i=r.return)?[4,i.call(r)]:[3,8];case 7:s.sent(),s.label=8;case 8:return[3,10];case 9:if(o)throw o.error;return[7];case 10:return[7];case 11:return e.complete(),[2]}}))}))})(t,e).catch((function(t){return e.error(t)}))}))}function q(t,e,r,n,o){return new C(t,e,r,n,o)}var C=function(t){function e(e,r,n,o,i,u){var c=t.call(this,e)||this;return c.onFinalize=i,c.shouldUnsubscribe=u,c._next=r?function(t){try{r(t)}catch(t){e.error(t)}}:t.prototype._next,c._error=o?function(t){try{o(t)}catch(t){e.error(t)}finally{this.unsubscribe()}}:t.prototype._error,c._complete=n?function(){try{n()}catch(t){e.error(t)}finally{this.unsubscribe()}}:t.prototype._complete,c}return u(e,t),e.prototype.unsubscribe=function(){var e;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;t.prototype.unsubscribe.call(this),!r&&(null===(e=this.onFinalize)||void 0===e||e.call(this))}},e}(T);function F(t,e,r,n,o){void 0===n&&(n=0),void 0===o&&(o=!1);var i=e.schedule((function(){r(),o?t.add(this.schedule(null,n)):this.unsubscribe()}),n);if(t.add(i),!o)return i}function M(t,e){return void 0===e&&(e=0),o((function(r,n){r.subscribe(q(n,(function(r){return F(n,t,(function(){return n.next(r)}),e)}),(function(){return F(n,t,(function(){return n.complete()}),e)}),(function(r){return F(n,t,(function(){return n.error(r)}),e)})))}))}function N(t,e){return o((function(r,n){var o=0;r.subscribe(q(n,(function(r){n.next(t.call(e,r,o++))})))}))}function $(t,e,r){return void 0===r&&(r=1/0),n(e)?$((function(r,n){return N((function(t,o){return e(r,t,n,o)}))(D(t(r,n)))}),r):("number"==typeof e&&(r=e),o((function(e,n){return function(t,e,r,n,o,i,u){var c=[],s=0,a=0,l=!1,f=function(){!l||c.length||s||e.complete()},p=function(t){s++;var o=!1;D(r(t,a++)).subscribe(q(e,(function(t){e.next(t)}),(function(){o=!0}),void 0,(function(){if(o)try{for(s--;c.length&&s<n;)t=void 0,t=c.shift(),u||p(t);f()}catch(t){e.error(t)}var t})))};return t.subscribe(q(e,(function(t){return s<n?p(t):c.push(t)}),(function(){l=!0,f()}))),function(){}}(e,n,t,r)})))}var G=class extends e.Observable{value;__notifier=new e.Subject;constructor(t,e){super((t=>{const e=this.__notifier.subscribe(t);return e&&!e.closed&&t.next(this.value),e})),this.value=e,t.subscribe((t=>{t!==this.value&&(this.value=t,this.__notifier.next(t))}))}};"object"==typeof console&&"function"==typeof console.warn&&console.warn.bind(console),t.StateObservable=G,t.__FOR_TESTING__resetDeprecationsSeen=()=>{},t.combineEpics=function(...t){const r=(...r)=>e.merge(...t.map((t=>{const e=t(...r);if(!e)throw new TypeError(`combineEpics: one of the provided Epics "${t.name||"<anonymous>"}" does not return a stream. Double check you're not missing a return statement!`);return e})));try{Object.defineProperty(r,"name",{value:`combineEpics(${t.map((t=>t.name||"<anonymous>")).join(", ")})`})}catch(t){}return r},t.createEpicMiddleware=function(t={}){const r=new(0,e.queueScheduler.constructor)(e.queueScheduler.schedulerActionCtor),n=new e.Subject;let i;const u=u=>{i=u;const c=new e.Subject,s=new e.Subject,a=c.asObservable().pipe(M(r)),l=new G(s.pipe(M(r)),i.getState());return n.pipe(N((e=>{const r=e(a,l,t.dependencies);if(!r)throw new TypeError(`Your root Epic "${e.name||"<anonymous>"}" does not return a stream. Double check you're not missing a return statement!`);return r})),$((t=>{return e.from(t).pipe((n=r,void 0===i&&(i=0),o((function(t,e){e.add(n.schedule((function(){return t.subscribe(e)}),i))}))),M(r));var n,i}))).subscribe(i.dispatch),t=>e=>{const r=t(e);return s.next(i.getState()),c.next(e),r}};return u.run=t=>{n.next(t)},u},t.ofType=function(...t){const e=t.length;return n=1===e?e=>r.isAction(e)&&e.type===t[0]:n=>{if(r.isAction(n))for(let r=0;r<e;r++)if(n.type===t[r])return!0;return!1},o((function(t,e){var r=0;t.subscribe(q(e,(function(t){return n.call(i,t,r++)&&e.next(t)})))}));var n,i}}));
