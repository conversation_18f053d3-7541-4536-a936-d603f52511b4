/* @generated */
// prettier-ignore
if (Intl.ListFormat && typeof Intl.ListFormat.__addLocaleData === 'function') {
  Intl.ListFormat.__addLocaleData({
  "data": {
    "conjunction": {
      "long": {
        "end": "{0} و{1}",
        "middle": "{0} و{1}",
        "pair": "{0} و{1}",
        "start": "{0} و{1}"
      },
      "narrow": {
        "end": "{0} و{1}",
        "middle": "{0} و{1}",
        "pair": "{0} و{1}",
        "start": "{0} و{1}"
      },
      "short": {
        "end": "{0} و{1}",
        "middle": "{0} و{1}",
        "pair": "{0} و{1}",
        "start": "{0} و{1}"
      }
    },
    "disjunction": {
      "long": {
        "end": "{0} أو {1}",
        "middle": "{0} أو {1}",
        "pair": "{0} أو {1}",
        "start": "{0} أو {1}"
      },
      "narrow": {
        "end": "{0} أو {1}",
        "middle": "{0} أو {1}",
        "pair": "{0} أو {1}",
        "start": "{0} أو {1}"
      },
      "short": {
        "end": "{0} أو {1}",
        "middle": "{0} أو {1}",
        "pair": "{0} أو {1}",
        "start": "{0} أو {1}"
      }
    },
    "unit": {
      "long": {
        "end": "{0}، و{1}",
        "middle": "{0}، و{1}",
        "pair": "{0} و{1}",
        "start": "{0}، و{1}"
      },
      "narrow": {
        "end": "{0} و{1}",
        "middle": "{0} و{1}",
        "pair": "{0} و{1}",
        "start": "{0} و{1}"
      },
      "short": {
        "end": "{0}، و{1}",
        "middle": "{0}، و{1}",
        "pair": "{0} و{1}",
        "start": "{0}، و{1}"
      }
    }
  },
  "locale": "ar-SD"
})
}