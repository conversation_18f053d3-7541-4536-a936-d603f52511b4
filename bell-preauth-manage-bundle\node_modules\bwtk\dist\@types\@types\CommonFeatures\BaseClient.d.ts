import { AjaxOptions, AjaxServices, AjaxResponse } from "../AjaxServices";
import { Observable } from "rxjs";
export declare abstract class BaseClient {
    private ajax;
    private _client;
    constructor(ajax: AjaxServices);
    protected init(): void;
    private get client();
    abstract get options(): AjaxOptions;
    get<T>(path: string, data?: any, options?: AjaxOptions): Observable<T>;
    post<T>(path: string, data: any, options?: AjaxOptions): Observable<T>;
    put<T>(path: string, data: any, options?: AjaxOptions): Observable<T>;
    patch<T>(path: string, data: any, options?: AjaxOptions): Observable<T>;
    del(path: string, options?: AjaxOptions): Observable<AjaxResponse>;
    private createObservableRequest;
}
