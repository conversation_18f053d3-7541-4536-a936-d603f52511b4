import React from "react";
import { DivProps } from "@bell/bell-ui-library/dist/@types/src/_types";
import {Text} from "@bell/bell-ui-library";


export interface AlertNotificationListProps extends DivProps{
  label?: string;
  className?: string;
  labelClassName?: string;
  listClassName?: string;
}

export const AlertNotificationList = ({label, className, labelClassName, listClassName, children,...rest }: AlertNotificationListProps) => {
  return (
    <div className={["payment-pb-15 last:payment-pb-0 payment-pt-15 first:payment-pt-0 payment-border-b-1 payment-border-b-gray-4 last:payment-border-none", className].join(" ").trim()} {...rest}>
      {label && 
      <Text className={[
        "payment-block payment-leading-18 payment-text-14 payment-mb-15 payment-text-gray sm:payment-mt-15 payment-mt-5", labelClassName].join(" ").trim()}>{label}</Text>
      }
      <div role="list" 
      className={["payment-text-14 payment-leading-18", listClassName].join(" ").trim()}>
        {children}
      </div>
    </div>
  );
};

export default AlertNotificationList;
