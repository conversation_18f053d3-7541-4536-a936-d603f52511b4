import { Store as RStore, Observable } from "redux";
import { StoreServices, Store, Id } from "../../../@types";
export declare class ReduxStore extends Store implements RStore<any> {
    id: Id;
    private storeServices;
    initialized: boolean;
    listenToAll: boolean;
    constructor(id: Id, storeServices: StoreServices);
    [Symbol.observable](): Observable<any>;
    init(): void;
    createStore(): any;
    private store;
    private unregister;
    private unsubscribers;
    getState(): any;
    replaceReducer(reducer: any): void;
    dispatch: (action: any) => any;
    subscribe: (listener: () => void) => () => void;
    notifyActionListener(action: any): void;
    destroy(): void;
}
