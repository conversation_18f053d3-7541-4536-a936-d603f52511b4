import { IWebpackStatsModuleBase } from "./webpack-stats";
export interface IModule extends IWebpackStatsModuleBase {
    baseName: string | null;
    isNodeModules: boolean;
    isSynthetic: boolean;
    source: string | null;
}
export declare const SYNTHETIC_SOURCE_TOKEN = "synthetic";
export interface IActionModule {
    baseName: string | null;
    fileName: string;
    size: {
        full: number;
    };
}
