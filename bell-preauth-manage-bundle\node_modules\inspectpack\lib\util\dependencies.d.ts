export interface INpmPackageBase {
    name: string;
    range: string;
    version: string;
}
export interface INpmPackage extends INpmPackageBase {
    dependencies: {
        [key: string]: string;
    };
    devDependencies: {
        [key: string]: string;
    };
}
interface INpmPackageMapPromise {
    [pkgPath: string]: Promise<INpmPackage | null>;
}
export interface INpmPackageMap {
    [pkgPath: string]: INpmPackage | null;
}
export interface IDependencies extends INpmPackageBase {
    dependencies: IDependencies[];
    filePath: string;
}
export declare const _files: {
    readJson: (file: string) => Promise<any>;
};
/**
 * Read a `package.json`.
 *
 * These calls are memoized into a cache if provided as this is the only real
 * I/O involved.
 *
 * @param   {String}                  path  full path to `package.json`
 * @param   {INpmPackageMapPromise?}  cache cache object
 * @returns {Promise<INpmPackage | null>} object w/ package.json info
 */
export declare const readPackage: (path: string, cache?: INpmPackageMapPromise | undefined) => Promise<INpmPackage | null>;
/**
 * Recursively traverse a directory and inflate `package.json` information.
 *
 * These calls are memoized into a cache.
 *
 * @param   {String}                  path        starting path
 * @param   {String[]?}               pkgsFilter  limit to only these package names
 * @param   {INpmPackageMapPromise?}  cache       package map
 * @returns {Promise<INpmPackageMapPromise>} map of paths to package info
 */
export declare const readPackages: (path: string, pkgsFilter?: string[] | undefined, cache?: INpmPackageMapPromise | undefined) => Promise<INpmPackageMapPromise>;
export declare const _resolvePackageMap: (pkgMap: INpmPackageMapPromise) => Promise<INpmPackageMap>;
export declare const _findPackage: ({ filePath, name, pkgMap, }: {
    filePath: string;
    name: string;
    pkgMap: INpmPackageMap;
}) => {
    isFlattened: boolean;
    pkgPath: string | null;
    pkgObj: INpmPackage | null;
};
/**
 * Create a dependency graph as **depended**, irrespective of tree flattening.
 *
 * The basic scheme is as follows:
 * - Take in a pre-existing list of all possible package names to limit the
 *   I/O and recursion we're going to do
 * - Read in **all** potential packages from the starting file path (limited
 *   to _potential_ packages we need) to an object of file paths : package data.
 * - Recursively traverse up paths like real node resolution to find things
 *   while assembling our logical dependencies structure.
 *
 * @param   {String}                  filePath    full path to dir w/ `package.json`
 * @param   {String[]?}               pkgsFilter  limit to only these package names
 * @param   {INpmPackageMapPromise?}  cache       cache object
 * @returns {Promise<IDependencies | null>} dependencies graph object
 */
export declare const dependencies: (filePath: string, pkgsFilter?: string[] | undefined, cache?: INpmPackageMapPromise | undefined) => Promise<IDependencies | null>;
export interface IDependenciesByPackageName {
    [packageName: string]: {
        [version: string]: {
            [filePath: string]: {
                skews: INpmPackageBase[][];
            };
        };
    };
}
/**
 * Create a lookup table by package name + version.
 *
 * @param   {IDependencies}               deps  dependencies graph
 * @returns {IDependenciesByPackageName}        lookup table
 */
export declare const mapDepsToPackageName: (deps: IDependencies) => IDependenciesByPackageName;
export {};
