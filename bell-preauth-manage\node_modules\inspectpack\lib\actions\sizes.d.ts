import { IActionModule } from "../interfaces/modules";
import { IAction, IActionConstructor } from "./base";
interface ISizesAssets {
    [asset: string]: {
        meta: {
            full: number;
        };
        files: IActionModule[];
    };
}
export interface ISizesData {
    meta: {
        full: number;
    };
    assets: ISizesAssets;
}
export declare const create: (opts: IActionConstructor) => IAction;
export {};
