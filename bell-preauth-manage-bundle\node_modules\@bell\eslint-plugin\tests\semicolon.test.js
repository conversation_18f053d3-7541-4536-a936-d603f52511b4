const { RuleTester } = require("eslint");
const rule = require("@stylistic/eslint-plugin").rules.semi;

const ruleTester = new RuleTester({
  languageOptions: {
    ecmaVersion: 2021,
  },
});

describe("Semicolon", () => {
  ruleTester.run("semicolon", rule, {
    valid: [{ code: "let x = 5;" }],
    invalid: [
      {
        code: "let x = 5",
        errors: [{ message: "Missing semicolon." }],
        output: "let x = 5;",
      },
    ],
  });
});
