import { Id } from "./Id";
export declare abstract class StoreServices {
    private _id;
    get id(): string;
    set id(value: string);
    getState: () => any;
    dispatch: <T extends IAction>(action: T) => T;
    abstract createStore(id: string): Store;
    abstract registerStore(store: Store): Function;
    abstract createGlobalActionListener(listener: Function): Function;
    abstract notifyActionListener(action: any): void;
}
export declare abstract class Store {
    id: Id;
    listenToAll: boolean;
    initialized?: boolean;
    dispatch: <T extends IAction>(action: T) => T;
    subscribe: (listener: Function) => Function;
    abstract init(): void;
    abstract destroy(): void;
    abstract createStore(): any;
    abstract getState(): any;
    abstract replaceReducer(reducer: (state: any, action: IAction) => void): void;
    abstract notifyActionListener(action: any): void;
}
export interface IAction extends ReduxActions.Action<any> {
}
