export declare abstract class LoggerServices {
    abstract createLogger(context: string, ...labels: string[]): Logger;
}
export declare abstract class Logger {
    abstract clear(): void;
    abstract debug(message?: any, ...optionalParams: any[]): void;
    abstract error(message?: any, ...optionalParams: any[]): void;
    abstract info(message?: any, ...optionalParams: any[]): void;
    abstract log(message?: any, ...optionalParams: any[]): void;
    abstract warn(message?: any, ...optionalParams: any[]): void;
}
export declare enum LoggerSeverityLevel {
    None = 0,
    Debug = 2,
    Errors = 4,
    Info = 8,
    Logs = 16,
    Warnings = 32,
    All = 62
}
export declare class LoggerConfigKeys {
    static get SeverityLevel(): string;
}
