import { StoreServices, Store, EventStreamServices } from "../../../@types";
export declare class ReduxStoreServices extends StoreServices {
    private stream;
    globalActionListeners: {
        listener: Function;
        params: {};
    }[];
    private stores;
    private state;
    constructor(stream: EventStreamServices);
    get id(): string;
    createStore(id: string): Store;
    createGlobalActionListener(listener: Function, params?: object): () => void;
    getState: () => {
        [key: string]: any;
    };
    dispatch: (action: any) => any;
    private tryInvokeActionListeners;
    registerStore(store: Store): () => void;
    notifyActionListener(action: any): void;
    replaceReducer(reducer: any): void;
    destroy(): void;
    destroyAll(): void;
}
