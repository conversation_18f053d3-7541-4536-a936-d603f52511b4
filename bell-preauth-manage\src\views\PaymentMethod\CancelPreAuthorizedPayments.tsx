import * as React from "react";
import { injectIntl } from "react-intl";
import { PaymentRadioCard } from "./PaymentMethodRadio";
import {NotifCard} from "../NotifCard";
import {
  Heading,
  ListItem,
  Text, Alert
} from "@bell/bell-ui-library";
import { AlertNotificationList, AlertNotificationListItem } from "../Alert";
import { 
  // CreditCardType, 
  PaymentItem, PaymentItemAccountType, PaymentItemAccountTypeName, SubscriberOffersWithBan } from "../../models";
import { getItemAccountTypeName, getPaymentItemCardType } from "../../utils";
import { getPaymentSelectOmniture } from "../../utils/Omniture";


interface CancelPaymentProps {
  intl: any;
  Checked: boolean;
  paymentItems: PaymentItem[];
  checkedBillItems: PaymentItem[];
  managePreauth: string;
  setCancelPreauthSectionClicked: Function;
  cancelPreauthSectionClicked: boolean;
  removedSubscriberOffers:SubscriberOffersWithBan[];
  province: string;
  IsAutopayCreditEnabled:boolean;
  setOmnitureOnPaymentSelect: Function;
  language: "en" | "fr";
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const CancelPaymentComponent = ({ intl, Checked, paymentItems, checkedBillItems, managePreauth, setCancelPreauthSectionClicked, cancelPreauthSectionClicked, removedSubscriberOffers, province, IsAutopayCreditEnabled, setOmnitureOnPaymentSelect, language, onChange }: CancelPaymentProps) => {

  let checkedBansNotOnPreauth;
  let checkedBansOnPreauth;

  if (paymentItems.length === 1) {
    checkedBansOnPreauth = paymentItems;
  }
  else if (paymentItems.length > 1) {
    checkedBansOnPreauth = checkedBillItems.filter(item => item.IsOnPreauthorizedPayments);
    checkedBansNotOnPreauth = checkedBillItems.filter(item => !item.IsOnPreauthorizedPayments);
  }

  const accountTypename: PaymentItemAccountTypeName = {
    MyBill: intl.formatMessage({ id: "ACCOUNT_TYPENAME_MYBILL" }),
    Mobility: intl.formatMessage({ id: "ACCOUNT_TYPENAME_MOBILITY" }),
    OneBill: intl.formatMessage({ id: "ACCOUNT_TYPENAME_ONEBILL" }),
    TV: intl.formatMessage({ id: "ACCOUNT_TYPENAME_TV" }),
    Internet: intl.formatMessage({ id: "ACCOUNT_TYPENAME_INTERNET" }),
    HomePhone: intl.formatMessage({ id: "ACCOUNT_TYPENAME_HOMEPHONE" }),
    MobilityAndOneBill: intl.formatMessage({ id: "ACCOUNT_TYPENAME_MOBILITY_ONEBILL" }),
    SingleBan: intl.formatMessage({ id: "ACCOUNT_TYPENAME_SINGLEBAN" })
  }

  const getCheckboxCCText = (paymentItem: PaymentItem) => {
    let preauthDetails;

    if ((paymentItem.IsOnPreauthorizedPayments || (managePreauth && managePreauth === "Creditcard" && paymentItem.CreditCardDetails)) && paymentItem.CreditCardDetails) {
      preauthDetails = intl.formatMessage({ id: "SELECT_BILLS_CC_DESC" }, {
        CreditCardType: getPaymentItemCardType(paymentItem.CreditCardDetails.CreditCardType),
        CCFourDigits: paymentItem.CreditCardDetails.CreditCardNumber.slice(-4),
        ExpiryDate: paymentItem.CreditCardDetails.ExpireMonth + "/" + paymentItem.CreditCardDetails.ExpireYear
      })
    }
    else if((paymentItem.IsOnPreauthorizedPayments || (managePreauth && managePreauth === "Debit" && paymentItem.BankAccountDetails)) && paymentItem.BankAccountDetails) {
      preauthDetails = intl.formatMessage({ id: "SELECT_BILLS_BANK_DESC" }, {
        BankName: paymentItem.BankAccountDetails.BankName,
        Code: paymentItem.BankAccountDetails.TransitCode,
        BankMaskedDigits: paymentItem.BankAccountDetails.AccountNumberMaskedDisplayView, 
      })
    }
    return preauthDetails;
}


  const handleOnChange = (event: any) => {
    if (event.target.checked)
    {
       setCancelPreauthSectionClicked(true);
       setOmnitureOnPaymentSelect(getPaymentSelectOmniture(false, true, checkedBillItems, true, ""));
    }
    else 
    {
      setCancelPreauthSectionClicked(false);
    }
  };

  let accountNumbers = "";
  if(checkedBansNotOnPreauth && checkedBansNotOnPreauth.length > 0 ){
    checkedBansNotOnPreauth.forEach((element, i) => {
       if (i === 0)
       {
         accountNumbers = element.NickName ?? element.BillName;
       }
       else 
       {
         accountNumbers = (accountNumbers + ", " + (element.NickName ?? element.BillName)) || "" ;
       }
    });
  }
  
  const getCancelAutopayOffers = (checkedBillItems: PaymentItem[], removedSubscriberOffers: SubscriberOffersWithBan[]) => {

    const groupedRemovedOffers = removedSubscriberOffers?.reduce((acc: any, debit: any) => {
      if (checkedBillItems?.some((billItem) => billItem.Ban === debit.Ban)) {
        debit.AutopayEligibleSubscribers?.forEach((item: any) => {
          if (!acc[debit.banInfo.nickName]) {
            acc[debit.banInfo.nickName] = [];
          }
          item.autopayOffers?.forEach((credit: any) => {
            acc[debit.banInfo.nickName].push({
              phoneNumber: item.subscriberTelephoneNumber,
              discountAmount: credit.discountAmount,
            });
          });
        });
      }
      return acc;
    }, {});
    
    const isMultipleOffersPresent = (groupedRemovedOffers: any) => {
      let isPlural = false;
      if (Object.keys(groupedRemovedOffers).length > 0) {
        if (Object.keys(groupedRemovedOffers).length > 1) {
          return true;
        }
        Object.keys(groupedRemovedOffers).forEach((ban) => {
          const value = groupedRemovedOffers[ban];
          if (value.length > 1) {
            isPlural = true;
          }
        });
      }
      return isPlural;
    }

    return <NotifCard 
        hasNotifCard={Object.keys(groupedRemovedOffers).length > 0}
        variant="notifCardWarning"
        label={isMultipleOffersPresent(groupedRemovedOffers) 
          ? intl.formatMessage({ id: "LABEL_LOADED_OFFERS_REMOVED_DEBIT_TITLE" })
          : intl.formatMessage({ id: "LABEL_LOADED_OFFER_REMOVED_DEBIT_TITLE" })}
        label2={intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING" })}
        label3={isMultipleOffersPresent(groupedRemovedOffers)
          ? intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE" })
          : intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE" })}
      >
        {Object.entries(groupedRemovedOffers).map(([ban, offers]: [string, any]) => (
              <div key={ban}>
                {Object.keys(groupedRemovedOffers).length > 1 && (<p className="payment-text-14 payment-text-gray payment-mb-5">{ban}:</p>)}
                <ul className="payment-list-disc payment-list-inside payment-mb-10">
                  {offers.map((offer: any, index: number) => (
                    <ListItem key={index} className="payment-text-14 payment-text-gray payment-mb-5 payment-leading-18">
                      {offer.phoneNumber} -&nbsp;
                      <div className="brui-font-bold brui-text-18 brui-text-blue brui-text-darkblue payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal" aria-hidden="true">
                        <span>{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT" }, { amount: offer.discountAmount })}</span>
                      </div>
                      <span className="brui-sr-only">{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT_SR" }, { amount: offer.discountAmount })}</span>
                    </ListItem>
                  ))}
                </ul>
              </div>
         ))}
      </NotifCard>
  }

  const isAtlanticProvince = province === "NB" || province === "NL" || province === "NS" || province === "PE";
  const isMTSProvince = province === "MB";

  const CANCEL_PAYMENT_LABEL = intl.formatMessage({ id: "CANCEL_PAYMENT_LABEL" });

  return (
    <div className="payment-mb-15">
      <PaymentRadioCard
        id="payment-radio-cancel"
        name="payment-radio"
        label={CANCEL_PAYMENT_LABEL}
        headingLevel="h3"
        checked={Checked}
        onChange={(e) => {
          handleOnChange(e);
          onChange?.(e);
          }
        }
      >
        <div>
          <Heading
            variant="default"
            level="h4"
            className="payment-font-sans payment-text-black payment-text-18 payment-text-left payment-leading-22 payment-font-bold"
          >
            {intl.formatMessage({ id: "CANCEL_PREAUTH_SUBHEADING" })}
          </Heading>
          <span className="payment-text-gray payment-text-14 payment-leading-18 payment-mt-5">
            {intl.formatMessage({ id: "CANCEL_PREAUTH_SUBHEADING_INFO" })}
          </span>
        </div>

        {/* Warning message - Final payment */}
        <div className="payment-pt-30">
          <Alert
            variant="warning"
            iconSize="36"
            className="sm:payment-flex sm:payment-border sm:payment-border-gray-4 sm:payment-rounded-[16px] sm:payment-p-30"
          >
            <div className="payment-w-full payment-mt-15 sm:payment-mt-0 sm:payment-ml-15">
              <div>
                <Heading
                  variant="default"
                  level="h4"
                  className="payment-font-sans payment-text-black payment-text-18 payment-text-left payment-leading-22 payment-font-bold"
                >
                  {intl.formatMessage({ id: "FINAL_PAYMENT" })}
                </Heading>
                <Text elementType="span" className="payment-text-14 payment-text-gray payment-leading-18 payment-mt-5">
                  {intl.formatMessage({ id: "FINAL_PAYMENT_HEADING" })}
                </Text>
              </div>
              {checkedBansOnPreauth && checkedBansOnPreauth.length > 0 &&
                <div>
                  <div>
                    <AlertNotificationList className="payment-mt-30" listClassName="payment-bg-gray-3 payment-rounded-10 payment-pr-15 payment-pl-15">
                      {checkedBansOnPreauth.map((item, i) => {
                        return (
                          <AlertNotificationListItem
                            variant="accountList"
                            label={getItemAccountTypeName(item.AccountType, item.IsNM1Account, accountTypename)}
                            labelDescription={item.NickName ?? item.BillName}
                            cardDetails={getCheckboxCCText(item)}
                            className="payment-bg-gray-3 payment-pb-15 last:payment-border-0 payment-border-b payment-border-gray-4"
                          />
                        )
                      })}
                    </AlertNotificationList>
                  </div>
                  {checkedBansNotOnPreauth && checkedBansNotOnPreauth.length > 0 && accountNumbers !== "" && accountNumbers !== undefined &&
                    <div className="payment-mt-30">
                      <p className="payment-text-gray payment-text-12 payment-leading-14"
                          dangerouslySetInnerHTML={{__html: intl.formatMessage({id: "NOT_PREAUTH_NOTE"}, {account: accountNumbers})}}>
                      </p>
                    </div>
                  }
                </div>
              }
            </div>
          </Alert>
        </div>

        {/* Autopay credits removed message */}
        {IsAutopayCreditEnabled &&<div className="payment-pt-15 sm:payment-pt-30">
        {!isAtlanticProvince && !isMTSProvince && checkedBillItems && removedSubscriberOffers !== undefined
          && getCancelAutopayOffers(checkedBillItems, removedSubscriberOffers)}

        {/* Possible AutoPay Credits (Atlantic, MTS, OneBill) -usecase */}
        {isAtlanticProvince || isMTSProvince || checkedBillItems.some((item: any) => item.IsOnPreauthorizedPayments && item.accountType === PaymentItemAccountType.OneBill) ?
            <Text elementType="div" className="payment-flex payment-items-center payment-mt-5">
              <p className="payment-text-gray payment-text-14"
                dangerouslySetInnerHTML={{__html: intl.formatMessage({id: "CANCEL_PREAUTH_POSSIBLE_CREDITS_NOTE"})}}>
              </p>
            </Text>
				:null}

        </div>}

      </PaymentRadioCard>
    </div>
  );
}


export const CancelPreAuthorizedPayments = injectIntl(CancelPaymentComponent);