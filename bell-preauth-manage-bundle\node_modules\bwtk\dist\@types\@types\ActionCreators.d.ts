import { InternalError, ReduxError } from "./Errors";
import { Action } from "redux-actions";
import { LocaleValue } from "./LocalizationServices";
export declare const raiseError: (error: InternalError) => Action<InternalError>;
export declare const setLocale: (locale: LocaleValue) => Action<LocaleValue>;
export declare const localeChanged: () => Action<void>;
export declare const loadMessages: () => Action<void>;
export declare const messagesLoaded: (error?: ReduxError) => Action<ReduxError | void>;
