"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_EXTRA_FILE_EXTENSIONS = exports.CORE_COMPILER_OPTIONS = void 0;
exports.canonicalDirname = canonicalDirname;
exports.createDefaultCompilerOptionsFromExtra = createDefaultCompilerOptionsFromExtra;
exports.createHash = createHash;
exports.ensureAbsolutePath = ensureAbsolutePath;
exports.getAstFromProgram = getAstFromProgram;
exports.getCanonicalFileName = getCanonicalFileName;
const node_path_1 = __importDefault(require("node:path"));
const ts = __importStar(require("typescript"));
/**
 * Compiler options required to avoid critical functionality issues
 */
const CORE_COMPILER_OPTIONS = {
    noEmit: true, // required to avoid parse from causing emit to occur
    /**
     * Flags required to make no-unused-vars work
     */
    noUnusedLocals: true,
    noUnusedParameters: true,
};
exports.CORE_COMPILER_OPTIONS = CORE_COMPILER_OPTIONS;
/**
 * Default compiler options for program generation
 */
const DEFAULT_COMPILER_OPTIONS = {
    ...CORE_COMPILER_OPTIONS,
    allowJs: true,
    allowNonTsExtensions: true,
    checkJs: true,
};
const DEFAULT_EXTRA_FILE_EXTENSIONS = new Set([
    ts.Extension.Cjs,
    ts.Extension.Cts,
    ts.Extension.Js,
    ts.Extension.Jsx,
    ts.Extension.Mjs,
    ts.Extension.Mts,
    ts.Extension.Ts,
    ts.Extension.Tsx,
]);
exports.DEFAULT_EXTRA_FILE_EXTENSIONS = DEFAULT_EXTRA_FILE_EXTENSIONS;
function createDefaultCompilerOptionsFromExtra(parseSettings) {
    if (parseSettings.debugLevel.has('typescript')) {
        return {
            ...DEFAULT_COMPILER_OPTIONS,
            extendedDiagnostics: true,
        };
    }
    return DEFAULT_COMPILER_OPTIONS;
}
// typescript doesn't provide a ts.sys implementation for browser environments
const useCaseSensitiveFileNames = 
// eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
ts.sys !== undefined ? ts.sys.useCaseSensitiveFileNames : true;
const correctPathCasing = useCaseSensitiveFileNames
    ? (filePath) => filePath
    : (filePath) => filePath.toLowerCase();
function getCanonicalFileName(filePath) {
    let normalized = node_path_1.default.normalize(filePath);
    if (normalized.endsWith(node_path_1.default.sep)) {
        normalized = normalized.slice(0, -1);
    }
    return correctPathCasing(normalized);
}
function ensureAbsolutePath(p, tsconfigRootDir) {
    return node_path_1.default.isAbsolute(p)
        ? p
        : node_path_1.default.join(tsconfigRootDir || process.cwd(), p);
}
function canonicalDirname(p) {
    return node_path_1.default.dirname(p);
}
const DEFINITION_EXTENSIONS = [
    ts.Extension.Dts,
    ts.Extension.Dcts,
    ts.Extension.Dmts,
];
function getExtension(fileName) {
    if (!fileName) {
        return null;
    }
    return (DEFINITION_EXTENSIONS.find(definitionExt => fileName.endsWith(definitionExt)) ?? node_path_1.default.extname(fileName));
}
function getAstFromProgram(currentProgram, filePath) {
    const ast = currentProgram.getSourceFile(filePath);
    // working around https://github.com/typescript-eslint/typescript-eslint/issues/1573
    const expectedExt = getExtension(filePath);
    const returnedExt = getExtension(ast?.fileName);
    if (expectedExt !== returnedExt) {
        return undefined;
    }
    return ast && { ast, program: currentProgram };
}
/**
 * Hash content for compare content.
 * @param content hashed contend
 * @returns hashed result
 */
function createHash(content) {
    // No ts.sys in browser environments.
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (ts.sys?.createHash) {
        return ts.sys.createHash(content);
    }
    return content;
}
//# sourceMappingURL=shared.js.map