export { COMMANDS, constructCommand, resolveCommand } from './commands.js';
export { AGENTS, INSTALL_PAGE, LOCKS } from './constants.js';
export { detect, detectSync, getUserAgent } from './detect.js';
export { A as Agent, b as AgentCommandValue, c as AgentCommands, a as AgentName, C as Command, D as DetectOptions, d as DetectResult, R as ResolvedCommand } from './shared/package-manager-detector.ncFwAKgD.js';
import 'quansync/types';
