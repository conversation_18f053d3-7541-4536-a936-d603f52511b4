{"name": "@formatjs/intl", "version": "2.9.0", "description": "Internationalize JS apps. This library provides an API to format dates, numbers, and strings, including pluralization and handling translations.", "keywords": ["intl", "i18n", "internationalization", "locale", "localization", "globalization", "react-intl", "format", "formatting", "translate", "translation"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://formatjs.io", "bugs": {"url": "https://github.com/formatjs/formatjs/issues"}, "repository": {"type": "git", "url": "**************:formatjs/formatjs.git"}, "main": "index.js", "module": "lib/index.js", "sideEffects": false, "dependencies": {"tslib": "^2.4.0", "@formatjs/ecma402-abstract": "1.17.0", "@formatjs/icu-messageformat-parser": "2.6.0", "@formatjs/intl-listformat": "7.4.0", "@formatjs/intl-displaynames": "6.5.0", "@formatjs/fast-memoize": "2.2.0", "intl-messageformat": "10.5.0"}, "devDependencies": {"@formatjs/intl-datetimeformat": "6.10.0", "@formatjs/intl-numberformat": "8.7.0"}, "peerDependencies": {"typescript": "^4.7 || 5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}