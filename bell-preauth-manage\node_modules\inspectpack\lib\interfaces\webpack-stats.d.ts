import * as t from "io-ts";
/**
 * Webpack module object interfaces.
 *
 * We use two "types" of interfaces here:
 * - `I<name>`: A standard TypeScript type/interface. Compiled away.
 * - `R<name>`: A outputted to `lib` `io-ts` data structure for runtime type
 *   checking.
 *
 * The webpack stats module objects have two "flavors":
 * - An object containing `source` with stringified source code.
 * - An object containing a modules array at the `modules` field.
 *
 * We bridge both with these compile + runtime types.
 */
declare const RWebpackStatsChunk: t.UnionC<[t.StringC, t.NumberC, t.NullC]>;
export declare type IWebpackStatsChunk = t.TypeOf<typeof RWebpackStatsChunk>;
declare const RWebpackStatsAsset: t.TypeC<{
    chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
    name: t.StringC;
    size: t.NumberC;
}>;
export declare type IWebpackStatsAsset = t.TypeOf<typeof RWebpackStatsAsset>;
declare const RWebpackStatsAssets: t.ArrayC<t.TypeC<{
    chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
    name: t.StringC;
    size: t.NumberC;
}>>;
export declare type IWebpackStatsAssets = t.TypeOf<typeof RWebpackStatsAssets>;
declare const RWebpackStatsModuleBase: t.TypeC<{
    chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
    identifier: t.StringC;
    size: t.NumberC;
}>;
export declare type IWebpackStatsModuleBase = t.TypeOf<typeof RWebpackStatsModuleBase>;
export declare const RWebpackStatsModuleSource: t.IntersectionC<[t.TypeC<{
    chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
    identifier: t.StringC;
    size: t.NumberC;
}>, t.TypeC<{
    name: t.StringC;
}>, t.TypeC<{
    source: t.StringC;
}>]>;
export declare type IWebpackStatsModuleSource = t.TypeOf<typeof RWebpackStatsModuleSource>;
export declare const RWebpackStatsModuleOrphan: t.IntersectionC<[t.IntersectionC<[t.TypeC<{
    chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
    identifier: t.StringC;
    size: t.NumberC;
}>, t.TypeC<{
    name: t.StringC;
}>, t.TypeC<{
    source: t.StringC;
}>]>, t.TypeC<{
    orphan: t.BooleanC;
}>]>;
export declare type IWebpackStatsModuleOrphan = t.TypeOf<typeof RWebpackStatsModuleOrphan>;
export declare const RWebpackStatsModuleSynthetic: t.IntersectionC<[t.TypeC<{
    chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
    identifier: t.StringC;
    size: t.NumberC;
}>, t.TypeC<{
    name: t.StringC;
}>]>;
export declare type IWebpackStatsModuleSynthetic = t.TypeOf<typeof RWebpackStatsModuleSynthetic>;
export interface IWebpackStatsModuleModules extends IWebpackStatsModuleBase {
    modules: (IWebpackStatsModuleSource | IWebpackStatsModuleModules)[];
}
export declare const RWebpackStatsModuleModules: t.RecursiveType<t.Type<IWebpackStatsModuleModules, IWebpackStatsModuleModules, unknown>, IWebpackStatsModuleModules, IWebpackStatsModuleModules, unknown>;
declare const RWebpackStatsModule: t.UnionC<[t.IntersectionC<[t.TypeC<{
    chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
    identifier: t.StringC;
    size: t.NumberC;
}>, t.TypeC<{
    name: t.StringC;
}>, t.TypeC<{
    source: t.StringC;
}>]>, t.IntersectionC<[t.TypeC<{
    chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
    identifier: t.StringC;
    size: t.NumberC;
}>, t.TypeC<{
    name: t.StringC;
}>]>, t.RecursiveType<t.Type<IWebpackStatsModuleModules, IWebpackStatsModuleModules, unknown>, IWebpackStatsModuleModules, IWebpackStatsModuleModules, unknown>]>;
export declare type IWebpackStatsModule = t.TypeOf<typeof RWebpackStatsModule>;
declare const RWebpackStatsModules: t.ArrayC<t.UnionC<[t.IntersectionC<[t.TypeC<{
    chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
    identifier: t.StringC;
    size: t.NumberC;
}>, t.TypeC<{
    name: t.StringC;
}>, t.TypeC<{
    source: t.StringC;
}>]>, t.IntersectionC<[t.TypeC<{
    chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
    identifier: t.StringC;
    size: t.NumberC;
}>, t.TypeC<{
    name: t.StringC;
}>]>, t.RecursiveType<t.Type<IWebpackStatsModuleModules, IWebpackStatsModuleModules, unknown>, IWebpackStatsModuleModules, IWebpackStatsModuleModules, unknown>]>>;
export declare type IWebpackStatsModules = t.TypeOf<typeof RWebpackStatsModules>;
export declare const RWebpackStats: t.TypeC<{
    assets: t.ArrayC<t.TypeC<{
        chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
        name: t.StringC;
        size: t.NumberC;
    }>>;
    modules: t.ArrayC<t.UnionC<[t.IntersectionC<[t.TypeC<{
        chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
        identifier: t.StringC;
        size: t.NumberC;
    }>, t.TypeC<{
        name: t.StringC;
    }>, t.TypeC<{
        source: t.StringC;
    }>]>, t.IntersectionC<[t.TypeC<{
        chunks: t.ArrayC<t.UnionC<[t.StringC, t.NumberC, t.NullC]>>;
        identifier: t.StringC;
        size: t.NumberC;
    }>, t.TypeC<{
        name: t.StringC;
    }>]>, t.RecursiveType<t.Type<IWebpackStatsModuleModules, IWebpackStatsModuleModules, unknown>, IWebpackStatsModuleModules, IWebpackStatsModuleModules, unknown>]>>;
}>;
export declare type IWebpackStats = t.TypeOf<typeof RWebpackStats>;
export {};
