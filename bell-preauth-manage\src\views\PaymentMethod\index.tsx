import * as React from "react";
import { <PERSON><PERSON>, HeadingStep } from "@bell/bell-ui-library";
import { BankPayment } from "./BankPayment";
import { CreditCardPayment } from "./CreditCardPayment";
import { injectIntl } from "react-intl";

import { AlertCreditCardErrorFormList, AlertCreditCardErrorInterac } from "../Alert";
import { CreditCardInputValue, CurrentSection, FieldType, PaymentItem, FormSubmit, SelectListItem, InputBankAccountDetail, PaymentItemAccountType, IGetRedirectUrl, IBankInfoRes, AccountInputValues, IBankInfoFailure, SubscriberOffersWithBan, ICancelledItem, CCDetails } from "../../models";
import { getPaymentItemCardType, GetCreditCardTypeFromCreditCardNumber, getPaymentItemCardTypeShort, getCardType, validationString } from "../../utils";

// import { numberOnly, getMonthList, getYearList } from "../../utils";
// START: IMPORT - update CreditCardDetails model
import {
  validateCardHolderName,
  validateCreditCardNumber,
  validateCreditCardExpiryDate,
  validateSecurityCode,
  validatBankDetailsErrorMapper,
} from "../../models/handleCreditCardValidationErrors";
import { connect } from "react-redux";
import {
  onCreditCardNumberChange,
  onCardHolderNameChange,
  onCreditCardExpiryDateChange,
  onSecurityCodeChange,
  setValidationErrors,
  resetValidationErrors,
  tokenizeAndPropagateFormValues,
  validateMultiOrderPaymentAction,
  OmnitureOnPaymentSelect,
  cancelPreauthAction,
  OmnitureOnCancelationCompleted,
  OmnitureOnCancelationPartiallyCompleted,
  OmnitureOnCancelationFailed,
  OmnitureOnCancelationPartiallyFailed,
  apiConfirmationStatus,
  OmnitureOnInteracFailure
  // interacBankInfoFailure
} from "../../store/Actions";
import { State } from "../../store";
import { ValidationErrors } from "../../models/Error";
import { PaymentSummary } from "../PaymentSummary/PaymentSummary";
import { CancelPreAuthorizedPayments } from "./CancelPreAuthorizedPayments";
import { getPaymentSelectOmniture } from "../../utils/Omniture";
import { IRequestStatus } from "../../models/App";
// END: IMPORT - update CreditCardDetails model
interface PaymentMethodProps {
  intl: any;
  isHeadingStepActive: "active" | "inactive" | "complete";
  paymentItem: PaymentItem[];
  creditcardDetails?: CCDetails;
  onCreditCardNumberChange: any;
  validatBankDetails: any;
  onCardHolderNameChange: any;
  onCreditCardExpiryDateChange: any;
  onSecurityCodeChange: any;
  resetValidationErrors: any;
  validationErrors?: ValidationErrors;
  isSingleClickEnableForPAD: boolean;
  isSingleClickEnableForPACC: boolean;
  onCurrentSteps: (step: any) => void;
  setHeadingSteps: (isActive: boolean) => void;
  setInputValue: (value: CreditCardInputValue) => void;
  inputValue: CreditCardInputValue;
  setInputBankValue: (value: InputBankAccountDetail) => void;
  inputBankValue: InputBankAccountDetail;
  setIsBankSelected: (IsbankSelected: boolean) => void;
  setCurrentSection: (section: CurrentSection) => void;
  currentSection: CurrentSection;
  validateFormOrder: Function;
  checkedBillItems: PaymentItem[];
  tokenizeAndPropagateFormValues: Function;
  bankList: SelectListItem[];
  cardTokenizationSuccess: string;
  redirectUrl: IGetRedirectUrl;
  interacBankInfo: IBankInfoRes;
  accountInputValues: AccountInputValues[];
  interactBankFailureInfo: IBankInfoFailure;
  creditCardAutopayOffers: SubscriberOffersWithBan[];
  debitCardAutopayOffers: SubscriberOffersWithBan[];
  removedSubscriberOffers: SubscriberOffersWithBan[];
  province: string;
  language: "en" | "fr";
  setOmnitureOnPaymentSelect: Function;
  isBankSelected: boolean;
  paymentHeadingStepState: string;
  managePreauth?: string;
  setCancelPreauthSectionClicked: Function;
  cancelPreauthSectionClicked: boolean;
  cancelPreauthPaymentsAction: Function;
  cancelPreauthStatus: IRequestStatus;
  setIsModalOpen: Function;
  setApiSatusIsFailed: Function;
  setOmnitureOnCancelationCompleted: Function;
  IsAutopayCreditEnabled: boolean;
  IsInteracEnabled: boolean;
  InteracCode: string;
  setSomeCancellationFailed: Function;
  cancelPreauthPayments: ICancelledItem[];
  setBansCancellationFailed: Function;
  setOmnitureOnCancelationPartiallyCompleted: Function;
  setOmnitureOnCancelationFailed: Function;
  setOmnitureOnCancelationPartiallyFailed: Function;
  setCancellationPaymentFailStatus: Function;
  setOmnitureOnInteracFailure: Function;
}


// Creditcard Fuctions UP HERE

export interface FormFieldsCreditCardPaymentProps {
  errorExpiryDate?: boolean;
  intl: any;
}

// const bankListInterac = [
//     { bankname: "BMO" },
//     { bankname: "CIBC" },
//     { bankname: "Desjardins" },
//     { bankname: "RBC" },
//     { bankname: "Scotiabank" },
//     { bankname: "TD" },
//   ]

const PaymentMethodComponent = ({ intl,
  isHeadingStepActive,
  paymentItem,
  creditcardDetails,
  onCreditCardNumberChange,
  onCardHolderNameChange,
  onCreditCardExpiryDateChange,
  onSecurityCodeChange,
  resetValidationErrors,
  validationErrors,
  isSingleClickEnableForPAD,
  isSingleClickEnableForPACC,
  onCurrentSteps,
  setHeadingSteps,
  setCurrentSection,
  currentSection,
  setInputValue,
  inputValue,
  setInputBankValue,
  inputBankValue,
  setIsBankSelected,
  validateFormOrder,
  checkedBillItems,
  tokenizeAndPropagateFormValues,
  bankList,
  validatBankDetails,
  cardTokenizationSuccess,
  redirectUrl,
  interacBankInfo,
  accountInputValues,
  interactBankFailureInfo,
  creditCardAutopayOffers,
  debitCardAutopayOffers,
  removedSubscriberOffers,
  province,
  language,
  setOmnitureOnPaymentSelect,
  managePreauth,
  setCancelPreauthSectionClicked,
  cancelPreauthSectionClicked,
  cancelPreauthPaymentsAction,
  cancelPreauthStatus,
  setIsModalOpen,
  setApiSatusIsFailed,
  setOmnitureOnCancelationCompleted,
  IsAutopayCreditEnabled,
  IsInteracEnabled,
  InteracCode,
  setSomeCancellationFailed,
  cancelPreauthPayments,
  setBansCancellationFailed,
  setOmnitureOnCancelationPartiallyCompleted,
  setOmnitureOnCancelationFailed,
  setOmnitureOnCancelationPartiallyFailed,
  setCancellationPaymentFailStatus,
  setOmnitureOnInteracFailure
}: PaymentMethodProps) => {
  const [localStatus, setLocalStatus] = React.useState(cancelPreauthStatus);
  const [localPayments, setLocalPayments] = React.useState(cancelPreauthPayments);

  const [cardNumber, setCardNumber] = React.useState(creditcardDetails?.CreditCardNumber);
  const [cardType, setCardType] = React.useState("default");
  const [CVV, setCVV] = React.useState('');
  const [errorCardNumber, setErrorCardNumber] = React.useState(false);
  const [errorCardName, setErrorCardName] = React.useState(false);
  const [errorSecurityCode, setErrorSecurityCode] = React.useState(false);
  const [errorExpiryDate, setErrorExpiryDate] = React.useState(false);

  const [errorBankName, setErrorBankName] = React.useState(false);
  const [errorBankTransit, setErrorBankTransit] = React.useState(false);
  const [errorBankAccountNumber, setErrorBankAccountNumber] = React.useState(false);
  const [errorBankAccountHolderName, setErrorBankAccountHolderName] = React.useState(false);

  const [isBankChecked, setisBankChecked] = React.useState(false);
  const [getDefaultPayment, setLoadDefaultPayment] = React.useState(intl.formatMessage({ id: "BANK_ACCOUNT_LABEL" }));
  const [selectedPaymentOption, setSelectedPaymentOption] = React.useState("");
  const [onFormSubmit, setOnFormSubmit] = React.useState(false);
  const [isInteracSelected, setInteracSelected] = React.useState(false);
  const [isInteractBankFail, setInteractBankFail] = React.useState(true);
  const [isBankManualEnterDetails, setBankManualEnterDetails] = React.useState(false);
  const [paymentDetails, setPaymentDetails] = React.useState({
    SelectedPaymentMethod: "",
    CardholderName: "",
    CreditCardToken: "",
    CreditCardType: "",
    ExpiryYear: "",
    ExpiryMonth: "",
    SecurityCode: ""
  });
  const [bankPaymentDetails, setBankPaymentDetails] = React.useState({
    SelectedPaymentMethod: "",
    BankName: "",
    HolderName: "",
    TransitCode: "",
    AccountNumber: "",
    BankCode: ""
  });
  const bankListCMS = intl.formatMessage({ id: "InteracSupportedFinancialInstitutions" });
  const bankListInterac = bankListCMS && bankListCMS.split(",");
  const [isValidateError, setOmnitureError] = React.useState(false);

  const paymentLabel = managePreauth !== null ? intl.formatMessage({ id: "UPDATE_BANK_ACCOUNT_LABEL" })
    : intl.formatMessage({ id: "BANK_ACCOUNT_LABEL" });

  const [paymentMethodSelected, setPaymentMethodSelected] = React.useState(paymentLabel);
  const [isBankPaymentSelected, setIsBankPaymentSelected] = React.useState(true);
  // const [isCancelModelOpen, setIsCancelModelOpen] = React.useState(false);
  const radioRef = React.useRef<HTMLInputElement>(null) as React.RefObject<HTMLInputElement>;
  const radioCardRef = {
    interac: React.useRef<HTMLInputElement>(null),
    manualDetails: React.useRef<HTMLInputElement>(null),
  };
  const inputRefs = {
    inputCreditCardNumber: React.useRef<HTMLInputElement>(null!),
    inputCreditCardHolderName: React.useRef<HTMLInputElement>(null!),
    inputCreditCardSecurityCode: React.useRef<HTMLInputElement>(null!),
    inputCreditCardExpiryMonth: React.useRef<HTMLSelectElement>(null!),
    inputCreditCardExpiryYear: React.useRef<HTMLSelectElement>(null!),
    inputBankName: React.useRef<HTMLSelectElement>(null!),
    inputBankAccountHolder: React.useRef<HTMLInputElement>(null!),
    inputTransitNumber: React.useRef<HTMLInputElement>(null!),
    inputBankAccountNumber: React.useRef<HTMLInputElement>(null!),
  };

  const getSubmitForm = () => ({
    cardHolderName: inputRefs.inputCreditCardHolderName.current?.value ? inputRefs.inputCreditCardHolderName.current?.value : CreditCardDetailsAccount?.CardholderName || "",
    creditCardNumber: inputRefs.inputCreditCardNumber.current?.value ? inputRefs.inputCreditCardNumber.current?.value : CreditCardDetailsAccount?.CreditCardNumber || "",
    creditCardToken: inputRefs.inputCreditCardNumber.current?.value ? inputRefs.inputCreditCardNumber.current?.value : CreditCardDetailsAccount?.CreditCardNumber || "",
    expirationMonth: inputRefs.inputCreditCardExpiryMonth.current?.value ? inputRefs.inputCreditCardExpiryMonth.current?.value : CreditCardDetailsAccount?.ExpireMonth || "",
    expirationYear: inputRefs.inputCreditCardExpiryYear.current?.value ? inputRefs.inputCreditCardExpiryYear.current?.value : CreditCardDetailsAccount?.ExpireYear || "",
    securityCode: inputRefs.inputCreditCardSecurityCode.current?.value ? inputRefs.inputCreditCardSecurityCode.current?.value : CreditCardDetailsAccount?.SecurityCode || "",
    cardType: inputRefs.inputCreditCardHolderName.current?.value ? GetCreditCardTypeFromCreditCardNumber(inputRefs.inputCreditCardHolderName.current?.value) : (CreditCardDetailsAccount?.CreditCardType ? getPaymentItemCardType(CreditCardDetailsAccount.CreditCardType) : "") || "",
  });

  const handleBankRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedPaymentOption(event.target.value);
  };
  React.useEffect(() => {
    if (selectedPaymentOption === intl.formatMessage({ id: "NEW_CREDIT_ACCOUNT_LABEL" }) || selectedPaymentOption === intl.formatMessage({ id: "BANK_NEW_BANK_ACCOUNT_LABEL" })) {
      setisBankChecked(true);
    }
    else {
      setisBankChecked(false);

    }
  }, [selectedPaymentOption]);

  const handleMaskCVV = (e: any) => {
    setCVV(e.target.value);
  };

  const cardIcons = {
    VISA: intl.formatMessage({ id: "VISA_CC_PNG" }),
    MASTERCARD: intl.formatMessage({ id: "MASTER_CC_PNG" }),
    AMEX: intl.formatMessage({ id: "AMEX_CC_PNG" })
  };
  const handleOnEditClick = () => {
    setCurrentSection(CurrentSection.PaymentMethod);
  };

  const setErrorStateToDefault = () => {
    setErrorCardNumber(false);
    setErrorCardName(false);
    setErrorExpiryDate(false);
    setErrorSecurityCode(false);

    setErrorBankAccountHolderName(false);
    setErrorBankAccountNumber(false);
    setErrorBankName(false);
    setErrorBankTransit(false);

    //         setErrorerrorBankInfo({});
  };

  const DebitPaymentMethod = intl.formatMessage({ id: "PAYMENT_METHOD_DEBIT" });

  const handleSubmit = (e: any) => {
    e.preventDefault();
    setErrorStateToDefault();

    if (!isBankPaymentSelected) {
      setPaymentDetails({
        SelectedPaymentMethod: "CreditCard",
        CardholderName: inputRefs.inputCreditCardHolderName.current?.value ? inputRefs.inputCreditCardHolderName.current?.value : CreditCardDetailsAccount?.CardholderName || "",
        CreditCardToken: inputRefs.inputCreditCardNumber.current?.value ? inputRefs.inputCreditCardNumber.current?.value : CreditCardDetailsAccount?.CreditCardNumber || "",
        CreditCardType: inputRefs.inputCreditCardNumber.current?.value ? GetCreditCardTypeFromCreditCardNumber(inputRefs.inputCreditCardNumber.current?.value) : (CreditCardDetailsAccount?.CreditCardType ? getPaymentItemCardTypeShort(CreditCardDetailsAccount.CreditCardType) : "") || "",
        ExpiryYear: inputRefs.inputCreditCardExpiryYear.current?.value ? inputRefs.inputCreditCardExpiryYear.current?.value : CreditCardDetailsAccount?.ExpireYear || "",
        ExpiryMonth: inputRefs.inputCreditCardExpiryMonth.current?.value ? inputRefs.inputCreditCardExpiryMonth.current?.value : CreditCardDetailsAccount?.ExpireMonth || "",
        SecurityCode: inputRefs.inputCreditCardSecurityCode.current?.value ? inputRefs.inputCreditCardSecurityCode.current?.value : CreditCardDetailsAccount?.SecurityCode || "",
      });
      onCreditCardNumberChange(inputRefs.inputCreditCardNumber.current?.value);
      onCardHolderNameChange(inputRefs.inputCreditCardHolderName.current?.value);
      onCreditCardExpiryDateChange(inputRefs.inputCreditCardExpiryMonth.current?.value, inputRefs.inputCreditCardExpiryYear.current?.value);
      onSecurityCodeChange(inputRefs.inputCreditCardSecurityCode.current?.value);
    }
    else {
      setBankPaymentDetails({
        SelectedPaymentMethod: DebitPaymentMethod,
        BankName: bankList.filter(x => x.Value === inputRefs.inputBankName.current?.value)[0]?.Text,
        HolderName: inputRefs.inputBankAccountHolder.current?.value ? inputRefs.inputBankAccountHolder.current?.value : BankDetailsAccount?.CardHolder || "",
        TransitCode: inputRefs.inputTransitNumber.current?.value ? inputRefs.inputTransitNumber.current?.value : BankDetailsAccount?.TransitCode || "",
        AccountNumber: inputRefs.inputBankAccountNumber.current?.value ? inputRefs.inputBankAccountNumber.current?.value : BankDetailsAccount?.AccountNumber || "",
        BankCode: inputRefs.inputBankName.current?.value ? inputRefs.inputBankName.current?.value.slice(-3) : "",

      });
      setInputBankValue({
        PaymentMethod: DebitPaymentMethod,
        AccountHolder: inputRefs.inputBankAccountHolder.current?.value ? inputRefs.inputBankAccountHolder.current?.value : BankDetailsAccount?.CardHolder || "",
        BankName: inputRefs.inputBankName.current?.value ? inputRefs.inputBankName.current?.value : BankDetailsAccount?.BankName || "",
        TransitNumber: inputRefs.inputTransitNumber.current?.value ? inputRefs.inputTransitNumber.current?.value : BankDetailsAccount?.TransitCode || "",
        AccountNumber: inputRefs.inputBankAccountNumber.current?.value ? inputRefs.inputBankAccountNumber.current?.value : BankDetailsAccount?.AccountNumber || "",
      });
      validatBankDetails(validateBankForm());
    }
    setInteractBankFail(false);
    setOnFormSubmit(true);
  };

  React.useEffect(() => {
    if (onFormSubmit) {
      validationErrors?.errors.map(error => {
        switch (error.field) {
          case FieldType.CardNumber:
            setErrorCardNumber(true);
            break;
          case FieldType.CardHolderName:
            setErrorCardName(true);
            break;
          case FieldType.ExpirationDate:
            setErrorExpiryDate(true);
            break;
          case FieldType.SecurityCode:
            setErrorSecurityCode(true);
            break;
          case FieldType.BankAccountHolderName:
            setErrorBankAccountHolderName(true);
            break;
          case FieldType.BankName:
            setErrorBankName(true);
            break;
          case FieldType.BankTransitCode:
            setErrorBankTransit(true);
            break;
          case FieldType.BankAccountNumber:
            // setErrorerrorBankInfo(validationErrors?.errors);
            setErrorBankAccountNumber(true);
            break;
          default:
            break;
        }
      });
      const hasErrors = validationErrors?.errors?.length ? validationErrors.errors.length > 0 : false;
      if (hasErrors) {
        setOmnitureError(true);
      }
      if (!hasErrors) {
        setInputValue({
          cardNumber: inputRefs.inputCreditCardNumber.current?.value || "",
          cardType: getCardType(inputRefs.inputCreditCardNumber.current?.value || ""),
          cardName: inputRefs.inputCreditCardHolderName.current?.value || "",
          expiryDate: `${inputRefs.inputCreditCardExpiryMonth.current?.value || ""}/${inputRefs.inputCreditCardExpiryYear.current?.value || ""}`
        });
        shotNextTermsandCondition();

      } else if (!hasErrors && isPreauth && (!isBankChecked && hasCreditCardDetails)) {
        // Proceed To terms and condition without the error validation if Creditcard Preauth and not selected the new Card option
        if (!hasErrors) {
          shotNextTermsandCondition();
        }
      }
      if (!hasErrors) {
        const form = getSubmitForm();
        if (bankitems.length === 1) {
          const item = bankitems[0];
          if (isBankPaymentSelected) {
            validateFormOrder(item.Ban, item.AccountType === PaymentItemAccountType.OneBill, bankPaymentDetails, accountInputValues, isBankPaymentSelected, item.subscriberId);
          }
          else {
            tokenizeAndPropagateFormValues(form, item.Ban, item.AccountType === PaymentItemAccountType.OneBill, paymentDetails, isBankPaymentSelected, item.subscriberId);
          }
        }
        if (bankitems.length > 1 && checkedBillItems && checkedBillItems.length > 0) {
          if (isBankPaymentSelected) {
            validateFormOrder(checkedBillItems[0].Ban, checkedBillItems[0].AccountType === PaymentItemAccountType.OneBill, bankPaymentDetails, accountInputValues, isBankPaymentSelected, checkedBillItems[0].subscriberId);
          }
          else {
            tokenizeAndPropagateFormValues(form, checkedBillItems[0].Ban, checkedBillItems[0].AccountType === PaymentItemAccountType.OneBill, paymentDetails, isBankPaymentSelected, checkedBillItems[0].subscriberId);
          }
        }
      }
      const resetError = new ValidationErrors();
      resetValidationErrors(resetError);
      setOnFormSubmit(false);
    }


  }, [onFormSubmit]);

  React.useEffect(() => {
    if (cardTokenizationSuccess) {
      if (checkedBillItems && checkedBillItems.length > 0) {
        validateFormOrder(checkedBillItems[0].Ban, checkedBillItems[0].AccountType === PaymentItemAccountType.OneBill, paymentDetails, accountInputValues, isBankPaymentSelected, checkedBillItems[0].subscriberId);
      }
    }
  }, [cardTokenizationSuccess]);

  const validateBankForm = () => {

    const validationResponse = {
      isValid: true,
      validationForm: {
        bankNameError: {
          isEmpty: false,
          isInvalid: false
        },
        bankAccountHolderError: {
          isEmpty: false,
          isInvalid: false
        },
        transitNumberError: {
          isEmpty: false,
          isInvalid: false
        },
        bankAccountNumberError: {
          isEmpty: false,
          isInvalid: false
        },
      }
    };
    if (!inputRefs.inputBankName.current?.value) {
      validationResponse.isValid = false;
      validationResponse.validationForm.bankNameError.isEmpty = true;
    }
    if (!inputRefs.inputBankAccountHolder.current?.value) {
      validationResponse.isValid = false;
      validationResponse.validationForm.bankAccountHolderError.isEmpty = true;
    } else if (inputRefs.inputBankAccountHolder.current?.value) {
      const InputNametest = validationString.test(inputRefs.inputBankAccountHolder.current?.value.trim())
        && inputRefs.inputBankAccountHolder.current?.value.trim().length <= 70;// -system will accept a maximum of 70 characters
      if (!InputNametest) {
        validationResponse.isValid = false;
        validationResponse.validationForm.bankAccountHolderError.isInvalid = true;
      }
    }
    if (!inputRefs.inputTransitNumber.current?.value) {
      validationResponse.isValid = false;
      validationResponse.validationForm.transitNumberError.isEmpty = true;
    } else if (inputRefs.inputTransitNumber.current?.value.length < 5) {
      validationResponse.isValid = false;
      validationResponse.validationForm.transitNumberError.isInvalid = true;
    }

    if (!inputRefs.inputBankAccountNumber.current?.value) {
      validationResponse.isValid = false;
      validationResponse.validationForm.bankAccountNumberError.isEmpty = true;
    } else if (inputRefs.inputBankAccountNumber.current?.value.length < 7) {
      validationResponse.isValid = false;
      validationResponse.validationForm.bankAccountNumberError.isInvalid = true;
    }
    return validationResponse;
  };
  const shotNextTermsandCondition = () => {
    // HIDE SHOW COMPONENT
    if (isSingleClickEnableForPACC || isSingleClickEnableForPAD) {
      setCurrentSection(CurrentSection.CurrentBalance);
    } else {
      setCurrentSection(CurrentSection.TermsAndCondition);
      setHeadingSteps(false); // Hide and Show Payment method
    }
  };

  const isPreauth: boolean = paymentItem?.some(paymentItem => paymentItem.IsOnPreauthorizedPayments ?? false);
  const bankitems = paymentItem;
  // const bankName = paymentItem.find(paymentItem => paymentItem.BankAccountDetails);
  const hasBankAccountDetails = paymentItem.find(paymentItem => paymentItem.BankAccountDetails);
  const hasCreditCardDetails = paymentItem.find(paymentItem => paymentItem.CreditCardDetails);
  const hasInteractFail = interactBankFailureInfo && interactBankFailureInfo.data ? interactBankFailureInfo.data : null;
  const filteredItems = paymentItem.filter(
    item => item.IsOnPreauthorizedPayments === true && item.CreditCardDetails
  );
  const CreditCardDetailsAccount = filteredItems.length > 0 ? filteredItems[0].CreditCardDetails : null;

  const bankFilteredItems = paymentItem.filter(
    item => item.IsOnPreauthorizedPayments === true && item.BankAccountDetails
  );
  const BankDetailsAccount = bankFilteredItems.length > 0 ? bankFilteredItems[0].BankAccountDetails : null;

  const handleCreditCardChange = (e: any) => {
    setCardNumber(e.target.value);
    const detectedCardType = getCardType(e.target.value);
    setCardType(detectedCardType);
  };

  // if (isHeadingStepActive === "active"){
  //     let pageTitle = intl.formatMessage({id: "SELECT_PAYMENT_METHOD_HEADING"});
  //     onCurrentSteps(pageTitle);
  // }
  // else if (showConfirmation){ 
  //     let pageTitle =  intl.formatMessage({ id: "CONFIRMATION_HEADING" })
  //     onCurrentSteps(pageTitle);
  // }

  const handleInteracSubmit = () => {
    // functionality will be changed on API Integration
    // setInteracSelected(true);

    window.location.href = redirectUrl.externalRedirectUrl;
  };

  const handleBankRadioManualDetailsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (radioCardRef.manualDetails?.current?.checked) {
      setBankManualEnterDetails(true);
    } else {
      setBankManualEnterDetails(false);
    }
  };
  const handlePaymentMethodRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const cancelOptioLabel = intl.formatMessage({ id: "CANCEL_PAYMENT_LABEL" });

    setPaymentMethodSelected(event.target.value);
    setLoadDefaultPayment(event.target.value);
    if (event.target.value !== cancelOptioLabel) {
      setCancelPreauthSectionClicked(false);
    }
  };
  const CreditpaymentLabel = managePreauth !== null ? intl.formatMessage({ id: "UPDATE_CREDITCARD_PAYMENT" }) : intl.formatMessage({ id: "CREDIT_CARD_LABEL" });


  React.useEffect(() => {
    if (paymentMethodSelected === paymentLabel) {
      setIsBankPaymentSelected(true);
      setIsBankSelected(true);

    } else if (paymentMethodSelected === CreditpaymentLabel) {
      setIsBankPaymentSelected(false);
      setIsBankSelected(false);

    }
  }, [paymentMethodSelected]);

  React.useEffect(() => {
    if (interacBankInfo != null && interacBankInfo.status === "SUCCESS") {
      setInteracSelected(true);
    }
    else {
      setInteracSelected(false);
    }
  }, [interacBankInfo]);


  // const checkedBanOffers = () => {
  //     var filteredOffer: any = [];
  //    debitCardAutopayOffers && debitCardAutopayOffers?.map((item) =>{
  //          checkedBillItems && checkedBillItems.map((billItem)=>{
  //           if(item.Ban == billItem.BillName){
  //             filteredOffer.push(item);
  //           }
  //          });
  //      });

  //     return filteredOffer;   
  //   }
  // const getTotalOffers = () => {

  //     return bankitems && bankitems.length > 1 ? checkedBanOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {

  //      const offersCount = ban.AutopayEligibleSubscribers.reduce(
  //        (count, subscriber) => count + subscriber.autopayOffers.length,
  //        0
  //      );
  //      return total + offersCount;
  //    }, 0): debitCardAutopayOffers && debitCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {

  //      const offersCount = ban.AutopayEligibleSubscribers.reduce(
  //        (count, subscriber) => count + subscriber.autopayOffers.length,
  //        0
  //      );
  //      return total + offersCount;
  //    }, 0);
  //  };
  React.useEffect(() => {
    if (currentSection === CurrentSection.PaymentMethod && checkedBillItems.length > 0) {

      if (paymentMethodSelected === paymentLabel && !cancelPreauthSectionClicked && !(hasInteractFail && isInteractBankFail)) {

        if (InteracCode === "") {
          setOmnitureOnPaymentSelect(getPaymentSelectOmniture(false, true, checkedBillItems, false, InteracCode));
        }

      }
      else if (paymentMethodSelected === CreditpaymentLabel && !cancelPreauthSectionClicked) {
        setOmnitureOnPaymentSelect(getPaymentSelectOmniture(false, false, checkedBillItems, false, InteracCode));
      }

    }
  }, [currentSection, paymentMethodSelected]);

  React.useEffect(() => {
    if (InteracCode && InteracCode !== "" && checkedBillItems.length > 0 && !(hasInteractFail && isInteractBankFail)) {
      if (paymentMethodSelected === paymentLabel && currentSection === CurrentSection.PaymentMethod) {
        setOmnitureOnPaymentSelect(getPaymentSelectOmniture(false, true, checkedBillItems, false, InteracCode));
      }
    }
    else if (isInteractBankFail && hasInteractFail && checkedBillItems.length > 0) {
      if (paymentMethodSelected === paymentLabel && currentSection === CurrentSection.PaymentMethod) {
        setOmnitureOnInteracFailure(getPaymentSelectOmniture(true, isBankPaymentSelected, checkedBillItems, false, ""));
      }
    }

  }, [InteracCode, currentSection, checkedBillItems]);

  React.useEffect(() => {
    if (currentSection === CurrentSection.PaymentMethod) {
      if (isValidateError) {
        setOmnitureOnPaymentSelect(
          getPaymentSelectOmniture(true, isBankPaymentSelected, checkedBillItems, false, InteracCode)
        );
        setOmnitureError(false);
      }
    }
  }, [isValidateError]);

  const isManage = managePreauth && managePreauth !== null;

  const handleCancelClick = () => {

    setCancelPreauthSectionClicked(false);

    if (hasBankAccountDetails) {
      setLoadDefaultPayment("Bank account");

    }
    else if (hasCreditCardDetails) {
      setLoadDefaultPayment("Credit card");

    }
  };

  const onSubmitClick = () => {
    const checkedBansOnPreauth = checkedBillItems.filter(item => item.IsOnPreauthorizedPayments)?.map(ele => ele.Ban);
    type accntType = 'PAD' | 'PACC';
    // Add banid here and cancel success status
    const mockbans: Record<accntType, any[]> = {
      PAD: [
        { banId: '*********', success: false },
        { banId: '*********', success: false },
        { banId: '*********', success: true }
      ], PACC: [
        { banId: '*********', success: false },
        { banId: '*********', success: false },
        { banId: '*********', success: true }
      ]
    };
    if (checkedBansOnPreauth && checkedBansOnPreauth.length > 0) {
      let matchedAccount: accntType | null = null;

      for (const account of Object.keys(mockbans) as accntType[]) {
        const mockBanIds = mockbans[account].map(b => b.banId);

        const containsSpecificBans = checkedBansOnPreauth.some(banId =>
          mockBanIds.includes(banId)
        );

        if (containsSpecificBans) {
          matchedAccount = account;
          break; // Stop at the first match
        }
      }
      if (matchedAccount) {
        const filteredPayments = mockbans[matchedAccount].filter(item =>
          checkedBansOnPreauth.includes(item.banId)
        );
        const allFailed = filteredPayments.every(item => item.success === false);
        // Check fail
        if (allFailed) {
          setLocalStatus(IRequestStatus.FAILED);
        } else {
          setLocalStatus(IRequestStatus.COMPLETED);
        }
        setLocalPayments(filteredPayments);

      } else {
        cancelPreauthPaymentsAction(checkedBansOnPreauth);
      }


    }
  };
  // CANCEL PAYMENT TESTING MOCKDATA START
  React.useEffect(() => {
    if (localStatus === IRequestStatus.COMPLETED) {
      if (localPayments && Object.values(localPayments).length > 0) {
        const cancelledValues = Object.values(localPayments);
        if (cancelledValues && cancelledValues.length > 0) {
          if (cancelledValues.every(value => value.success === true)) {
            setIsModalOpen(true);
            setOmnitureOnCancelationCompleted();
          }
          else {
            const cancelledFailedBans = cancelledValues.filter(val => !val.success).map(v => v.banId);
            if (cancelledFailedBans && cancelledFailedBans.length > 0) {
              const cancelledFailedPaymentItems = bankitems.filter(item => cancelledFailedBans.includes(item.Ban));
              setSomeCancellationFailed(true);
              setBansCancellationFailed(cancelledFailedPaymentItems);
              setOmnitureOnCancelationPartiallyFailed();
              setOmnitureOnCancelationPartiallyCompleted();
              setCancellationPaymentFailStatus("FAILED");
            }
          }
        }
      }
    }

    else if (localStatus === IRequestStatus.FAILED) {
      setApiSatusIsFailed(true);
      setOmnitureOnCancelationFailed();

    }
  }, [localStatus, localPayments]);

  // CANCEL PAYMENT TESTING MOCKDATA END
  React.useEffect(() => {
    if (cancelPreauthStatus === IRequestStatus.COMPLETED) {
      if (cancelPreauthPayments && Object.values(cancelPreauthPayments).length > 0) {
        const cancelledValues = Object.values(cancelPreauthPayments);
        if (cancelledValues && cancelledValues.length > 0) {
          if (cancelledValues.every(value => value.success === true)) {
            setIsModalOpen(true);
            setOmnitureOnCancelationCompleted();
          }
          else {
            const cancelledFailedBans = cancelledValues.filter(val => !val.success).map(v => v.banId);
            if (cancelledFailedBans && cancelledFailedBans.length > 0) {
              const cancelledFailedPaymentItems = bankitems.filter(item => cancelledFailedBans.includes(item.Ban));
              setSomeCancellationFailed(true);
              setBansCancellationFailed(cancelledFailedPaymentItems);
              setOmnitureOnCancelationPartiallyFailed();
              setOmnitureOnCancelationPartiallyCompleted();
              setCancellationPaymentFailStatus("FAILED");
            }
          }
        }
      }
    }

    else if (cancelPreauthStatus === IRequestStatus.FAILED) {
      setApiSatusIsFailed(true);
      setOmnitureOnCancelationFailed();

    }
  }, [cancelPreauthStatus, cancelPreauthPayments]);


  const paymentHeading = managePreauth ? intl.formatMessage({ id: "CHANGE_PAYMENT" }) : intl.formatMessage({ id: "SELECT_PAYMENT_METHOD_HEADING" });

  return (
    <>

      <div className={["payment-border-b payment-border-gray-4", currentSection > CurrentSection.PaymentMethod ? "payment-hidden" : ""].join(" ").trim()}>
        <div>
          <HeadingStep
            disableSrOnlyText={true}
            tabIndex={-1}
            status={isHeadingStepActive}
            subtitle=""
            hideSubtitle
            variant="leftAlignNoStep"
            title={paymentHeading}
            aria-hidden={currentSection === CurrentSection.PaymentMethod ? "true" : undefined}
            id={currentSection === CurrentSection.PaymentMethod ? "payment-manage-heading" : undefined}
          />
        </div>

        {((errorCardNumber || errorCardName || errorExpiryDate || errorSecurityCode || errorBankAccountHolderName || errorBankAccountNumber || errorBankName || errorBankTransit) && currentSection !== CurrentSection.SelectBills)
          ?
          <div className="payment-pt-30">
            <AlertCreditCardErrorFormList
              isErrorCardNumber={errorCardNumber}
              isErrorCardName={errorCardName}
              isErrorSecurityCode={errorSecurityCode}
              isErrorExpiryDate={errorExpiryDate}
              isErrorBankAccountHolderName={errorBankAccountHolderName}
              isErrorBankAccountNumber={errorBankAccountNumber}
              isErrorBankName={errorBankName}
              iserrorBankTransit={errorBankTransit}
              inputRefs={inputRefs}
            />
          </div>
          : <></>
        }
        {isInteractBankFail && hasInteractFail && <div className={`${isHeadingStepActive === "active" ? "" : "payment-hidden"} payment-pt-30`}><AlertCreditCardErrorInterac interact={hasInteractFail} /></div>}
        <div role="radiogroup">
          <div className={`${isHeadingStepActive === "active" ? "" : "payment-hidden"} payment-pt-30`}>
            <BankPayment
              Checked={(getDefaultPayment?.trim().toLowerCase?.() ?? "") === intl.formatMessage({ id: "BANK_ACCOUNT_LABEL" }).trim().toLowerCase()}
              isInteracSelected={isInteracSelected}
              checkedBillItems={checkedBillItems}
              radioCardRef={radioCardRef}
              handleBankRadioManualDetailsChange={handleBankRadioManualDetailsChange}
              isBankManualEnterDetails={isBankManualEnterDetails}
              isPreauth={isPreauth}
              hasBankAccountDetails={hasBankAccountDetails}
              bankitems={bankitems}
              handleBankRadioChange={handleBankRadioChange}
              bankListInterac={bankListInterac}
              handleInteracSubmit={handleInteracSubmit}
              isBankChecked={isBankChecked}
              inputRefs={inputRefs}
              errorBankName={errorBankName}
              errorBankTransit={errorBankTransit}
              errorBankAccountNumber={errorBankAccountNumber}
              errorBankAccountHolderName={errorBankAccountHolderName}
              radioRef={radioRef}
              bankList={bankList}
              onChange={handlePaymentMethodRadioChange}
              creditCardAutopayOffers={creditCardAutopayOffers}
              debitCardAutopayOffers={debitCardAutopayOffers}
              language={language}
              managePreauth={managePreauth}
              IsAutopayCreditEnabled={IsAutopayCreditEnabled}
              IsInteracEnabled={IsInteracEnabled}
            >
            </BankPayment>
            <CreditCardPayment
              isPreauth={isPreauth}
              hasCreditCardDetails={hasCreditCardDetails}
              bankitems={bankitems}
              radioRef={radioRef}
              handleBankRadioChange={handleBankRadioChange}
              isBankChecked={isBankChecked}
              cardNumber={cardNumber}
              handleCreditCardChange={handleCreditCardChange}
              inputRefs={inputRefs}
              cardIcons={cardIcons}
              cardType={cardType}
              errorCardNumber={errorCardNumber}
              errorCardName={errorCardName}
              errorExpiryDate={errorExpiryDate}
              errorSecurityCode={errorSecurityCode}
              handleMaskCVV={handleMaskCVV}
              CVV={CVV}
              onChange={handlePaymentMethodRadioChange}
              checkedBillItems={checkedBillItems}
              creditCardAutopayOffers={creditCardAutopayOffers}
              debitCardAutopayOffers={debitCardAutopayOffers}
              language={language}
              managePreauth={managePreauth}
              IsAutopayCreditEnabled={IsAutopayCreditEnabled}
              Checked={(getDefaultPayment?.trim().toLowerCase?.() ?? "") === intl.formatMessage({ id: "CREDIT_CARD_LABEL" }).trim().toLowerCase()}
            >
            </CreditCardPayment>
            {isManage &&
              <CancelPreAuthorizedPayments
                Checked={(getDefaultPayment?.trim().toLowerCase?.() ?? "") === intl.formatMessage({ id: "CANCEL_PAYMENT_LABEL" }).trim().toLowerCase()}
                paymentItems={bankitems}
                checkedBillItems={checkedBillItems}
                managePreauth={managePreauth}
                setCancelPreauthSectionClicked={setCancelPreauthSectionClicked}
                cancelPreauthSectionClicked={cancelPreauthSectionClicked}
                removedSubscriberOffers={removedSubscriberOffers}
                province={province}
                IsAutopayCreditEnabled={IsAutopayCreditEnabled}
                setOmnitureOnPaymentSelect={setOmnitureOnPaymentSelect}
                language={language}
                onChange={handlePaymentMethodRadioChange}
              />
            }

            {cancelPreauthSectionClicked ?
              <div className="brui-inline-flex brui-flex-wrap brui-items-center payment-pb-45">
                <div className="payment-pr-30 payment-pt-30">
                  <Button
                    variant="primary"
                    size="regular"
                    onClick={onSubmitClick}
                  >
                    {intl.formatMessage({ id: "CTA_CONFIRM" })}
                  </Button>
                </div>
                <div className="payment-pt-30">
                  <Button
                    variant="textBlue"
                    size="regular"
                    className="!brui-text-14 brui-leading-18"
                    onClick={handleCancelClick}
                  >
                    {intl.formatMessage({ id: "CTA_CANCEL" })}
                  </Button>
                </div>
              </div>
              :
              <div className="payment-pt-15 payment-pb-45 sm:payment-pb-60">
                <Button variant="primary" onClick={handleSubmit} disabled={false}>{intl.formatMessage({ id: "CTA_NEXT" })}</Button>
              </div>}
          </div>
        </div>

      </div>

      {<PaymentSummary paymentItem={bankitems} className={currentSection > CurrentSection.PaymentMethod ? "" : "payment-hidden"}
        isPreauth={isPreauth}
        inputValue={inputValue}
        inputBankValue={inputBankValue}
        isNewbank={isBankChecked ?? false}
        onEditClick={handleOnEditClick}
        showHeading={true}
        isBankPaymentSelected={isBankPaymentSelected}
        isSingleClickEnable={isSingleClickEnableForPACC || isSingleClickEnableForPAD}
        bankList={bankList}
        debitCardAutopayOffers={debitCardAutopayOffers}
        creditCardAutopayOffers={creditCardAutopayOffers}
        checkedBillItems={checkedBillItems}
        bankitems={bankitems}
        isConfirmation={false}
        isShow={currentSection > CurrentSection.PaymentMethod ? true : false}
      />}
    </>
  );
};

const mapStateToProps = (state: State) => ({
  creditcardDetails: state.creditCardDetails,
  validationErrors: state.validationErrors,
  cardTokenizationSuccess: state.cardTokenizationSuccess,
  redirectUrl: state.redirectUrl,
  interacBankInfo: state.interacBankInfo,
  interactBankFailureInfo: state.interactBankFailureInfo,
  cancelPreauthStatus: state.cancelPreauthStatus,
  cancelPreauthPayments: state.cancelPreauthPayments
});

const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({
  onCreditCardNumberChange: (ccnumber: any) => {
    const details = new CCDetails();
    let valErrors;
    details.CreditCardNumber = ccnumber;

    if (!ccnumber) {
      valErrors = validateCreditCardNumber(ccnumber);
      dispatch(setValidationErrors(valErrors));
    }

    if (!valErrors || valErrors.length <= 0 || !valErrors.errors || valErrors.errors.length <= 0) {
      dispatch(onCreditCardNumberChange(details));
    }
  },
  onCardHolderNameChange: (ccname: any) => {
    const details = new CCDetails();
    let valErrors;
    details.CardholderName = ccname;

    valErrors = validateCardHolderName(ccname);

    if (valErrors?.errors?.length > 0) {
      dispatch(setValidationErrors(valErrors));
    }

    if (!valErrors || valErrors.length <= 0 || !valErrors.errors || valErrors.errors.length <= 0) {
      dispatch(onCardHolderNameChange(details));
    }
  },
  onCreditCardExpiryDateChange: (month: any, year: any) => {
    const details = new CCDetails();
    details.ExpireMonth = month;
    details.ExpireYear = year;

    const valErrors = validateCreditCardExpiryDate(month, year);
    if (valErrors.length > 0 || valErrors.errors) {
      dispatch(setValidationErrors(valErrors));
    }

    if (!valErrors || valErrors.length <= 0 || !valErrors.errors || valErrors.errors.length <= 0) {
      dispatch(onCreditCardExpiryDateChange(details));
    }
  },
  onSecurityCodeChange: (securityCode: any) => {
    const details = new CCDetails();
    details.SecurityCode = securityCode;
    let valErrors;

    if (!securityCode) {
      valErrors = validateSecurityCode(securityCode);
      dispatch(setValidationErrors(valErrors));
    }

    if (!valErrors || valErrors.length <= 0 || !valErrors.errors || valErrors.errors.length <= 0) {
      dispatch(onSecurityCodeChange(details));
    }
  },
  validatBankDetails: (validationForm: any) => {
    const validationErrors = validatBankDetailsErrorMapper(validationForm);
    if (validationForm.isValid) {
      dispatch(resetValidationErrors({ errors: [] }));
    } else {
      dispatch(setValidationErrors(validationErrors));
    }
  },
  resetValidationErrors: (errors: any) => { dispatch(resetValidationErrors(errors)); },
  validateFormOrder: (ban: string, type: boolean, details: any, accountInputValue: any, isBankPaymentSelected: boolean, sub?: string) => { dispatch(validateMultiOrderPaymentAction({ ban, type, details, accountInputValue, isBankPaymentSelected, sub })); },
  tokenizeAndPropagateFormValues: (form: FormSubmit, ban: string, type: boolean, details: any, isBankPaymentSelected: boolean, sub?: string) => { dispatch(tokenizeAndPropagateFormValues({ form, ban, type, details, isBankPaymentSelected, sub })); },
  setOmnitureOnPaymentSelect: (data?: any) => { dispatch(OmnitureOnPaymentSelect({ data })); },
  cancelPreauthPaymentsAction: (bans: string[]) => { dispatch(cancelPreauthAction({ bans })); },
  setOmnitureOnCancelationCompleted: (data?: any) => { dispatch(OmnitureOnCancelationCompleted(data)); },
  setOmnitureOnCancelationPartiallyCompleted: (data?: any) => { dispatch(OmnitureOnCancelationPartiallyCompleted(data)); },
  setOmnitureOnCancelationFailed: (data?: any) => { dispatch(OmnitureOnCancelationFailed(data)); },
  setOmnitureOnCancelationPartiallyFailed: (data?: any) => { dispatch(OmnitureOnCancelationPartiallyFailed(data)); },
  setCancellationPaymentFailStatus: (type: any) => dispatch(apiConfirmationStatus({ type })),
  setOmnitureOnInteracFailure: (data: any) => { dispatch(OmnitureOnInteracFailure(data)); }
});

export const PaymentMethod = connect(mapStateToProps, mapDispatchToProps)(injectIntl(PaymentMethodComponent));
