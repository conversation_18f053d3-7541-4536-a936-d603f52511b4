!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):((e="undefined"!=typeof globalThis?globalThis:e||self).prettierPlugins=e.prettierPlugins||{},e.prettierPlugins.angular=t())}(this,(function(){"use strict";var e=e=>"string"==typeof e?e.replace((({onlyFirst:e=!1}={})=>{const t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:[a-zA-Z\\d]*(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")})(),""):e;const t=e=>!Number.isNaN(e)&&(e>=4352&&(e<=4447||9001===e||9002===e||11904<=e&&e<=12871&&12351!==e||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141));var r=t,n=t;r.default=n;const i=t=>{if("string"!=typeof t||0===t.length)return 0;if(0===(t=e(t)).length)return 0;t=t.replace(/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F|\uD83D\uDC68(?:\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68\uD83C\uDFFB|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|[\u2695\u2696\u2708]\uFE0F|\uD83D[\uDC66\uDC67]|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708])\uFE0F|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C[\uDFFB-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)\uD83C\uDFFB|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB\uDFFC])|\uD83D\uDC69(?:\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB-\uDFFD])|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|(?:(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)\uFE0F|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620)\uFE0F|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF4\uD83C\uDDF2|\uD83C\uDDF6\uD83C\uDDE6|[#\*0-9]\uFE0F\u20E3|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC70\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD36\uDDB5\uDDB6\uDDBB\uDDD2-\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5\uDEEB\uDEEC\uDEF4-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g,"  ");let n=0;for(let e=0;e<t.length;e++){const i=t.codePointAt(e);i<=31||i>=127&&i<=159||(i>=768&&i<=879||(i>65535&&e++,n+=r(i)?2:1))}return n};var s=i,u=i;s.default=u;var a=e=>{if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")};var o=e=>e[e.length-1];function D(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},s=Object.keys(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var c="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function l(e){var t={exports:{}};return e(t,t.exports),t.exports}var p=function(e){return e&&e.Math==Math&&e},h=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof c&&c)||function(){return this}()||Function("return this")(),d=function(e){try{return!!e()}catch(e){return!0}},f=!d((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),E={}.propertyIsEnumerable,C=Object.getOwnPropertyDescriptor,v={f:C&&!E.call({1:2},1)?function(e){var t=C(this,e);return!!t&&t.enumerable}:E},F=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},g={}.toString,m=function(e){return g.call(e).slice(8,-1)},y="".split,x=d((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==m(e)?y.call(e,""):Object(e)}:Object,A=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},S=function(e){return x(A(e))},w=function(e){return"object"==typeof e?null!==e:"function"==typeof e},I=function(e,t){if(!w(e))return e;var r,n;if(t&&"function"==typeof(r=e.toString)&&!w(n=r.call(e)))return n;if("function"==typeof(r=e.valueOf)&&!w(n=r.call(e)))return n;if(!t&&"function"==typeof(r=e.toString)&&!w(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")},O=function(e){return Object(A(e))},N={}.hasOwnProperty,b=Object.hasOwn||function(e,t){return N.call(O(e),t)},B=h.document,P=w(B)&&w(B.createElement),T=!f&&!d((function(){return 7!=Object.defineProperty((e="div",P?B.createElement(e):{}),"a",{get:function(){return 7}}).a;var e})),L=Object.getOwnPropertyDescriptor,R={f:f?L:function(e,t){if(e=S(e),t=I(t,!0),T)try{return L(e,t)}catch(e){}if(b(e,t))return F(!v.f.call(e,t),e[t])}},k=function(e){if(!w(e))throw TypeError(String(e)+" is not an object");return e},$=Object.defineProperty,M={f:f?$:function(e,t,r){if(k(e),t=I(t,!0),k(r),T)try{return $(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},_=f?function(e,t,r){return M.f(e,t,F(1,r))}:function(e,t,r){return e[t]=r,e},j=function(e,t){try{_(h,e,t)}catch(r){h[e]=t}return t},G="__core-js_shared__",K=h[G]||j(G,{}),U=Function.toString;"function"!=typeof K.inspectSource&&(K.inspectSource=function(e){return U.call(e)});var W,V,X,z,q=K.inspectSource,Q=h.WeakMap,Y="function"==typeof Q&&/native code/.test(q(Q)),H=l((function(e){(e.exports=function(e,t){return K[e]||(K[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.14.0",mode:"global",copyright:"\xa9 2021 Denis Pushkarev (zloirock.ru)"})})),J=0,Z=Math.random(),ee=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++J+Z).toString(36)},te=H("keys"),re={},ne="Object already initialized",ie=h.WeakMap;if(Y||K.state){var se=K.state||(K.state=new ie),ue=se.get,ae=se.has,oe=se.set;W=function(e,t){if(ae.call(se,e))throw new TypeError(ne);return t.facade=e,oe.call(se,e,t),t},V=function(e){return ue.call(se,e)||{}},X=function(e){return ae.call(se,e)}}else{var De=te[z="state"]||(te[z]=ee(z));re[De]=!0,W=function(e,t){if(b(e,De))throw new TypeError(ne);return t.facade=e,_(e,De,t),t},V=function(e){return b(e,De)?e[De]:{}},X=function(e){return b(e,De)}}var ce,le,pe={set:W,get:V,has:X,enforce:function(e){return X(e)?V(e):W(e,{})},getterFor:function(e){return function(t){var r;if(!w(t)||(r=V(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return r}}},he=l((function(e){var t=pe.get,r=pe.enforce,n=String(String).split("String");(e.exports=function(e,t,i,s){var u,a=!!s&&!!s.unsafe,o=!!s&&!!s.enumerable,D=!!s&&!!s.noTargetGet;"function"==typeof i&&("string"!=typeof t||b(i,"name")||_(i,"name",t),(u=r(i)).source||(u.source=n.join("string"==typeof t?t:""))),e!==h?(a?!D&&e[t]&&(o=!0):delete e[t],o?e[t]=i:_(e,t,i)):o?e[t]=i:j(t,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&t(this).source||q(this)}))})),de=h,fe=function(e){return"function"==typeof e?e:void 0},Ee=function(e,t){return arguments.length<2?fe(de[e])||fe(h[e]):de[e]&&de[e][t]||h[e]&&h[e][t]},Ce=Math.ceil,ve=Math.floor,Fe=function(e){return isNaN(e=+e)?0:(e>0?ve:Ce)(e)},ge=Math.min,me=function(e){return e>0?ge(Fe(e),9007199254740991):0},ye=Math.max,xe=Math.min,Ae=function(e){return function(t,r,n){var i,s=S(t),u=me(s.length),a=function(e,t){var r=Fe(e);return r<0?ye(r+t,0):xe(r,t)}(n,u);if(e&&r!=r){for(;u>a;)if((i=s[a++])!=i)return!0}else for(;u>a;a++)if((e||a in s)&&s[a]===r)return e||a||0;return!e&&-1}},Se={includes:Ae(!0),indexOf:Ae(!1)}.indexOf,we=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),Ie={f:Object.getOwnPropertyNames||function(e){return function(e,t){var r,n=S(e),i=0,s=[];for(r in n)!b(re,r)&&b(n,r)&&s.push(r);for(;t.length>i;)b(n,r=t[i++])&&(~Se(s,r)||s.push(r));return s}(e,we)}},Oe={f:Object.getOwnPropertySymbols},Ne=Ee("Reflect","ownKeys")||function(e){var t=Ie.f(k(e)),r=Oe.f;return r?t.concat(r(e)):t},be=function(e,t){for(var r=Ne(t),n=M.f,i=R.f,s=0;s<r.length;s++){var u=r[s];b(e,u)||n(e,u,i(t,u))}},Be=/#|\.prototype\./,Pe=function(e,t){var r=Le[Te(e)];return r==ke||r!=Re&&("function"==typeof t?d(t):!!t)},Te=Pe.normalize=function(e){return String(e).replace(Be,".").toLowerCase()},Le=Pe.data={},Re=Pe.NATIVE="N",ke=Pe.POLYFILL="P",$e=Pe,Me=R.f,_e=function(e,t){var r,n,i,s,u,a=e.target,o=e.global,D=e.stat;if(r=o?h:D?h[a]||j(a,{}):(h[a]||{}).prototype)for(n in t){if(s=t[n],i=e.noTargetGet?(u=Me(r,n))&&u.value:r[n],!$e(o?n:a+(D?".":"#")+n,e.forced)&&void 0!==i){if(typeof s==typeof i)continue;be(s,i)}(e.sham||i&&i.sham)&&_(s,"sham",!0),he(r,n,s,e)}},je=Array.isArray||function(e){return"Array"==m(e)},Ge=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e},Ke=function(e,t,r){if(Ge(e),void 0===t)return e;switch(r){case 0:return function(){return e.call(t)};case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,i){return e.call(t,r,n,i)}}return function(){return e.apply(t,arguments)}},Ue=function(e,t,r,n,i,s,u,a){for(var o,D=i,c=0,l=!!u&&Ke(u,a,3);c<n;){if(c in r){if(o=l?l(r[c],c,t):r[c],s>0&&je(o))D=Ue(e,t,o,me(o.length),D,s-1)-1;else{if(D>=9007199254740991)throw TypeError("Exceed the acceptable array length");e[D]=o}D++}c++}return D},We=Ue,Ve=Ee("navigator","userAgent")||"",Xe=h.process,ze=Xe&&Xe.versions,qe=ze&&ze.v8;qe?le=(ce=qe.split("."))[0]<4?1:ce[0]+ce[1]:Ve&&(!(ce=Ve.match(/Edge\/(\d+)/))||ce[1]>=74)&&(ce=Ve.match(/Chrome\/(\d+)/))&&(le=ce[1]);var Qe=le&&+le,Ye=!!Object.getOwnPropertySymbols&&!d((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&Qe&&Qe<41})),He=Ye&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Je=H("wks"),Ze=h.Symbol,et=He?Ze:Ze&&Ze.withoutSetter||ee,tt=function(e){return b(Je,e)&&(Ye||"string"==typeof Je[e])||(Ye&&b(Ze,e)?Je[e]=Ze[e]:Je[e]=et("Symbol."+e)),Je[e]},rt=tt("species"),nt=function(e,t){var r;return je(e)&&("function"!=typeof(r=e.constructor)||r!==Array&&!je(r.prototype)?w(r)&&null===(r=r[rt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===t?0:t)};_e({target:"Array",proto:!0},{flatMap:function(e){var t,r=O(this),n=me(r.length);return Ge(e),(t=nt(r,0)).length=We(t,r,r,n,0,1,e,arguments.length>1?arguments[1]:void 0),t}});var it,st,ut=Math.floor,at=function(e,t){var r=e.length,n=ut(r/2);return r<8?ot(e,t):Dt(at(e.slice(0,n),t),at(e.slice(n),t),t)},ot=function(e,t){for(var r,n,i=e.length,s=1;s<i;){for(n=s,r=e[s];n&&t(e[n-1],r)>0;)e[n]=e[--n];n!==s++&&(e[n]=r)}return e},Dt=function(e,t,r){for(var n=e.length,i=t.length,s=0,u=0,a=[];s<n||u<i;)s<n&&u<i?a.push(r(e[s],t[u])<=0?e[s++]:t[u++]):a.push(s<n?e[s++]:t[u++]);return a},ct=at,lt=Ve.match(/firefox\/(\d+)/i),pt=!!lt&&+lt[1],ht=/MSIE|Trident/.test(Ve),dt=Ve.match(/AppleWebKit\/(\d+)\./),ft=!!dt&&+dt[1],Et=[],Ct=Et.sort,vt=d((function(){Et.sort(void 0)})),Ft=d((function(){Et.sort(null)})),gt=!!(st=[]["sort"])&&d((function(){st.call(null,it||function(){throw 1},1)})),mt=!d((function(){if(Qe)return Qe<70;if(!(pt&&pt>3)){if(ht)return!0;if(ft)return ft<603;var e,t,r,n,i="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(n=0;n<47;n++)Et.push({k:t+n,v:r})}for(Et.sort((function(e,t){return t.v-e.v})),n=0;n<Et.length;n++)t=Et[n].k.charAt(0),i.charAt(i.length-1)!==t&&(i+=t);return"DGBEFHACIJK"!==i}}));_e({target:"Array",proto:!0,forced:vt||!Ft||!gt||!mt},{sort:function(e){void 0!==e&&Ge(e);var t=O(this);if(mt)return void 0===e?Ct.call(t):Ct.call(t,e);var r,n,i=[],s=me(t.length);for(n=0;n<s;n++)n in t&&i.push(t[n]);for(r=(i=ct(i,function(e){return function(t,r){return void 0===r?-1:void 0===t?1:void 0!==e?+e(t,r)||0:String(t)>String(r)?1:-1}}(e))).length,n=0;n<r;)t[n]=i[n++];for(;n<s;)delete t[n++];return t}});var yt={},xt=tt("iterator"),At=Array.prototype,St={};St[tt("toStringTag")]="z";var wt="[object z]"===String(St),It=tt("toStringTag"),Ot="Arguments"==m(function(){return arguments}()),Nt=wt?m:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),It))?r:Ot?m(t):"Object"==(n=m(t))&&"function"==typeof t.callee?"Arguments":n},bt=tt("iterator"),Bt=function(e){var t=e.return;if(void 0!==t)return k(t.call(e)).value},Pt=function(e,t){this.stopped=e,this.result=t},Tt=function(e,t,r){var n,i,s,u,a,o,D,c,l=r&&r.that,p=!(!r||!r.AS_ENTRIES),h=!(!r||!r.IS_ITERATOR),d=!(!r||!r.INTERRUPTED),f=Ke(t,l,1+p+d),E=function(e){return n&&Bt(n),new Pt(!0,e)},C=function(e){return p?(k(e),d?f(e[0],e[1],E):f(e[0],e[1])):d?f(e,E):f(e)};if(h)n=e;else{if("function"!=typeof(i=function(e){if(null!=e)return e[bt]||e["@@iterator"]||yt[Nt(e)]}(e)))throw TypeError("Target is not iterable");if(void 0!==(c=i)&&(yt.Array===c||At[xt]===c)){for(s=0,u=me(e.length);u>s;s++)if((a=C(e[s]))&&a instanceof Pt)return a;return new Pt(!1)}n=i.call(e)}for(o=n.next;!(D=o.call(n)).done;){try{a=C(D.value)}catch(e){throw Bt(n),e}if("object"==typeof a&&a&&a instanceof Pt)return a}return new Pt(!1)};_e({target:"Object",stat:!0},{fromEntries:function(e){var t={};return Tt(e,(function(e,r){!function(e,t,r){var n=I(t);n in e?M.f(e,n,F(0,r)):e[n]=r}(t,e,r)}),{AS_ENTRIES:!0}),t}});var Lt=void 0!==Lt?Lt:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};function Rt(){throw new Error("setTimeout has not been defined")}function kt(){throw new Error("clearTimeout has not been defined")}var $t=Rt,Mt=kt;function _t(e){if($t===setTimeout)return setTimeout(e,0);if(($t===Rt||!$t)&&setTimeout)return $t=setTimeout,setTimeout(e,0);try{return $t(e,0)}catch(t){try{return $t.call(null,e,0)}catch(t){return $t.call(this,e,0)}}}"function"==typeof Lt.setTimeout&&($t=setTimeout),"function"==typeof Lt.clearTimeout&&(Mt=clearTimeout);var jt,Gt=[],Kt=!1,Ut=-1;function Wt(){Kt&&jt&&(Kt=!1,jt.length?Gt=jt.concat(Gt):Ut=-1,Gt.length&&Vt())}function Vt(){if(!Kt){var e=_t(Wt);Kt=!0;for(var t=Gt.length;t;){for(jt=Gt,Gt=[];++Ut<t;)jt&&jt[Ut].run();Ut=-1,t=Gt.length}jt=null,Kt=!1,function(e){if(Mt===clearTimeout)return clearTimeout(e);if((Mt===kt||!Mt)&&clearTimeout)return Mt=clearTimeout,clearTimeout(e);try{Mt(e)}catch(t){try{return Mt.call(null,e)}catch(t){return Mt.call(this,e)}}}(e)}}function Xt(e,t){this.fun=e,this.array=t}Xt.prototype.run=function(){this.fun.apply(null,this.array)};function zt(){}var qt=zt,Qt=zt,Yt=zt,Ht=zt,Jt=zt,Zt=zt,er=zt;var tr=Lt.performance||{},rr=tr.now||tr.mozNow||tr.msNow||tr.oNow||tr.webkitNow||function(){return(new Date).getTime()};var nr=new Date;var ir={nextTick:function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];Gt.push(new Xt(e,t)),1!==Gt.length||Kt||_t(Vt)},title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:qt,addListener:Qt,once:Yt,off:Ht,removeListener:Jt,removeAllListeners:Zt,emit:er,binding:function(e){throw new Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(e){throw new Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(e){var t=.001*rr.call(tr),r=Math.floor(t),n=Math.floor(t%1*1e9);return e&&(r-=e[0],(n-=e[1])<0&&(r--,n+=1e9)),[r,n]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-nr)/1e3}};var sr="object"==typeof ir&&ir.env&&ir.env.NODE_DEBUG&&/\bsemver\b/i.test(ir.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};var ur={SEMVER_SPEC_VERSION:"2.0.0",MAX_LENGTH:256,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||9007199254740991,MAX_SAFE_COMPONENT_LENGTH:16},ar=l((function(e,t){const{MAX_SAFE_COMPONENT_LENGTH:r}=ur,n=(t=e.exports={}).re=[],i=t.src=[],s=t.t={};let u=0;const a=(e,t,r)=>{const a=u++;sr(a,t),s[e]=a,i[a]=t,n[a]=new RegExp(t,r?"g":void 0)};a("NUMERICIDENTIFIER","0|[1-9]\\d*"),a("NUMERICIDENTIFIERLOOSE","[0-9]+"),a("NONNUMERICIDENTIFIER","\\d*[a-zA-Z-][a-zA-Z0-9-]*"),a("MAINVERSION",`(${i[s.NUMERICIDENTIFIER]})\\.(${i[s.NUMERICIDENTIFIER]})\\.(${i[s.NUMERICIDENTIFIER]})`),a("MAINVERSIONLOOSE",`(${i[s.NUMERICIDENTIFIERLOOSE]})\\.(${i[s.NUMERICIDENTIFIERLOOSE]})\\.(${i[s.NUMERICIDENTIFIERLOOSE]})`),a("PRERELEASEIDENTIFIER",`(?:${i[s.NUMERICIDENTIFIER]}|${i[s.NONNUMERICIDENTIFIER]})`),a("PRERELEASEIDENTIFIERLOOSE",`(?:${i[s.NUMERICIDENTIFIERLOOSE]}|${i[s.NONNUMERICIDENTIFIER]})`),a("PRERELEASE",`(?:-(${i[s.PRERELEASEIDENTIFIER]}(?:\\.${i[s.PRERELEASEIDENTIFIER]})*))`),a("PRERELEASELOOSE",`(?:-?(${i[s.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${i[s.PRERELEASEIDENTIFIERLOOSE]})*))`),a("BUILDIDENTIFIER","[0-9A-Za-z-]+"),a("BUILD",`(?:\\+(${i[s.BUILDIDENTIFIER]}(?:\\.${i[s.BUILDIDENTIFIER]})*))`),a("FULLPLAIN",`v?${i[s.MAINVERSION]}${i[s.PRERELEASE]}?${i[s.BUILD]}?`),a("FULL",`^${i[s.FULLPLAIN]}$`),a("LOOSEPLAIN",`[v=\\s]*${i[s.MAINVERSIONLOOSE]}${i[s.PRERELEASELOOSE]}?${i[s.BUILD]}?`),a("LOOSE",`^${i[s.LOOSEPLAIN]}$`),a("GTLT","((?:<|>)?=?)"),a("XRANGEIDENTIFIERLOOSE",`${i[s.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),a("XRANGEIDENTIFIER",`${i[s.NUMERICIDENTIFIER]}|x|X|\\*`),a("XRANGEPLAIN",`[v=\\s]*(${i[s.XRANGEIDENTIFIER]})(?:\\.(${i[s.XRANGEIDENTIFIER]})(?:\\.(${i[s.XRANGEIDENTIFIER]})(?:${i[s.PRERELEASE]})?${i[s.BUILD]}?)?)?`),a("XRANGEPLAINLOOSE",`[v=\\s]*(${i[s.XRANGEIDENTIFIERLOOSE]})(?:\\.(${i[s.XRANGEIDENTIFIERLOOSE]})(?:\\.(${i[s.XRANGEIDENTIFIERLOOSE]})(?:${i[s.PRERELEASELOOSE]})?${i[s.BUILD]}?)?)?`),a("XRANGE",`^${i[s.GTLT]}\\s*${i[s.XRANGEPLAIN]}$`),a("XRANGELOOSE",`^${i[s.GTLT]}\\s*${i[s.XRANGEPLAINLOOSE]}$`),a("COERCE",`(^|[^\\d])(\\d{1,${r}})(?:\\.(\\d{1,${r}}))?(?:\\.(\\d{1,${r}}))?(?:$|[^\\d])`),a("COERCERTL",i[s.COERCE],!0),a("LONETILDE","(?:~>?)"),a("TILDETRIM",`(\\s*)${i[s.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",a("TILDE",`^${i[s.LONETILDE]}${i[s.XRANGEPLAIN]}$`),a("TILDELOOSE",`^${i[s.LONETILDE]}${i[s.XRANGEPLAINLOOSE]}$`),a("LONECARET","(?:\\^)"),a("CARETTRIM",`(\\s*)${i[s.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",a("CARET",`^${i[s.LONECARET]}${i[s.XRANGEPLAIN]}$`),a("CARETLOOSE",`^${i[s.LONECARET]}${i[s.XRANGEPLAINLOOSE]}$`),a("COMPARATORLOOSE",`^${i[s.GTLT]}\\s*(${i[s.LOOSEPLAIN]})$|^$`),a("COMPARATOR",`^${i[s.GTLT]}\\s*(${i[s.FULLPLAIN]})$|^$`),a("COMPARATORTRIM",`(\\s*)${i[s.GTLT]}\\s*(${i[s.LOOSEPLAIN]}|${i[s.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",a("HYPHENRANGE",`^\\s*(${i[s.XRANGEPLAIN]})\\s+-\\s+(${i[s.XRANGEPLAIN]})\\s*$`),a("HYPHENRANGELOOSE",`^\\s*(${i[s.XRANGEPLAINLOOSE]})\\s+-\\s+(${i[s.XRANGEPLAINLOOSE]})\\s*$`),a("STAR","(<|>)?=?\\s*\\*"),a("GTE0","^\\s*>=\\s*0.0.0\\s*$"),a("GTE0PRE","^\\s*>=\\s*0.0.0-0\\s*$")}));const or=["includePrerelease","loose","rtl"];var Dr=e=>e?"object"!=typeof e?{loose:!0}:or.filter((t=>e[t])).reduce(((e,t)=>(e[t]=!0,e)),{}):{};const cr=/^[0-9]+$/,lr=(e,t)=>{const r=cr.test(e),n=cr.test(t);return r&&n&&(e=+e,t=+t),e===t?0:r&&!n?-1:n&&!r?1:e<t?-1:1};var pr={compareIdentifiers:lr,rcompareIdentifiers:(e,t)=>lr(t,e)};const{MAX_LENGTH:hr,MAX_SAFE_INTEGER:dr}=ur,{re:fr,t:Er}=ar,{compareIdentifiers:Cr}=pr;class vr{constructor(e,t){if(t=Dr(t),e instanceof vr){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid Version: ${e}`);if(e.length>hr)throw new TypeError(`version is longer than ${hr} characters`);sr("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;const r=e.trim().match(t.loose?fr[Er.LOOSE]:fr[Er.FULL]);if(!r)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>dr||this.major<0)throw new TypeError("Invalid major version");if(this.minor>dr||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>dr||this.patch<0)throw new TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<dr)return t}return e})):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(sr("SemVer.compare",this.version,this.options,e),!(e instanceof vr)){if("string"==typeof e&&e===this.version)return 0;e=new vr(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof vr||(e=new vr(e,this.options)),Cr(this.major,e.major)||Cr(this.minor,e.minor)||Cr(this.patch,e.patch)}comparePre(e){if(e instanceof vr||(e=new vr(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{const r=this.prerelease[t],n=e.prerelease[t];if(sr("prerelease compare",t,r,n),void 0===r&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===r)return-1;if(r!==n)return Cr(r,n)}while(++t)}compareBuild(e){e instanceof vr||(e=new vr(e,this.options));let t=0;do{const r=this.build[t],n=e.build[t];if(sr("prerelease compare",t,r,n),void 0===r&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===r)return-1;if(r!==n)return Cr(r,n)}while(++t)}inc(e,t){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t),this.inc("pre",t);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t),this.inc("pre",t);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":if(0===this.prerelease.length)this.prerelease=[0];else{let e=this.prerelease.length;for(;--e>=0;)"number"==typeof this.prerelease[e]&&(this.prerelease[e]++,e=-2);-1===e&&this.prerelease.push(0)}t&&(this.prerelease[0]===t?isNaN(this.prerelease[1])&&(this.prerelease=[t,0]):this.prerelease=[t,0]);break;default:throw new Error(`invalid increment argument: ${e}`)}return this.format(),this.raw=this.version,this}}var Fr=vr;var gr=(e,t,r)=>new Fr(e,r).compare(new Fr(t,r));var mr=(e,t,r)=>gr(e,t,r)<0;var yr=(e,t,r)=>gr(e,t,r)>=0,xr="2.3.2",Ar=l((function(e,t){function r(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]}function n(){return"undefined"!=typeof WeakMap?new WeakMap:{add:r,delete:r,get:r,set:r,has:function(e){return!1}}}Object.defineProperty(t,"__esModule",{value:!0}),t.outdent=void 0;var i=Object.prototype.hasOwnProperty,s=function(e,t){return i.call(e,t)};function u(e,t){for(var r in t)s(t,r)&&(e[r]=t[r]);return e}var a=/^[ \t]*(?:\r\n|\r|\n)/,o=/(?:\r\n|\r|\n)[ \t]*$/,D=/^(?:[\r\n]|$)/,c=/(?:\r\n|\r|\n)([ \t]*)(?:[^ \t\r\n]|$)/,l=/^[ \t]*[\r\n][ \t\r\n]*$/;function p(e,t,r){var n=0,i=e[0].match(c);i&&(n=i[1].length);var s=new RegExp("(\\r\\n|\\r|\\n).{0,"+n+"}","g");t&&(e=e.slice(1));var u=r.newline,D=r.trimLeadingNewline,l=r.trimTrailingNewline,p="string"==typeof u,h=e.length;return e.map((function(e,t){return e=e.replace(s,"$1"),0===t&&D&&(e=e.replace(a,"")),t===h-1&&l&&(e=e.replace(o,"")),p&&(e=e.replace(/\r\n|\n|\r/g,(function(e){return u}))),e}))}function h(e,t){for(var r="",n=0,i=e.length;n<i;n++)r+=e[n],n<i-1&&(r+=t[n]);return r}function d(e){return s(e,"raw")&&s(e,"length")}var f=function e(t){var r=n(),i=n();return u((function n(s){for(var a=[],o=1;o<arguments.length;o++)a[o-1]=arguments[o];if(d(s)){var c=s,E=(a[0]===n||a[0]===f)&&l.test(c[0])&&D.test(c[1]),C=E?i:r,v=C.get(c);if(v||(v=p(c,E,t),C.set(c,v)),0===a.length)return v[0];var F=h(v,E?a.slice(1):a);return F}return e(u(u({},t),s||{}))}),{string:function(e){return p([e],!1,t)[0]}})}({trimLeadingNewline:!0,trimTrailingNewline:!0});t.outdent=f,t.default=f;try{e.exports=f,Object.defineProperty(f,"__esModule",{value:!0}),f.default=f,f.outdent=f}catch(e){}}));const{outdent:Sr}=Ar,wr="Config",Ir="Editor",Or="Other",Nr="Global",br="Special",Br={cursorOffset:{since:"1.4.0",category:br,type:"int",default:-1,range:{start:-1,end:Number.POSITIVE_INFINITY,step:1},description:Sr`
      Print (to stderr) where a cursor at the given position would move to after formatting.
      This option cannot be used with --range-start and --range-end.
    `,cliCategory:Ir},endOfLine:{since:"1.15.0",category:Nr,type:"choice",default:[{since:"1.15.0",value:"auto"},{since:"2.0.0",value:"lf"}],description:"Which end of line characters to apply.",choices:[{value:"lf",description:"Line Feed only (\\n), common on Linux and macOS as well as inside git repos"},{value:"crlf",description:"Carriage Return + Line Feed characters (\\r\\n), common on Windows"},{value:"cr",description:"Carriage Return character only (\\r), used very rarely"},{value:"auto",description:Sr`
          Maintain existing
          (mixed values within one file are normalised by looking at what's used after the first line)
        `}]},filepath:{since:"1.4.0",category:br,type:"path",description:"Specify the input filepath. This will be used to do parser inference.",cliName:"stdin-filepath",cliCategory:Or,cliDescription:"Path to the file to pretend that stdin comes from."},insertPragma:{since:"1.8.0",category:br,type:"boolean",default:!1,description:"Insert @format pragma into file's first docblock comment.",cliCategory:Or},parser:{since:"0.0.10",category:Nr,type:"choice",default:[{since:"0.0.10",value:"babylon"},{since:"1.13.0",value:void 0}],description:"Which parser to use.",exception:e=>"string"==typeof e||"function"==typeof e,choices:[{value:"flow",description:"Flow"},{value:"babel",since:"1.16.0",description:"JavaScript"},{value:"babel-flow",since:"1.16.0",description:"Flow"},{value:"babel-ts",since:"2.0.0",description:"TypeScript"},{value:"typescript",since:"1.4.0",description:"TypeScript"},{value:"espree",since:"2.2.0",description:"JavaScript"},{value:"meriyah",since:"2.2.0",description:"JavaScript"},{value:"css",since:"1.7.1",description:"CSS"},{value:"less",since:"1.7.1",description:"Less"},{value:"scss",since:"1.7.1",description:"SCSS"},{value:"json",since:"1.5.0",description:"JSON"},{value:"json5",since:"1.13.0",description:"JSON5"},{value:"json-stringify",since:"1.13.0",description:"JSON.stringify"},{value:"graphql",since:"1.5.0",description:"GraphQL"},{value:"markdown",since:"1.8.0",description:"Markdown"},{value:"mdx",since:"1.15.0",description:"MDX"},{value:"vue",since:"1.10.0",description:"Vue"},{value:"yaml",since:"1.14.0",description:"YAML"},{value:"glimmer",since:"2.3.0",description:"Ember / Handlebars"},{value:"html",since:"1.15.0",description:"HTML"},{value:"angular",since:"1.15.0",description:"Angular"},{value:"lwc",since:"1.17.0",description:"Lightning Web Components"}]},plugins:{since:"1.10.0",type:"path",array:!0,default:[{value:[]}],category:Nr,description:"Add a plugin. Multiple plugins can be passed as separate `--plugin`s.",exception:e=>"string"==typeof e||"object"==typeof e,cliName:"plugin",cliCategory:wr},pluginSearchDirs:{since:"1.13.0",type:"path",array:!0,default:[{value:[]}],category:Nr,description:Sr`
      Custom directory that contains prettier plugins in node_modules subdirectory.
      Overrides default behavior when plugins are searched relatively to the location of Prettier.
      Multiple values are accepted.
    `,exception:e=>"string"==typeof e||"object"==typeof e,cliName:"plugin-search-dir",cliCategory:wr},printWidth:{since:"0.0.0",category:Nr,type:"int",default:80,description:"The line length where Prettier will try wrap.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},rangeEnd:{since:"1.4.0",category:br,type:"int",default:Number.POSITIVE_INFINITY,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:Sr`
      Format code ending at a given character offset (exclusive).
      The range will extend forwards to the end of the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:Ir},rangeStart:{since:"1.4.0",category:br,type:"int",default:0,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:Sr`
      Format code starting at a given character offset.
      The range will extend backwards to the start of the first line containing the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:Ir},requirePragma:{since:"1.7.0",category:br,type:"boolean",default:!1,description:Sr`
      Require either '@prettier' or '@format' to be present in the file's first docblock comment
      in order for it to be formatted.
    `,cliCategory:Or},tabWidth:{type:"int",category:Nr,default:2,description:"Number of spaces per indentation level.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},useTabs:{since:"1.0.0",category:Nr,type:"boolean",default:!1,description:"Indent with tabs instead of spaces."},embeddedLanguageFormatting:{since:"2.1.0",category:Nr,type:"choice",default:[{since:"2.1.0",value:"auto"}],description:"Control how Prettier formats quoted code embedded in the file.",choices:[{value:"auto",description:"Format embedded code if Prettier can automatically identify it."},{value:"off",description:"Never automatically format embedded code."}]}};const Pr=["cliName","cliCategory","cliDescription"],Tr={compare:gr,lt:mr,gte:yr},Lr=xr,Rr={CATEGORY_CONFIG:wr,CATEGORY_EDITOR:Ir,CATEGORY_FORMAT:"Format",CATEGORY_OTHER:Or,CATEGORY_OUTPUT:"Output",CATEGORY_GLOBAL:Nr,CATEGORY_SPECIAL:br,options:Br}.options;var kr={getSupportInfo:function({plugins:e=[],showUnreleased:t=!1,showDeprecated:r=!1,showInternal:n=!1}={}){const i=Lr.split("-",1)[0],s=e.flatMap((e=>e.languages||[])).filter(c),u=(a=Object.assign({},...e.map((({options:e})=>e)),Rr),o="name",Object.entries(a).map((([e,t])=>Object.assign({[o]:e},t)))).filter((e=>c(e)&&l(e))).sort(((e,t)=>e.name===t.name?0:e.name<t.name?-1:1)).map((function(e){if(n)return e;return D(e,Pr)})).map((t=>{t=Object.assign({},t),Array.isArray(t.default)&&(t.default=1===t.default.length?t.default[0].value:t.default.filter(c).sort(((e,t)=>Tr.compare(t.since,e.since)))[0].value),Array.isArray(t.choices)&&(t.choices=t.choices.filter((e=>c(e)&&l(e))),"parser"===t.name&&function(e,t,r){const n=new Set(e.choices.map((e=>e.value)));for(const i of t)if(i.parsers)for(const t of i.parsers)if(!n.has(t)){n.add(t);const s=r.find((e=>e.parsers&&e.parsers[t]));let u=i.name;s&&s.name&&(u+=` (plugin: ${s.name})`),e.choices.push({value:t,description:u})}}(t,s,e));const r=Object.fromEntries(e.filter((e=>e.defaultOptions&&void 0!==e.defaultOptions[t.name])).map((e=>[e.name,e.defaultOptions[t.name]])));return Object.assign(Object.assign({},t),{},{pluginDefaults:r})}));var a,o;return{languages:s,options:u};function c(e){return t||!("since"in e)||e.since&&Tr.gte(i,e.since)}function l(e){return r||!("deprecated"in e)||e.deprecated&&Tr.lt(i,e.deprecated)}}};const{getSupportInfo:$r}=kr,Mr=/[^\x20-\x7F]/;function _r(e){return(t,r,n)=>{const i=n&&n.backwards;if(!1===r)return!1;const{length:s}=t;let u=r;for(;u>=0&&u<s;){const r=t.charAt(u);if(e instanceof RegExp){if(!e.test(r))return u}else if(!e.includes(r))return u;i?u--:u++}return(-1===u||u===s)&&u}}const jr=_r(/\s/),Gr=_r(" \t"),Kr=_r(",; \t"),Ur=_r(/[^\n\r]/);function Wr(e,t){if(!1===t)return!1;if("/"===e.charAt(t)&&"*"===e.charAt(t+1))for(let r=t+2;r<e.length;++r)if("*"===e.charAt(r)&&"/"===e.charAt(r+1))return r+2;return t}function Vr(e,t){return!1!==t&&("/"===e.charAt(t)&&"/"===e.charAt(t+1)?Ur(e,t):t)}function Xr(e,t,r){const n=r&&r.backwards;if(!1===t)return!1;const i=e.charAt(t);if(n){if("\r"===e.charAt(t-1)&&"\n"===i)return t-2;if("\n"===i||"\r"===i||"\u2028"===i||"\u2029"===i)return t-1}else{if("\r"===i&&"\n"===e.charAt(t+1))return t+2;if("\n"===i||"\r"===i||"\u2028"===i||"\u2029"===i)return t+1}return t}function zr(e,t,r={}){const n=Gr(e,r.backwards?t-1:t,r);return n!==Xr(e,n,r)}function qr(e,t){let r=null,n=t;for(;n!==r;)r=n,n=Kr(e,n),n=Wr(e,n),n=Gr(e,n);return n=Vr(e,n),n=Xr(e,n),!1!==n&&zr(e,n)}function Qr(e,t){let r=null,n=t;for(;n!==r;)r=n,n=Gr(e,n),n=Wr(e,n),n=Vr(e,n),n=Xr(e,n);return n}function Yr(e,t,r){return Qr(e,r(t))}function Hr(e,t,r=0){let n=0;for(let i=r;i<e.length;++i)"\t"===e[i]?n=n+t-n%t:n++;return n}function Jr(e,t){const r=e.slice(1,-1),n={quote:'"',regex:/"/g},i={quote:"'",regex:/'/g},s="'"===t?i:n,u=s===i?n:i;let a=s.quote;if(r.includes(s.quote)||r.includes(u.quote)){a=(r.match(s.regex)||[]).length>(r.match(u.regex)||[]).length?u.quote:s.quote}return a}function Zr(e,t,r){const n='"'===t?"'":'"',i=e.replace(/\\(.)|(["'])/gs,((e,i,s)=>i===n?i:s===t?"\\"+s:s||(r&&/^[^\n\r"'0-7\\bfnrt-vx\u2028\u2029]$/.test(i)?i:"\\"+i)));return t+i+t}function en(e,t){(e.comments||(e.comments=[])).push(t),t.printed=!1,t.nodeDescription=function(e){const t=e.type||e.kind||"(unknown type)";let r=String(e.name||e.id&&("object"==typeof e.id?e.id.name:e.id)||e.key&&("object"==typeof e.key?e.key.name:e.key)||e.value&&("object"==typeof e.value?"":String(e.value))||e.operator||"");r.length>20&&(r=r.slice(0,19)+"\u2026");return t+(r?" "+r:"")}(e)}var tn={inferParserByLanguage:function(e,t){const{languages:r}=$r({plugins:t.plugins}),n=r.find((({name:t})=>t.toLowerCase()===e))||r.find((({aliases:t})=>Array.isArray(t)&&t.includes(e)))||r.find((({extensions:t})=>Array.isArray(t)&&t.includes(`.${e}`)));return n&&n.parsers[0]},getStringWidth:function(e){return e?Mr.test(e)?s(e):e.length:0},getMaxContinuousCount:function(e,t){const r=e.match(new RegExp(`(${a(t)})+`,"g"));return null===r?0:r.reduce(((e,r)=>Math.max(e,r.length/t.length)),0)},getMinNotPresentContinuousCount:function(e,t){const r=e.match(new RegExp(`(${a(t)})+`,"g"));if(null===r)return 0;const n=new Map;let i=0;for(const e of r){const r=e.length/t.length;n.set(r,!0),r>i&&(i=r)}for(let e=1;e<i;e++)if(!n.get(e))return e;return i+1},getPenultimate:e=>e[e.length-2],getLast:o,getNextNonSpaceNonCommentCharacterIndexWithStartIndex:Qr,getNextNonSpaceNonCommentCharacterIndex:Yr,getNextNonSpaceNonCommentCharacter:function(e,t,r){return e.charAt(Yr(e,t,r))},skip:_r,skipWhitespace:jr,skipSpaces:Gr,skipToLineEnd:Kr,skipEverythingButNewLine:Ur,skipInlineComment:Wr,skipTrailingComment:Vr,skipNewline:Xr,isNextLineEmptyAfterIndex:qr,isNextLineEmpty:function(e,t,r){return qr(e,r(t))},isPreviousLineEmpty:function(e,t,r){let n=r(t)-1;return n=Gr(e,n,{backwards:!0}),n=Xr(e,n,{backwards:!0}),n=Gr(e,n,{backwards:!0}),n!==Xr(e,n,{backwards:!0})},hasNewline:zr,hasNewlineInRange:function(e,t,r){for(let n=t;n<r;++n)if("\n"===e.charAt(n))return!0;return!1},hasSpaces:function(e,t,r={}){return Gr(e,r.backwards?t-1:t,r)!==t},getAlignmentSize:Hr,getIndentSize:function(e,t){const r=e.lastIndexOf("\n");return-1===r?0:Hr(e.slice(r+1).match(/^[\t ]*/)[0],t)},getPreferredQuote:Jr,printString:function(e,t){return Zr(e.slice(1,-1),"json"===t.parser||"json5"===t.parser&&"preserve"===t.quoteProps&&!t.singleQuote?'"':t.__isInHtmlAttribute?"'":Jr(e,t.singleQuote?"'":'"'),!("css"===t.parser||"less"===t.parser||"scss"===t.parser||t.__embeddedInHtml))},printNumber:function(e){return e.toLowerCase().replace(/^([+-]?[\d.]+e)(?:\+|(-))?0*(\d)/,"$1$2$3").replace(/^([+-]?[\d.]+)e[+-]?0+$/,"$1").replace(/^([+-])?\./,"$10.").replace(/(\.\d+?)0+(?=e|$)/,"$1").replace(/\.(?=e|$)/,"")},makeString:Zr,addLeadingComment:function(e,t){t.leading=!0,t.trailing=!1,en(e,t)},addDanglingComment:function(e,t,r){t.leading=!1,t.trailing=!1,r&&(t.marker=r),en(e,t)},addTrailingComment:function(e,t){t.leading=!1,t.trailing=!0,en(e,t)},isFrontMatterNode:function(e){return e&&"front-matter"===e.type},getShebang:function(e){if(!e.startsWith("#!"))return"";const t=e.indexOf("\n");return-1===t?e:e.slice(0,t)},isNonEmptyArray:function(e){return Array.isArray(e)&&e.length>0},createGroupIdMapper:function(e){const t=new WeakMap;return function(r){return t.has(r)||t.set(r,Symbol(e)),t.get(r)}}};const{isNonEmptyArray:rn}=tn;function nn(e,t){const{ignoreDecorators:r}=t||{};if(!r){const t=e.declaration&&e.declaration.decorators||e.decorators;if(rn(t))return nn(t[0])}return e.range?e.range[0]:e.start}function sn(e){return e.range?e.range[1]:e.end}function un(e,t){return nn(e)===nn(t)}var an,on={locStart:nn,locEnd:sn,hasSameLocStart:un,hasSameLoc:function(e,t){return un(e,t)&&function(e,t){return sn(e)===sn(t)}(e,t)}},Dn=l((function(e,t){var r="\n",n=function(){function e(e){this.string=e;for(var t=[0],n=0;n<e.length;)switch(e[n]){case r:n+=r.length,t.push(n);break;case"\r":e[n+="\r".length]===r&&(n+=r.length),t.push(n);break;default:n++}this.offsets=t}return e.prototype.locationForIndex=function(e){if(e<0||e>this.string.length)return null;for(var t=0,r=this.offsets;r[t+1]<=e;)t++;return{line:t,column:e-r[t]}},e.prototype.indexForLocation=function(e){var t=e.line,r=e.column;return t<0||t>=this.offsets.length||r<0||r>this.lengthOfLine(t)?null:this.offsets[t]+r},e.prototype.lengthOfLine=function(e){var t=this.offsets[e];return(e===this.offsets.length-1?this.string.length:this.offsets[e+1])-t},e}();t.__esModule=!0,t.default=n})),cn=l((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.Context=void 0;t.Context=class{constructor(e){this.text=e,this.locator=new r(this.text)}};class r{constructor(e){this._lineAndColumn=new Dn.default(e)}locationForIndex(e){const{line:t,column:r}=this._lineAndColumn.locationForIndex(e);return{line:t+1,column:r}}}}));
/**
	 * @license
	 * Copyright Google LLC All Rights Reserved.
	 *
	 * Use of this source code is governed by an MIT-style license that can be
	 * found in the LICENSE file at https://angular.io/license
	 */
class ln{constructor(e,t,r,n){this.input=t,this.errLocation=r,this.ctxLocation=n,this.message=`Parser Error: ${e} ${r} [${t}] in ${n}`}}class pn{constructor(e,t){this.start=e,this.end=t}toAbsolute(e){return new Mn(e+this.start,e+this.end)}}class hn{constructor(e,t){this.span=e,this.sourceSpan=t}visit(e,t=null){return null}toString(){return"AST"}}class dn extends hn{constructor(e,t,r){super(e,t),this.nameSpan=r}}class fn extends hn{constructor(e,t,r,n,i){super(e,t),this.prefix=r,this.uninterpretedExpression=n,this.location=i}visit(e,t=null){return e.visitQuote(this,t)}toString(){return"Quote"}}class En extends hn{visit(e,t=null){}}class Cn extends hn{visit(e,t=null){return e.visitImplicitReceiver(this,t)}}class vn extends Cn{visit(e,t=null){var r;return null===(r=e.visitThisReceiver)||void 0===r?void 0:r.call(e,this,t)}}class Fn extends hn{constructor(e,t,r){super(e,t),this.expressions=r}visit(e,t=null){return e.visitChain(this,t)}}class gn extends hn{constructor(e,t,r,n,i){super(e,t),this.condition=r,this.trueExp=n,this.falseExp=i}visit(e,t=null){return e.visitConditional(this,t)}}class mn extends dn{constructor(e,t,r,n,i){super(e,t,r),this.receiver=n,this.name=i}visit(e,t=null){return e.visitPropertyRead(this,t)}}class yn extends dn{constructor(e,t,r,n,i,s){super(e,t,r),this.receiver=n,this.name=i,this.value=s}visit(e,t=null){return e.visitPropertyWrite(this,t)}}class xn extends dn{constructor(e,t,r,n,i){super(e,t,r),this.receiver=n,this.name=i}visit(e,t=null){return e.visitSafePropertyRead(this,t)}}class An extends hn{constructor(e,t,r,n){super(e,t),this.obj=r,this.key=n}visit(e,t=null){return e.visitKeyedRead(this,t)}}class Sn extends hn{constructor(e,t,r,n,i){super(e,t),this.obj=r,this.key=n,this.value=i}visit(e,t=null){return e.visitKeyedWrite(this,t)}}class wn extends dn{constructor(e,t,r,n,i,s){super(e,t,s),this.exp=r,this.name=n,this.args=i}visit(e,t=null){return e.visitPipe(this,t)}}class In extends hn{constructor(e,t,r){super(e,t),this.value=r}visit(e,t=null){return e.visitLiteralPrimitive(this,t)}}class On extends hn{constructor(e,t,r){super(e,t),this.expressions=r}visit(e,t=null){return e.visitLiteralArray(this,t)}}class Nn extends hn{constructor(e,t,r,n){super(e,t),this.keys=r,this.values=n}visit(e,t=null){return e.visitLiteralMap(this,t)}}class bn extends hn{constructor(e,t,r,n){super(e,t),this.strings=r,this.expressions=n}visit(e,t=null){return e.visitInterpolation(this,t)}}class Bn extends hn{constructor(e,t,r,n,i){super(e,t),this.operation=r,this.left=n,this.right=i}visit(e,t=null){return e.visitBinary(this,t)}}class Pn extends Bn{constructor(e,t,r,n,i,s,u){super(e,t,i,s,u),this.operator=r,this.expr=n}static createMinus(e,t,r){return new Pn(e,t,"-",r,"-",new In(e,t,0),r)}static createPlus(e,t,r){return new Pn(e,t,"+",r,"-",r,new In(e,t,0))}visit(e,t=null){return void 0!==e.visitUnary?e.visitUnary(this,t):e.visitBinary(this,t)}}class Tn extends hn{constructor(e,t,r){super(e,t),this.expression=r}visit(e,t=null){return e.visitPrefixNot(this,t)}}class Ln extends hn{constructor(e,t,r){super(e,t),this.expression=r}visit(e,t=null){return e.visitNonNullAssert(this,t)}}class Rn extends dn{constructor(e,t,r,n,i,s,u){super(e,t,r),this.receiver=n,this.name=i,this.args=s,this.argumentSpan=u}visit(e,t=null){return e.visitMethodCall(this,t)}}class kn extends dn{constructor(e,t,r,n,i,s,u){super(e,t,r),this.receiver=n,this.name=i,this.args=s,this.argumentSpan=u}visit(e,t=null){return e.visitSafeMethodCall(this,t)}}class $n extends hn{constructor(e,t,r,n){super(e,t),this.target=r,this.args=n}visit(e,t=null){return e.visitFunctionCall(this,t)}}class Mn{constructor(e,t){this.start=e,this.end=t}}class _n extends hn{constructor(e,t,r,n,i){super(new pn(0,null===t?0:t.length),new Mn(n,null===t?n:n+t.length)),this.ast=e,this.source=t,this.location=r,this.errors=i}visit(e,t=null){return e.visitASTWithSource?e.visitASTWithSource(this,t):this.ast.visit(e,t)}toString(){return`${this.source} in ${this.location}`}}class jn{constructor(e,t,r){this.sourceSpan=e,this.key=t,this.value=r}}class Gn{constructor(e,t,r){this.sourceSpan=e,this.key=t,this.value=r}}class Kn{visit(e,t){e.visit(this,t)}visitUnary(e,t){this.visit(e.expr,t)}visitBinary(e,t){this.visit(e.left,t),this.visit(e.right,t)}visitChain(e,t){this.visitAll(e.expressions,t)}visitConditional(e,t){this.visit(e.condition,t),this.visit(e.trueExp,t),this.visit(e.falseExp,t)}visitPipe(e,t){this.visit(e.exp,t),this.visitAll(e.args,t)}visitFunctionCall(e,t){e.target&&this.visit(e.target,t),this.visitAll(e.args,t)}visitImplicitReceiver(e,t){}visitThisReceiver(e,t){}visitInterpolation(e,t){this.visitAll(e.expressions,t)}visitKeyedRead(e,t){this.visit(e.obj,t),this.visit(e.key,t)}visitKeyedWrite(e,t){this.visit(e.obj,t),this.visit(e.key,t),this.visit(e.value,t)}visitLiteralArray(e,t){this.visitAll(e.expressions,t)}visitLiteralMap(e,t){this.visitAll(e.values,t)}visitLiteralPrimitive(e,t){}visitMethodCall(e,t){this.visit(e.receiver,t),this.visitAll(e.args,t)}visitPrefixNot(e,t){this.visit(e.expression,t)}visitNonNullAssert(e,t){this.visit(e.expression,t)}visitPropertyRead(e,t){this.visit(e.receiver,t)}visitPropertyWrite(e,t){this.visit(e.receiver,t),this.visit(e.value,t)}visitSafePropertyRead(e,t){this.visit(e.receiver,t)}visitSafeMethodCall(e,t){this.visit(e.receiver,t),this.visitAll(e.args,t)}visitQuote(e,t){}visitAll(e,t){for(const r of e)this.visit(r,t)}}!function(e){e[e.DEFAULT=0]="DEFAULT",e[e.LITERAL_ATTR=1]="LITERAL_ATTR",e[e.ANIMATION=2]="ANIMATION"}(an||(an={}));var Un=Object.freeze({__proto__:null,ParserError:ln,ParseSpan:pn,AST:hn,ASTWithName:dn,Quote:fn,EmptyExpr:En,ImplicitReceiver:Cn,ThisReceiver:vn,Chain:Fn,Conditional:gn,PropertyRead:mn,PropertyWrite:yn,SafePropertyRead:xn,KeyedRead:An,KeyedWrite:Sn,BindingPipe:wn,LiteralPrimitive:In,LiteralArray:On,LiteralMap:Nn,Interpolation:bn,Binary:Bn,Unary:Pn,PrefixNot:Tn,NonNullAssert:Ln,MethodCall:Rn,SafeMethodCall:kn,FunctionCall:$n,AbsoluteSourceSpan:Mn,ASTWithSource:_n,VariableBinding:jn,ExpressionBinding:Gn,RecursiveAstVisitor:Kn,AstTransformer:class{visitImplicitReceiver(e,t){return e}visitThisReceiver(e,t){return e}visitInterpolation(e,t){return new bn(e.span,e.sourceSpan,e.strings,this.visitAll(e.expressions))}visitLiteralPrimitive(e,t){return new In(e.span,e.sourceSpan,e.value)}visitPropertyRead(e,t){return new mn(e.span,e.sourceSpan,e.nameSpan,e.receiver.visit(this),e.name)}visitPropertyWrite(e,t){return new yn(e.span,e.sourceSpan,e.nameSpan,e.receiver.visit(this),e.name,e.value.visit(this))}visitSafePropertyRead(e,t){return new xn(e.span,e.sourceSpan,e.nameSpan,e.receiver.visit(this),e.name)}visitMethodCall(e,t){return new Rn(e.span,e.sourceSpan,e.nameSpan,e.receiver.visit(this),e.name,this.visitAll(e.args),e.argumentSpan)}visitSafeMethodCall(e,t){return new kn(e.span,e.sourceSpan,e.nameSpan,e.receiver.visit(this),e.name,this.visitAll(e.args),e.argumentSpan)}visitFunctionCall(e,t){return new $n(e.span,e.sourceSpan,e.target.visit(this),this.visitAll(e.args))}visitLiteralArray(e,t){return new On(e.span,e.sourceSpan,this.visitAll(e.expressions))}visitLiteralMap(e,t){return new Nn(e.span,e.sourceSpan,e.keys,this.visitAll(e.values))}visitUnary(e,t){switch(e.operator){case"+":return Pn.createPlus(e.span,e.sourceSpan,e.expr.visit(this));case"-":return Pn.createMinus(e.span,e.sourceSpan,e.expr.visit(this));default:throw new Error(`Unknown unary operator ${e.operator}`)}}visitBinary(e,t){return new Bn(e.span,e.sourceSpan,e.operation,e.left.visit(this),e.right.visit(this))}visitPrefixNot(e,t){return new Tn(e.span,e.sourceSpan,e.expression.visit(this))}visitNonNullAssert(e,t){return new Ln(e.span,e.sourceSpan,e.expression.visit(this))}visitConditional(e,t){return new gn(e.span,e.sourceSpan,e.condition.visit(this),e.trueExp.visit(this),e.falseExp.visit(this))}visitPipe(e,t){return new wn(e.span,e.sourceSpan,e.exp.visit(this),e.name,this.visitAll(e.args),e.nameSpan)}visitKeyedRead(e,t){return new An(e.span,e.sourceSpan,e.obj.visit(this),e.key.visit(this))}visitKeyedWrite(e,t){return new Sn(e.span,e.sourceSpan,e.obj.visit(this),e.key.visit(this),e.value.visit(this))}visitAll(e){const t=[];for(let r=0;r<e.length;++r)t[r]=e[r].visit(this);return t}visitChain(e,t){return new Fn(e.span,e.sourceSpan,this.visitAll(e.expressions))}visitQuote(e,t){return new fn(e.span,e.sourceSpan,e.prefix,e.uninterpretedExpression,e.location)}},AstMemoryEfficientTransformer:class{visitImplicitReceiver(e,t){return e}visitThisReceiver(e,t){return e}visitInterpolation(e,t){const r=this.visitAll(e.expressions);return r!==e.expressions?new bn(e.span,e.sourceSpan,e.strings,r):e}visitLiteralPrimitive(e,t){return e}visitPropertyRead(e,t){const r=e.receiver.visit(this);return r!==e.receiver?new mn(e.span,e.sourceSpan,e.nameSpan,r,e.name):e}visitPropertyWrite(e,t){const r=e.receiver.visit(this),n=e.value.visit(this);return r!==e.receiver||n!==e.value?new yn(e.span,e.sourceSpan,e.nameSpan,r,e.name,n):e}visitSafePropertyRead(e,t){const r=e.receiver.visit(this);return r!==e.receiver?new xn(e.span,e.sourceSpan,e.nameSpan,r,e.name):e}visitMethodCall(e,t){const r=e.receiver.visit(this),n=this.visitAll(e.args);return r!==e.receiver||n!==e.args?new Rn(e.span,e.sourceSpan,e.nameSpan,r,e.name,n,e.argumentSpan):e}visitSafeMethodCall(e,t){const r=e.receiver.visit(this),n=this.visitAll(e.args);return r!==e.receiver||n!==e.args?new kn(e.span,e.sourceSpan,e.nameSpan,r,e.name,n,e.argumentSpan):e}visitFunctionCall(e,t){const r=e.target&&e.target.visit(this),n=this.visitAll(e.args);return r!==e.target||n!==e.args?new $n(e.span,e.sourceSpan,r,n):e}visitLiteralArray(e,t){const r=this.visitAll(e.expressions);return r!==e.expressions?new On(e.span,e.sourceSpan,r):e}visitLiteralMap(e,t){const r=this.visitAll(e.values);return r!==e.values?new Nn(e.span,e.sourceSpan,e.keys,r):e}visitUnary(e,t){const r=e.expr.visit(this);if(r!==e.expr)switch(e.operator){case"+":return Pn.createPlus(e.span,e.sourceSpan,r);case"-":return Pn.createMinus(e.span,e.sourceSpan,r);default:throw new Error(`Unknown unary operator ${e.operator}`)}return e}visitBinary(e,t){const r=e.left.visit(this),n=e.right.visit(this);return r!==e.left||n!==e.right?new Bn(e.span,e.sourceSpan,e.operation,r,n):e}visitPrefixNot(e,t){const r=e.expression.visit(this);return r!==e.expression?new Tn(e.span,e.sourceSpan,r):e}visitNonNullAssert(e,t){const r=e.expression.visit(this);return r!==e.expression?new Ln(e.span,e.sourceSpan,r):e}visitConditional(e,t){const r=e.condition.visit(this),n=e.trueExp.visit(this),i=e.falseExp.visit(this);return r!==e.condition||n!==e.trueExp||i!==e.falseExp?new gn(e.span,e.sourceSpan,r,n,i):e}visitPipe(e,t){const r=e.exp.visit(this),n=this.visitAll(e.args);return r!==e.exp||n!==e.args?new wn(e.span,e.sourceSpan,r,e.name,n,e.nameSpan):e}visitKeyedRead(e,t){const r=e.obj.visit(this),n=e.key.visit(this);return r!==e.obj||n!==e.key?new An(e.span,e.sourceSpan,r,n):e}visitKeyedWrite(e,t){const r=e.obj.visit(this),n=e.key.visit(this),i=e.value.visit(this);return r!==e.obj||n!==e.key||i!==e.value?new Sn(e.span,e.sourceSpan,r,n,i):e}visitAll(e){const t=[];let r=!1;for(let n=0;n<e.length;++n){const i=e[n],s=i.visit(this);t[n]=s,r=r||s!==i}return r?t:e}visitChain(e,t){const r=this.visitAll(e.expressions);return r!==e.expressions?new Fn(e.span,e.sourceSpan,r):e}visitQuote(e,t){return e}},ParsedProperty:class{constructor(e,t,r,n,i,s){this.name=e,this.expression=t,this.type=r,this.sourceSpan=n,this.keySpan=i,this.valueSpan=s,this.isLiteral=this.type===an.LITERAL_ATTR,this.isAnimation=this.type===an.ANIMATION}},get ParsedPropertyType(){return an},ParsedEvent:class{constructor(e,t,r,n,i,s,u){this.name=e,this.targetOrPhase=t,this.type=r,this.handler=n,this.sourceSpan=i,this.handlerSpan=s,this.keySpan=u}},ParsedVariable:class{constructor(e,t,r,n,i){this.name=e,this.value=t,this.sourceSpan=r,this.keySpan=n,this.valueSpan=i}},BoundElementProperty:class{constructor(e,t,r,n,i,s,u,a){this.name=e,this.type=t,this.securityContext=r,this.value=n,this.unit=i,this.sourceSpan=s,this.keySpan=u,this.valueSpan=a}}});
/**
	 * @license
	 * Copyright Google LLC All Rights Reserved.
	 *
	 * Use of this source code is governed by an MIT-style license that can be
	 * found in the LICENSE file at https://angular.io/license
	 */const Wn=41,Vn=46,Xn=125;function zn(e){return 48<=e&&e<=57}
/**
	 * @license
	 * Copyright Google LLC All Rights Reserved.
	 *
	 * Use of this source code is governed by an MIT-style license that can be
	 * found in the LICENSE file at https://angular.io/license
	 */
var qn;!function(e){e[e.Character=0]="Character",e[e.Identifier=1]="Identifier",e[e.PrivateIdentifier=2]="PrivateIdentifier",e[e.Keyword=3]="Keyword",e[e.String=4]="String",e[e.Operator=5]="Operator",e[e.Number=6]="Number",e[e.Error=7]="Error"}(qn||(qn={}));const Qn=["var","let","as","null","undefined","true","false","if","else","this"];class Yn{constructor(e,t,r,n,i){this.index=e,this.end=t,this.type=r,this.numValue=n,this.strValue=i}isCharacter(e){return this.type==qn.Character&&this.numValue==e}isNumber(){return this.type==qn.Number}isString(){return this.type==qn.String}isOperator(e){return this.type==qn.Operator&&this.strValue==e}isIdentifier(){return this.type==qn.Identifier}isPrivateIdentifier(){return this.type==qn.PrivateIdentifier}isKeyword(){return this.type==qn.Keyword}isKeywordLet(){return this.type==qn.Keyword&&"let"==this.strValue}isKeywordAs(){return this.type==qn.Keyword&&"as"==this.strValue}isKeywordNull(){return this.type==qn.Keyword&&"null"==this.strValue}isKeywordUndefined(){return this.type==qn.Keyword&&"undefined"==this.strValue}isKeywordTrue(){return this.type==qn.Keyword&&"true"==this.strValue}isKeywordFalse(){return this.type==qn.Keyword&&"false"==this.strValue}isKeywordThis(){return this.type==qn.Keyword&&"this"==this.strValue}isError(){return this.type==qn.Error}toNumber(){return this.type==qn.Number?this.numValue:-1}toString(){switch(this.type){case qn.Character:case qn.Identifier:case qn.Keyword:case qn.Operator:case qn.PrivateIdentifier:case qn.String:case qn.Error:return this.strValue;case qn.Number:return this.numValue.toString();default:return null}}}function Hn(e,t,r){return new Yn(e,t,qn.Character,r,String.fromCharCode(r))}function Jn(e,t,r){return new Yn(e,t,qn.Operator,0,r)}const Zn=new Yn(-1,-1,qn.Character,0,"");class ei{constructor(e){this.input=e,this.peek=0,this.index=-1,this.length=e.length,this.advance()}advance(){this.peek=++this.index>=this.length?0:this.input.charCodeAt(this.index)}scanToken(){const e=this.input,t=this.length;let r=this.peek,n=this.index;for(;r<=32;){if(++n>=t){r=0;break}r=e.charCodeAt(n)}if(this.peek=r,this.index=n,n>=t)return null;if(ti(r))return this.scanIdentifier();if(zn(r))return this.scanNumber(n);const i=n;switch(r){case Vn:return this.advance(),zn(this.peek)?this.scanNumber(i):Hn(i,this.index,Vn);case 40:case Wn:case 123:case Xn:case 91:case 93:case 44:case 58:case 59:return this.scanCharacter(i,r);case 39:case 34:return this.scanString();case 35:return this.scanPrivateIdentifier();case 43:case 45:case 42:case 47:case 37:case 94:return this.scanOperator(i,String.fromCharCode(r));case 63:return this.scanQuestion(i);case 60:case 62:return this.scanComplexOperator(i,String.fromCharCode(r),61,"=");case 33:case 61:return this.scanComplexOperator(i,String.fromCharCode(r),61,"=",61,"=");case 38:return this.scanComplexOperator(i,"&",38,"&");case 124:return this.scanComplexOperator(i,"|",124,"|");case 160:for(;(s=this.peek)>=9&&s<=32||160==s;)this.advance();return this.scanToken()}var s;return this.advance(),this.error(`Unexpected character [${String.fromCharCode(r)}]`,0)}scanCharacter(e,t){return this.advance(),Hn(e,this.index,t)}scanOperator(e,t){return this.advance(),Jn(e,this.index,t)}scanComplexOperator(e,t,r,n,i,s){this.advance();let u=t;return this.peek==r&&(this.advance(),u+=n),null!=i&&this.peek==i&&(this.advance(),u+=s),Jn(e,this.index,u)}scanIdentifier(){const e=this.index;for(this.advance();ni(this.peek);)this.advance();const t=this.input.substring(e,this.index);return Qn.indexOf(t)>-1?(r=e,n=this.index,i=t,new Yn(r,n,qn.Keyword,0,i)):function(e,t,r){return new Yn(e,t,qn.Identifier,0,r)}(e,this.index,t);var r,n,i}scanPrivateIdentifier(){const e=this.index;if(this.advance(),!ti(this.peek))return this.error("Invalid character [#]",-1);for(;ni(this.peek);)this.advance();const t=this.input.substring(e,this.index);return r=e,n=this.index,i=t,new Yn(r,n,qn.PrivateIdentifier,0,i);var r,n,i}scanNumber(e){let t=this.index===e;for(this.advance();;){if(zn(this.peek));else if(this.peek==Vn)t=!1;else{if(101!=(r=this.peek)&&69!=r)break;if(this.advance(),ii(this.peek)&&this.advance(),!zn(this.peek))return this.error("Invalid exponent",-1);t=!1}this.advance()}var r;const n=this.input.substring(e,this.index),i=t?function(e){const t=parseInt(e);if(isNaN(t))throw new Error("Invalid integer literal when parsing "+e);return t}(n):parseFloat(n);return s=e,u=this.index,a=i,new Yn(s,u,qn.Number,a,"");var s,u,a}scanString(){const e=this.index,t=this.peek;this.advance();let r="",n=this.index;const i=this.input;for(;this.peek!=t;)if(92==this.peek){let e;if(r+=i.substring(n,this.index),this.advance(),this.peek=this.peek,117==this.peek){const t=i.substring(this.index+1,this.index+5);if(!/^[0-9a-f]+$/i.test(t))return this.error(`Invalid unicode escape [\\u${t}]`,0);e=parseInt(t,16);for(let e=0;e<5;e++)this.advance()}else e=ui(this.peek),this.advance();r+=String.fromCharCode(e),n=this.index}else{if(0==this.peek)return this.error("Unterminated quote",0);this.advance()}const s=i.substring(n,this.index);return this.advance(),u=e,a=this.index,o=r+s,new Yn(u,a,qn.String,0,o);var u,a,o}scanQuestion(e){this.advance();let t="?";return 63!==this.peek&&this.peek!==Vn||(t+=this.peek===Vn?".":"?",this.advance()),Jn(e,this.index,t)}error(e,t){const r=this.index+t;return function(e,t,r){return new Yn(e,t,qn.Error,0,r)}(r,this.index,`Lexer Error: ${e} at column ${r} in expression [${this.input}]`)}}function ti(e){return 97<=e&&e<=122||65<=e&&e<=90||95==e||36==e}function ri(e){if(0==e.length)return!1;const t=new ei(e);if(!ti(t.peek))return!1;for(t.advance();0!==t.peek;){if(!ni(t.peek))return!1;t.advance()}return!0}function ni(e){return function(e){return e>=97&&e<=122||e>=65&&e<=90}(e)||zn(e)||95==e||36==e}function ii(e){return 45==e||43==e}function si(e){return 39===e||34===e||96===e}function ui(e){switch(e){case 110:return 10;case 102:return 12;case 114:return 13;case 116:return 9;case 118:return 11;default:return e}}var ai=Object.freeze({__proto__:null,get TokenType(){return qn},Lexer:class{tokenize(e){const t=new ei(e),r=[];let n=t.scanToken();for(;null!=n;)r.push(n),n=t.scanToken();return r}},Token:Yn,EOF:Zn,isIdentifier:ri,isQuote:si});
/**
	 * @license
	 * Copyright Google LLC All Rights Reserved.
	 *
	 * Use of this source code is governed by an MIT-style license that can be
	 * found in the LICENSE file at https://angular.io/license
	 */const oi=[/^\s*$/,/[<>]/,/^[{}]$/,/&(#|[a-z])/i,/^\/\//];
/**
	 * @license
	 * Copyright Google LLC All Rights Reserved.
	 *
	 * Use of this source code is governed by an MIT-style license that can be
	 * found in the LICENSE file at https://angular.io/license
	 */
class Di{constructor(e,t){this.start=e,this.end=t}static fromArray(e){return e?(function(e,t){if(!(null==t||Array.isArray(t)&&2==t.length))throw new Error(`Expected '${e}' to be an array, [start, end].`);if(null!=t){const e=t[0],r=t[1];oi.forEach((t=>{if(t.test(e)||t.test(r))throw new Error(`['${e}', '${r}'] contains unusable interpolation symbol.`)}))}}("interpolation",e),new Di(e[0],e[1])):ci}}const ci=new Di("{{","}}");
/**
	 * @license
	 * Copyright Google LLC All Rights Reserved.
	 *
	 * Use of this source code is governed by an MIT-style license that can be
	 * found in the LICENSE file at https://angular.io/license
	 */class li{constructor(e,t,r){this.strings=e,this.expressions=t,this.offsets=r}}class pi{constructor(e,t,r){this.templateBindings=e,this.warnings=t,this.errors=r}}class hi{constructor(e){this._lexer=e,this.errors=[],this.simpleExpressionChecker=Ei}parseAction(e,t,r,n=ci){this._checkNoInterpolation(e,t,n);const i=this._stripComments(e),s=this._lexer.tokenize(this._stripComments(e)),u=new fi(e,t,r,s,i.length,!0,this.errors,e.length-i.length).parseChain();return new _n(u,e,t,r,this.errors)}parseBinding(e,t,r,n=ci){const i=this._parseBindingAst(e,t,r,n);return new _n(i,e,t,r,this.errors)}checkSimpleExpression(e){const t=new this.simpleExpressionChecker;return e.visit(t),t.errors}parseSimpleBinding(e,t,r,n=ci){const i=this._parseBindingAst(e,t,r,n),s=this.checkSimpleExpression(i);return s.length>0&&this._reportError(`Host binding expression cannot contain ${s.join(" ")}`,e,t),new _n(i,e,t,r,this.errors)}_reportError(e,t,r,n){this.errors.push(new ln(e,t,r,n))}_parseBindingAst(e,t,r,n){const i=this._parseQuote(e,t,r);if(null!=i)return i;this._checkNoInterpolation(e,t,n);const s=this._stripComments(e),u=this._lexer.tokenize(s);return new fi(e,t,r,u,s.length,!1,this.errors,e.length-s.length).parseChain()}_parseQuote(e,t,r){if(null==e)return null;const n=e.indexOf(":");if(-1==n)return null;const i=e.substring(0,n).trim();if(!ri(i))return null;const s=e.substring(n+1),u=new pn(0,e.length);return new fn(u,u.toAbsolute(r),i,s,t)}parseTemplateBindings(e,t,r,n,i){const s=this._lexer.tokenize(t);return new fi(t,r,i,s,t.length,!1,this.errors,0).parseTemplateBindings({source:e,span:new Mn(n,n+e.length)})}parseInterpolation(e,t,r,n=ci){const{strings:i,expressions:s,offsets:u}=this.splitInterpolation(e,t,n);if(0===s.length)return null;const a=[];for(let n=0;n<s.length;++n){const i=s[n].text,o=this._stripComments(i),D=this._lexer.tokenize(o),c=new fi(e,t,r,D,o.length,!1,this.errors,u[n]+(i.length-o.length)).parseChain();a.push(c)}return this.createInterpolationAst(i.map((e=>e.text)),a,e,t,r)}parseInterpolationExpression(e,t,r){const n=this._stripComments(e),i=this._lexer.tokenize(n),s=new fi(e,t,r,i,n.length,!1,this.errors,0).parseChain();return this.createInterpolationAst(["",""],[s],e,t,r)}createInterpolationAst(e,t,r,n,i){const s=new pn(0,r.length),u=new bn(s,s.toAbsolute(i),e,t);return new _n(u,r,n,i,this.errors)}splitInterpolation(e,t,r=ci){const n=[],i=[],s=[];let u=0,a=!1,o=!1,{start:D,end:c}=r;for(;u<e.length;)if(a){const r=u,n=r+D.length,l=this._getInterpolationEndIndex(e,c,n);if(-1===l){a=!1,o=!0;break}const p=l+c.length,h=e.substring(n,l);0===h.trim().length&&this._reportError("Blank expressions are not allowed in interpolated strings",e,`at column ${u} in`,t),i.push({text:h,start:r,end:p}),s.push(n),u=p,a=!1}else{const t=u;u=e.indexOf(D,u),-1===u&&(u=e.length);const r=e.substring(t,u);n.push({text:r,start:t,end:u}),a=!0}if(!a)if(o){const t=n[n.length-1];t.text+=e.substring(u),t.end=e.length}else n.push({text:e.substring(u),start:u,end:e.length});return new li(n,i,s)}wrapLiteralPrimitive(e,t,r){const n=new pn(0,null==e?0:e.length);return new _n(new In(n,n.toAbsolute(r),e),e,t,r,this.errors)}_stripComments(e){const t=this._commentStart(e);return null!=t?e.substring(0,t).trim():e}_commentStart(e){let t=null;for(let r=0;r<e.length-1;r++){const n=e.charCodeAt(r),i=e.charCodeAt(r+1);if(47===n&&47==i&&null==t)return r;t===n?t=null:null==t&&si(n)&&(t=n)}return null}_checkNoInterpolation(e,t,{start:r,end:n}){let i=-1,s=-1;for(const t of this._forEachUnquotedChar(e,0))if(-1===i)e.startsWith(r)&&(i=t);else if(s=this._getInterpolationEndIndex(e,n,t),s>-1)break;i>-1&&s>-1&&this._reportError(`Got interpolation (${r}${n}) where expression was expected`,e,`at column ${i} in`,t)}_getInterpolationEndIndex(e,t,r){for(const n of this._forEachUnquotedChar(e,r)){if(e.startsWith(t,n))return n;if(e.startsWith("//",n))return e.indexOf(t,n)}return-1}*_forEachUnquotedChar(e,t){let r=null,n=0;for(let i=t;i<e.length;i++){const t=e[i];!si(e.charCodeAt(i))||null!==r&&r!==t||n%2!=0?null===r&&(yield i):r=null===r?t:null,n="\\"===t?n+1:0}}}var di;!function(e){e[e.None=0]="None",e[e.Writable=1]="Writable"}(di||(di={}));class fi{constructor(e,t,r,n,i,s,u,a){this.input=e,this.location=t,this.absoluteOffset=r,this.tokens=n,this.inputLength=i,this.parseAction=s,this.errors=u,this.offset=a,this.rparensExpected=0,this.rbracketsExpected=0,this.rbracesExpected=0,this.context=di.None,this.sourceSpanCache=new Map,this.index=0}peek(e){const t=this.index+e;return t<this.tokens.length?this.tokens[t]:Zn}get next(){return this.peek(0)}get atEOF(){return this.index>=this.tokens.length}get inputIndex(){return this.atEOF?this.currentEndIndex:this.next.index+this.offset}get currentEndIndex(){if(this.index>0){return this.peek(-1).end+this.offset}return 0===this.tokens.length?this.inputLength+this.offset:this.next.index+this.offset}get currentAbsoluteOffset(){return this.absoluteOffset+this.inputIndex}span(e,t){let r=this.currentEndIndex;if(void 0!==t&&t>this.currentEndIndex&&(r=t),e>r){const t=r;r=e,e=t}return new pn(e,r)}sourceSpan(e,t){const r=`${e}@${this.inputIndex}:${t}`;return this.sourceSpanCache.has(r)||this.sourceSpanCache.set(r,this.span(e,t).toAbsolute(this.absoluteOffset)),this.sourceSpanCache.get(r)}advance(){this.index++}withContext(e,t){this.context|=e;const r=t();return this.context^=e,r}consumeOptionalCharacter(e){return!!this.next.isCharacter(e)&&(this.advance(),!0)}peekKeywordLet(){return this.next.isKeywordLet()}peekKeywordAs(){return this.next.isKeywordAs()}expectCharacter(e){this.consumeOptionalCharacter(e)||this.error(`Missing expected ${String.fromCharCode(e)}`)}consumeOptionalOperator(e){return!!this.next.isOperator(e)&&(this.advance(),!0)}expectOperator(e){this.consumeOptionalOperator(e)||this.error(`Missing expected operator ${e}`)}prettyPrintToken(e){return e===Zn?"end of input":`token ${e}`}expectIdentifierOrKeyword(){const e=this.next;return e.isIdentifier()||e.isKeyword()?(this.advance(),e.toString()):(e.isPrivateIdentifier()?this._reportErrorForPrivateIdentifier(e,"expected identifier or keyword"):this.error(`Unexpected ${this.prettyPrintToken(e)}, expected identifier or keyword`),null)}expectIdentifierOrKeywordOrString(){const e=this.next;return e.isIdentifier()||e.isKeyword()||e.isString()?(this.advance(),e.toString()):(e.isPrivateIdentifier()?this._reportErrorForPrivateIdentifier(e,"expected identifier, keyword or string"):this.error(`Unexpected ${this.prettyPrintToken(e)}, expected identifier, keyword, or string`),"")}parseChain(){const e=[],t=this.inputIndex;for(;this.index<this.tokens.length;){const t=this.parsePipe();if(e.push(t),this.consumeOptionalCharacter(59))for(this.parseAction||this.error("Binding expression cannot contain chained expression");this.consumeOptionalCharacter(59););else this.index<this.tokens.length&&this.error(`Unexpected token '${this.next}'`)}if(0==e.length){const e=this.offset,t=this.offset+this.inputLength;return new En(this.span(e,t),this.sourceSpan(e,t))}return 1==e.length?e[0]:new Fn(this.span(t),this.sourceSpan(t),e)}parsePipe(){const e=this.inputIndex;let t=this.parseExpression();if(this.consumeOptionalOperator("|")){this.parseAction&&this.error("Cannot have a pipe in an action expression");do{const r=this.inputIndex;let n,i,s=this.expectIdentifierOrKeyword();null!==s?n=this.sourceSpan(r):(s="",i=-1!==this.next.index?this.next.index:this.inputLength+this.offset,n=new pn(i,i).toAbsolute(this.absoluteOffset));const u=[];for(;this.consumeOptionalCharacter(58);)u.push(this.parseExpression());t=new wn(this.span(e),this.sourceSpan(e,i),t,s,u,n)}while(this.consumeOptionalOperator("|"))}return t}parseExpression(){return this.parseConditional()}parseConditional(){const e=this.inputIndex,t=this.parseLogicalOr();if(this.consumeOptionalOperator("?")){const r=this.parsePipe();let n;if(this.consumeOptionalCharacter(58))n=this.parsePipe();else{const t=this.inputIndex,r=this.input.substring(e,t);this.error(`Conditional expression ${r} requires all 3 expressions`),n=new En(this.span(e),this.sourceSpan(e))}return new gn(this.span(e),this.sourceSpan(e),t,r,n)}return t}parseLogicalOr(){const e=this.inputIndex;let t=this.parseLogicalAnd();for(;this.consumeOptionalOperator("||");){const r=this.parseLogicalAnd();t=new Bn(this.span(e),this.sourceSpan(e),"||",t,r)}return t}parseLogicalAnd(){const e=this.inputIndex;let t=this.parseNullishCoalescing();for(;this.consumeOptionalOperator("&&");){const r=this.parseNullishCoalescing();t=new Bn(this.span(e),this.sourceSpan(e),"&&",t,r)}return t}parseNullishCoalescing(){const e=this.inputIndex;let t=this.parseEquality();for(;this.consumeOptionalOperator("??");){const r=this.parseEquality();t=new Bn(this.span(e),this.sourceSpan(e),"??",t,r)}return t}parseEquality(){const e=this.inputIndex;let t=this.parseRelational();for(;this.next.type==qn.Operator;){const r=this.next.strValue;switch(r){case"==":case"===":case"!=":case"!==":this.advance();const n=this.parseRelational();t=new Bn(this.span(e),this.sourceSpan(e),r,t,n);continue}break}return t}parseRelational(){const e=this.inputIndex;let t=this.parseAdditive();for(;this.next.type==qn.Operator;){const r=this.next.strValue;switch(r){case"<":case">":case"<=":case">=":this.advance();const n=this.parseAdditive();t=new Bn(this.span(e),this.sourceSpan(e),r,t,n);continue}break}return t}parseAdditive(){const e=this.inputIndex;let t=this.parseMultiplicative();for(;this.next.type==qn.Operator;){const r=this.next.strValue;switch(r){case"+":case"-":this.advance();let n=this.parseMultiplicative();t=new Bn(this.span(e),this.sourceSpan(e),r,t,n);continue}break}return t}parseMultiplicative(){const e=this.inputIndex;let t=this.parsePrefix();for(;this.next.type==qn.Operator;){const r=this.next.strValue;switch(r){case"*":case"%":case"/":this.advance();let n=this.parsePrefix();t=new Bn(this.span(e),this.sourceSpan(e),r,t,n);continue}break}return t}parsePrefix(){if(this.next.type==qn.Operator){const e=this.inputIndex;let t;switch(this.next.strValue){case"+":return this.advance(),t=this.parsePrefix(),Pn.createPlus(this.span(e),this.sourceSpan(e),t);case"-":return this.advance(),t=this.parsePrefix(),Pn.createMinus(this.span(e),this.sourceSpan(e),t);case"!":return this.advance(),t=this.parsePrefix(),new Tn(this.span(e),this.sourceSpan(e),t)}}return this.parseCallChain()}parseCallChain(){const e=this.inputIndex;let t=this.parsePrimary();for(;;)if(this.consumeOptionalCharacter(Vn))t=this.parseAccessMemberOrMethodCall(t,e,!1);else if(this.consumeOptionalOperator("?."))t=this.parseAccessMemberOrMethodCall(t,e,!0);else if(this.consumeOptionalCharacter(91))this.withContext(di.Writable,(()=>{this.rbracketsExpected++;const r=this.parsePipe();if(r instanceof En&&this.error("Key access cannot be empty"),this.rbracketsExpected--,this.expectCharacter(93),this.consumeOptionalOperator("=")){const n=this.parseConditional();t=new Sn(this.span(e),this.sourceSpan(e),t,r,n)}else t=new An(this.span(e),this.sourceSpan(e),t,r)}));else if(this.consumeOptionalCharacter(40)){this.rparensExpected++;const r=this.parseCallArguments();this.rparensExpected--,this.expectCharacter(Wn),t=new $n(this.span(e),this.sourceSpan(e),t,r)}else{if(!this.consumeOptionalOperator("!"))return t;t=new Ln(this.span(e),this.sourceSpan(e),t)}}parsePrimary(){const e=this.inputIndex;if(this.consumeOptionalCharacter(40)){this.rparensExpected++;const e=this.parsePipe();return this.rparensExpected--,this.expectCharacter(Wn),e}if(this.next.isKeywordNull())return this.advance(),new In(this.span(e),this.sourceSpan(e),null);if(this.next.isKeywordUndefined())return this.advance(),new In(this.span(e),this.sourceSpan(e),void 0);if(this.next.isKeywordTrue())return this.advance(),new In(this.span(e),this.sourceSpan(e),!0);if(this.next.isKeywordFalse())return this.advance(),new In(this.span(e),this.sourceSpan(e),!1);if(this.next.isKeywordThis())return this.advance(),new vn(this.span(e),this.sourceSpan(e));if(this.consumeOptionalCharacter(91)){this.rbracketsExpected++;const t=this.parseExpressionList(93);return this.rbracketsExpected--,this.expectCharacter(93),new On(this.span(e),this.sourceSpan(e),t)}if(this.next.isCharacter(123))return this.parseLiteralMap();if(this.next.isIdentifier())return this.parseAccessMemberOrMethodCall(new Cn(this.span(e),this.sourceSpan(e)),e,!1);if(this.next.isNumber()){const t=this.next.toNumber();return this.advance(),new In(this.span(e),this.sourceSpan(e),t)}if(this.next.isString()){const t=this.next.toString();return this.advance(),new In(this.span(e),this.sourceSpan(e),t)}return this.next.isPrivateIdentifier()?(this._reportErrorForPrivateIdentifier(this.next,null),new En(this.span(e),this.sourceSpan(e))):this.index>=this.tokens.length?(this.error(`Unexpected end of expression: ${this.input}`),new En(this.span(e),this.sourceSpan(e))):(this.error(`Unexpected token ${this.next}`),new En(this.span(e),this.sourceSpan(e)))}parseExpressionList(e){const t=[];do{if(this.next.isCharacter(e))break;t.push(this.parsePipe())}while(this.consumeOptionalCharacter(44));return t}parseLiteralMap(){const e=[],t=[],r=this.inputIndex;if(this.expectCharacter(123),!this.consumeOptionalCharacter(Xn)){this.rbracesExpected++;do{const r=this.next.isString(),n=this.expectIdentifierOrKeywordOrString();e.push({key:n,quoted:r}),this.expectCharacter(58),t.push(this.parsePipe())}while(this.consumeOptionalCharacter(44));this.rbracesExpected--,this.expectCharacter(Xn)}return new Nn(this.span(r),this.sourceSpan(r),e,t)}parseAccessMemberOrMethodCall(e,t,r=!1){const n=this.inputIndex,i=this.withContext(di.Writable,(()=>{var t;const r=null!==(t=this.expectIdentifierOrKeyword())&&void 0!==t?t:"";return 0===r.length&&this.error("Expected identifier for property access",e.span.end),r})),s=this.sourceSpan(n);if(this.consumeOptionalCharacter(40)){const n=this.inputIndex;this.rparensExpected++;const u=this.parseCallArguments(),a=this.span(n,this.inputIndex).toAbsolute(this.absoluteOffset);this.expectCharacter(Wn),this.rparensExpected--;const o=this.span(t),D=this.sourceSpan(t);return r?new kn(o,D,s,e,i,u,a):new Rn(o,D,s,e,i,u,a)}if(r)return this.consumeOptionalOperator("=")?(this.error("The '?.' operator cannot be used in the assignment"),new En(this.span(t),this.sourceSpan(t))):new xn(this.span(t),this.sourceSpan(t),s,e,i);if(this.consumeOptionalOperator("=")){if(!this.parseAction)return this.error("Bindings cannot contain assignments"),new En(this.span(t),this.sourceSpan(t));const r=this.parseConditional();return new yn(this.span(t),this.sourceSpan(t),s,e,i,r)}return new mn(this.span(t),this.sourceSpan(t),s,e,i)}parseCallArguments(){if(this.next.isCharacter(Wn))return[];const e=[];do{e.push(this.parsePipe())}while(this.consumeOptionalCharacter(44));return e}expectTemplateBindingKey(){let e="",t=!1;const r=this.currentAbsoluteOffset;do{e+=this.expectIdentifierOrKeywordOrString(),t=this.consumeOptionalOperator("-"),t&&(e+="-")}while(t);return{source:e,span:new Mn(r,r+e.length)}}parseTemplateBindings(e){const t=[];for(t.push(...this.parseDirectiveKeywordBindings(e));this.index<this.tokens.length;){const r=this.parseLetBinding();if(r)t.push(r);else{const r=this.expectTemplateBindingKey(),n=this.parseAsBinding(r);n?t.push(n):(r.source=e.source+r.source.charAt(0).toUpperCase()+r.source.substring(1),t.push(...this.parseDirectiveKeywordBindings(r)))}this.consumeStatementTerminator()}return new pi(t,[],this.errors)}parseDirectiveKeywordBindings(e){const t=[];this.consumeOptionalCharacter(58);const r=this.getDirectiveBoundTarget();let n=this.currentAbsoluteOffset;const i=this.parseAsBinding(e);i||(this.consumeStatementTerminator(),n=this.currentAbsoluteOffset);const s=new Mn(e.span.start,n);return t.push(new Gn(s,e,r)),i&&t.push(i),t}getDirectiveBoundTarget(){if(this.next===Zn||this.peekKeywordAs()||this.peekKeywordLet())return null;const e=this.parsePipe(),{start:t,end:r}=e.span,n=this.input.substring(t,r);return new _n(e,n,this.location,this.absoluteOffset+t,this.errors)}parseAsBinding(e){if(!this.peekKeywordAs())return null;this.advance();const t=this.expectTemplateBindingKey();this.consumeStatementTerminator();const r=new Mn(e.span.start,this.currentAbsoluteOffset);return new jn(r,t,e)}parseLetBinding(){if(!this.peekKeywordLet())return null;const e=this.currentAbsoluteOffset;this.advance();const t=this.expectTemplateBindingKey();let r=null;this.consumeOptionalOperator("=")&&(r=this.expectTemplateBindingKey()),this.consumeStatementTerminator();const n=new Mn(e,this.currentAbsoluteOffset);return new jn(n,t,r)}consumeStatementTerminator(){this.consumeOptionalCharacter(59)||this.consumeOptionalCharacter(44)}error(e,t=null){this.errors.push(new ln(e,this.input,this.locationText(t),this.location)),this.skip()}locationText(e=null){return null==e&&(e=this.index),e<this.tokens.length?`at column ${this.tokens[e].index+1} in`:"at the end of the expression"}_reportErrorForPrivateIdentifier(e,t){let r=`Private identifiers are not supported. Unexpected private identifier: ${e}`;null!==t&&(r+=`, ${t}`),this.error(r)}skip(){let e=this.next;for(;!(!(this.index<this.tokens.length)||e.isCharacter(59)||e.isOperator("|")||!(this.rparensExpected<=0)&&e.isCharacter(Wn)||!(this.rbracesExpected<=0)&&e.isCharacter(Xn)||!(this.rbracketsExpected<=0)&&e.isCharacter(93)||this.context&di.Writable&&e.isOperator("="));)this.next.isError()&&this.errors.push(new ln(this.next.toString(),this.input,this.locationText(),this.location)),this.advance(),e=this.next}}class Ei{constructor(){this.errors=[]}visitImplicitReceiver(e,t){}visitThisReceiver(e,t){}visitInterpolation(e,t){}visitLiteralPrimitive(e,t){}visitPropertyRead(e,t){}visitPropertyWrite(e,t){}visitSafePropertyRead(e,t){}visitMethodCall(e,t){}visitSafeMethodCall(e,t){}visitFunctionCall(e,t){}visitLiteralArray(e,t){this.visitAll(e.expressions,t)}visitLiteralMap(e,t){this.visitAll(e.values,t)}visitUnary(e,t){}visitBinary(e,t){}visitPrefixNot(e,t){}visitNonNullAssert(e,t){}visitConditional(e,t){}visitPipe(e,t){this.errors.push("pipes")}visitKeyedRead(e,t){}visitKeyedWrite(e,t){}visitAll(e,t){return e.map((e=>e.visit(this,t)))}visitChain(e,t){}visitQuote(e,t){}}class Ci extends Kn{constructor(){super(...arguments),this.errors=[]}visitPipe(){this.errors.push("pipes")}}var vi=Object.freeze({__proto__:null,SplitInterpolation:li,TemplateBindingParseResult:pi,Parser:hi,IvyParser:class extends hi{constructor(){super(...arguments),this.simpleExpressionChecker=Ci}},_ParseAST:fi}),Fi=l((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getLast=t.toLowerCamelCase=t.findBackChar=t.findFrontChar=t.fitSpans=t.getNgType=t.parseNgInterpolation=t.parseNgTemplateBindings=t.parseNgAction=t.parseNgSimpleBinding=t.parseNgBinding=t.NG_PARSE_TEMPLATE_BINDINGS_FAKE_PREFIX=void 0;const r="angular-estree-parser";t.NG_PARSE_TEMPLATE_BINDINGS_FAKE_PREFIX="NgEstreeParser";const n=[r,0];function i(){return new vi.Parser(new ai.Lexer)}function s(e,t){const r=i(),{astInput:n,comments:s}=o(e,r),{ast:u,errors:D}=t(n,r);return a(D),{ast:u,comments:s}}function u(e,t){if(e&&"object"==typeof e){if(Array.isArray(e))return e.forEach((e=>u(e,t)));for(const r of Object.keys(e)){const n=e[r];"span"===r?t(n):u(n,t)}}}function a(e){if(0!==e.length){const[{message:t}]=e;throw new SyntaxError(t.replace(/^Parser Error: | at column \d+ in [^]*$/g,""))}}function o(e,t){const r=t._commentStart(e);return null===r?{astInput:e,comments:[]}:{astInput:e.slice(0,r),comments:[{type:"Comment",value:e.slice(r+"//".length),span:{start:r,end:e.length}}]}}function D({start:e,end:t},r){let n=e,i=t;for(;i!==n&&/\s/.test(r[i-1]);)i--;for(;n!==i&&/\s/.test(r[n]);)n++;return{start:n,end:i}}function c({start:e,end:t},r){let n=e,i=t;for(;i!==r.length&&/\s/.test(r[i]);)i++;for(;0!==n&&/\s/.test(r[n-1]);)n--;return{start:n,end:i}}function l(e,t){return"("===t[e.start-1]&&")"===t[e.end]?{start:e.start-1,end:e.end+1}:e}t.parseNgBinding=function(e){return s(e,((e,t)=>t.parseBinding(e,...n)))},t.parseNgSimpleBinding=function(e){return s(e,((e,t)=>t.parseSimpleBinding(e,...n)))},t.parseNgAction=function(e){return s(e,((e,t)=>t.parseAction(e,...n)))},t.parseNgTemplateBindings=function(e){const n=i(),{templateBindings:s,errors:u}=n.parseTemplateBindings(t.NG_PARSE_TEMPLATE_BINDINGS_FAKE_PREFIX,e,r,0,0);return a(u),s},t.parseNgInterpolation=function(e){const t=i(),{astInput:r,comments:s}=o(e,t),D="{{",{ast:c,errors:l}=t.parseInterpolation(D+r+"}}",...n);a(l);const p=c.expressions[0],h=new Set;return u(p,(e=>{h.has(e)||(e.start-=D.length,e.end-=D.length,h.add(e))})),{ast:p,comments:s}},t.getNgType=function(e){return Un.Unary&&e instanceof Un.Unary?"Unary":e instanceof Un.Binary?"Binary":e instanceof Un.BindingPipe?"BindingPipe":e instanceof Un.Chain?"Chain":e instanceof Un.Conditional?"Conditional":e instanceof Un.EmptyExpr?"EmptyExpr":e instanceof Un.FunctionCall?"FunctionCall":e instanceof Un.ImplicitReceiver?"ImplicitReceiver":e instanceof Un.KeyedRead?"KeyedRead":e instanceof Un.KeyedWrite?"KeyedWrite":e instanceof Un.LiteralArray?"LiteralArray":e instanceof Un.LiteralMap?"LiteralMap":e instanceof Un.LiteralPrimitive?"LiteralPrimitive":e instanceof Un.MethodCall?"MethodCall":e instanceof Un.NonNullAssert?"NonNullAssert":e instanceof Un.PrefixNot?"PrefixNot":e instanceof Un.PropertyRead?"PropertyRead":e instanceof Un.PropertyWrite?"PropertyWrite":e instanceof Un.Quote?"Quote":e instanceof Un.SafeMethodCall?"SafeMethodCall":e instanceof Un.SafePropertyRead?"SafePropertyRead":e.type},t.fitSpans=function(e,t,r){let n=0;const i={start:e.start,end:e.end};for(;;){const e=c(i,t),r=l(e,t);if(e.start===r.start&&e.end===r.end)break;i.start=r.start,i.end=r.end,n++}return{hasParens:0!==(r?n-1:n),outerSpan:D(r?{start:i.start+1,end:i.end-1}:i,t),innerSpan:D(e,t)}},t.findFrontChar=function(e,t,r){let n=t;for(;!e.test(r[n]);)if(--n<0)throw new Error(`Cannot find front char ${e} from index ${t} in ${JSON.stringify(r)}`);return n},t.findBackChar=function(e,t,r){let n=t;for(;!e.test(r[n]);)if(++n>=r.length)throw new Error(`Cannot find back char ${e} from index ${t} in ${JSON.stringify(r)}`);return n},t.toLowerCamelCase=function(e){return e.slice(0,1).toLowerCase()+e.slice(1)},t.getLast=function(e){return 0===e.length?void 0:e[e.length-1]}})),gi=l((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.transformSpan=t.transform=void 0;function r(e,t,r=!1,n=!1){if(!r){const{start:r,end:n}=e;return{start:r,end:n,loc:{start:t.locator.locationForIndex(r),end:t.locator.locationForIndex(n)}}}const{outerSpan:i,innerSpan:s,hasParens:u}=Fi.fitSpans(e,t.text,n);return Object.assign({start:s.start,end:s.end,loc:{start:t.locator.locationForIndex(s.start),end:t.locator.locationForIndex(s.end)}},u&&{extra:{parenthesized:!0,parenStart:i.start,parenEnd:i.end}})}t.transform=(e,n,i=!1)=>{const s=Fi.getNgType(e);switch(s){case"Unary":{const{operator:t,expr:r}=e;return o("UnaryExpression",{prefix:!0,argument:u(r),operator:t},e.span,{hasParentParens:i})}case"Binary":{const{left:t,operation:r,right:n}=e,s=n.span.start===n.span.end,a=t.span.start===t.span.end;if(s||a){const r=t.span.start===t.span.end?u(n):u(t);return o("UnaryExpression",{prefix:!0,argument:r,operator:s?"+":"-"},{start:e.span.start,end:E(r)},{hasParentParens:i})}const D=u(t),c=u(n);return o("&&"===r||"||"===r?"LogicalExpression":"BinaryExpression",{left:D,right:c,operator:r},{start:f(D),end:E(c)},{hasParentParens:i})}case"BindingPipe":{const{exp:t,name:r,args:n}=e,s=u(t),a=l(/\S/,l(/\|/,E(s))+1),D=o("Identifier",{name:r},{start:a,end:a+r.length}),c=n.map(u);return o("NGPipeExpression",{left:s,right:D,arguments:c},{start:f(s),end:E(0===c.length?D:Fi.getLast(c))},{hasParentParens:i})}case"Chain":{const{expressions:t}=e;return o("NGChainedExpression",{expressions:t.map(u)},e.span,{hasParentParens:i})}case"Comment":{const{value:t}=e;return o("CommentLine",{value:t},e.span,{processSpan:!1})}case"Conditional":{const{condition:t,trueExp:r,falseExp:n}=e,s=u(t),a=u(r),D=u(n);return o("ConditionalExpression",{test:s,consequent:a,alternate:D},{start:f(s),end:E(D)},{hasParentParens:i})}case"EmptyExpr":return o("NGEmptyExpression",{},e.span,{hasParentParens:i});case"FunctionCall":{const{target:t,args:r}=e,n=1===r.length?[a(r[0])]:r.map(u),s=u(t);return o("CallExpression",{callee:s,arguments:n},{start:f(s),end:e.span.end},{hasParentParens:i})}case"ImplicitReceiver":return o("ThisExpression",{},e.span,{hasParentParens:i});case"KeyedRead":{const{obj:t,key:r}=e;return D(t,u(r),{computed:!0,optional:!1},{end:e.span.end,hasParentParens:i})}case"LiteralArray":{const{expressions:t}=e;return o("ArrayExpression",{elements:t.map(u)},e.span,{hasParentParens:i})}case"LiteralMap":{const{keys:t,values:r}=e,n=r.map((e=>u(e))),s=t.map((({key:t,quoted:r},i)=>{const s=n[i],u={start:l(/\S/,0===i?e.span.start+1:l(/,/,E(n[i-1]))+1),end:c(/\S/,c(/:/,f(s)-1)-1)+1},a=r?o("StringLiteral",{value:t},u):o("Identifier",{name:t},u);return o("ObjectProperty",{key:a,value:s,method:!1,shorthand:!1,computed:!1},{start:f(a),end:E(s)})}));return o("ObjectExpression",{properties:s},e.span,{hasParentParens:i})}case"LiteralPrimitive":{const{value:t}=e;switch(typeof t){case"boolean":return o("BooleanLiteral",{value:t},e.span,{hasParentParens:i});case"number":return o("NumericLiteral",{value:t},e.span,{hasParentParens:i});case"object":return o("NullLiteral",{},e.span,{hasParentParens:i});case"string":return o("StringLiteral",{value:t},e.span,{hasParentParens:i});case"undefined":return o("Identifier",{name:"undefined"},e.span,{hasParentParens:i});default:throw new Error("Unexpected LiteralPrimitive value type "+typeof t)}}case"MethodCall":case"SafeMethodCall":{const t="SafeMethodCall"===s,{receiver:r,name:n,args:l}=e,p=1===l.length?[a(l[0])]:l.map(u),d=c(/\S/,c(/\(/,(0===p.length?c(/\)/,e.span.end-1):f(p[0]))-1)-1)+1,E=D(r,o("Identifier",{name:n},{start:d-n.length,end:d}),{computed:!1,optional:t}),C=h(E);return o(t||C?"OptionalCallExpression":"CallExpression",{callee:E,arguments:p},{start:f(E),end:e.span.end},{hasParentParens:i})}case"NonNullAssert":{const{expression:t}=e,r=u(t);return o("TSNonNullExpression",{expression:r},{start:f(r),end:e.span.end},{hasParentParens:i})}case"PrefixNot":{const{expression:t}=e,r=u(t);return o("UnaryExpression",{prefix:!0,operator:"!",argument:r},{start:e.span.start,end:E(r)},{hasParentParens:i})}case"PropertyRead":case"SafePropertyRead":{const t="SafePropertyRead"===s,{receiver:r,name:n}=e,u=c(/\S/,e.span.end-1)+1;return D(r,o("Identifier",{name:n},{start:u-n.length,end:u},p(r)?{hasParentParens:i}:{}),{computed:!1,optional:t},{hasParentParens:i})}case"KeyedWrite":{const{obj:t,key:r,value:n}=e,s=u(r),a=u(n),c=D(t,s,{computed:!0,optional:!1},{end:l(/\]/,E(s))+1});return o("AssignmentExpression",{left:c,operator:"=",right:a},{start:f(c),end:E(a)},{hasParentParens:i})}case"PropertyWrite":{const{receiver:t,name:r,value:n}=e,s=u(n),a=c(/\S/,c(/=/,f(s)-1)-1)+1,l=D(t,o("Identifier",{name:r},{start:a-r.length,end:a}),{computed:!1,optional:!1});return o("AssignmentExpression",{left:l,operator:"=",right:s},{start:f(l),end:E(s)},{hasParentParens:i})}case"Quote":{const{prefix:t,uninterpretedExpression:r}=e;return o("NGQuotedExpression",{prefix:t,value:r},e.span,{hasParentParens:i})}default:throw new Error(`Unexpected node ${s}`)}function u(e){return t.transform(e,n)}function a(e){return t.transform(e,n,!0)}function o(e,t,i,{processSpan:s=!0,hasParentParens:u=!1}={}){const a=Object.assign(Object.assign({type:e},r(i,n,s,u)),t);switch(e){case"Identifier":{const e=a;e.loc.identifierName=e.name;break}case"NumericLiteral":{const e=a;e.extra=Object.assign(Object.assign({},e.extra),{raw:n.text.slice(e.start,e.end),rawValue:e.value});break}case"StringLiteral":{const e=a;e.extra=Object.assign(Object.assign({},e.extra),{raw:n.text.slice(e.start,e.end),rawValue:e.value});break}}return a}function D(e,t,r,{end:n=E(t),hasParentParens:i=!1}={}){if(p(e))return t;const s=u(e),a=h(s);return o(r.optional||a?"OptionalMemberExpression":"MemberExpression",Object.assign({object:s,property:t,computed:r.computed},r.optional?{optional:!0}:a?{optional:!1}:null),{start:f(s),end:n},{hasParentParens:i})}function c(e,t){return Fi.findFrontChar(e,t,n.text)}function l(e,t){return Fi.findBackChar(e,t,n.text)}function p(e){return e.span.start>=e.span.end||/^\s+$/.test(n.text.slice(e.span.start,e.span.end))}function h(e){return("OptionalCallExpression"===e.type||"OptionalMemberExpression"===e.type)&&!d(e)}function d(e){return e.extra&&e.extra.parenthesized}function f(e){return d(e)?e.extra.parenStart:e.start}function E(e){return d(e)?e.extra.parenEnd:e.end}},t.transformSpan=r})),mi=l((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.transformTemplateBindings=void 0,t.transformTemplateBindings=function(e,t){e.forEach((function(e){h(e.key.span),p(e)&&e.value&&h(e.value.span)}));const[r]=e,{key:n}=r,i=0===t.text.slice(r.sourceSpan.start,r.sourceSpan.end).trim().length?e.slice(1):e,s=[];let u=null;for(let e=0;e<i.length;e++){const r=i[e];if(u&&l(u)&&p(r)&&r.value&&r.value.source===u.key.source){const e=D("NGMicrosyntaxKey",{name:r.key.source},r.key.span),n=(e,r)=>Object.assign(Object.assign({},e),gi.transformSpan({start:e.start,end:r},t)),i=t=>Object.assign(Object.assign({},n(t,e.end)),{alias:e}),u=s.pop();if("NGMicrosyntaxExpression"===u.type)s.push(i(u));else{if("NGMicrosyntaxKeyedExpression"!==u.type)throw new Error(`Unexpected type ${u.type}`);{const e=i(u.expression);s.push(n(Object.assign(Object.assign({},u),{expression:e}),e.end))}}}else s.push(a(r,e));u=r}return D("NGMicrosyntax",{body:s},0===s.length?e[0].sourceSpan:{start:s[0].start,end:s[s.length-1].end});function a(e,r){if(l(e)){const{key:t,value:n}=e;return n?0===r?D("NGMicrosyntaxExpression",{expression:o(n.ast),alias:null},n.sourceSpan):D("NGMicrosyntaxKeyedExpression",{key:D("NGMicrosyntaxKey",{name:c(t.source)},t.span),expression:D("NGMicrosyntaxExpression",{expression:o(n.ast),alias:null},n.sourceSpan)},{start:t.span.start,end:n.sourceSpan.end}):D("NGMicrosyntaxKey",{name:c(t.source)},t.span)}{const{key:r,sourceSpan:n}=e;if(/^let\s$/.test(t.text.slice(n.start,n.start+4))){const{value:t}=e;return D("NGMicrosyntaxLet",{key:D("NGMicrosyntaxKey",{name:r.source},r.span),value:t?D("NGMicrosyntaxKey",{name:t.source},t.span):null},{start:n.start,end:t?t.span.end:r.span.end})}{const n=function(e){if(!e.value||e.value.source!==Fi.NG_PARSE_TEMPLATE_BINDINGS_FAKE_PREFIX)return e.value;const r=Fi.findBackChar(/\S/,e.sourceSpan.start,t.text);return{source:"$implicit",span:{start:r,end:r}}}(e);return D("NGMicrosyntaxAs",{key:D("NGMicrosyntaxKey",{name:n.source},n.span),alias:D("NGMicrosyntaxKey",{name:r.source},r.span)},{start:n.span.start,end:r.span.end})}}}function o(e){return gi.transform(e,t)}function D(e,r,n,i=!0){return Object.assign(Object.assign({type:e},gi.transformSpan(n,t,i)),r)}function c(e){return Fi.toLowerCamelCase(e.slice(n.source.length))}function l(e){return e instanceof Un.ExpressionBinding}function p(e){return e instanceof Un.VariableBinding}function h(e){if('"'!==t.text[e.start]&&"'"!==t.text[e.start])return;const r=t.text[e.start];let n=!1;for(let i=e.start+1;i<t.text.length;i++)switch(t.text[i]){case r:if(!n)return void(e.end=i+1);default:n=!1;break;case"\\":n=!n}}}})),yi=l((function(e,t){function r(e,t){const{ast:r,comments:n}=t(e),i=new cn.Context(e),s=e=>gi.transform(e,i),u=s(r);return u.comments=n.map(s),u}Object.defineProperty(t,"__esModule",{value:!0}),t.parseTemplateBindings=t.parseAction=t.parseInterpolation=t.parseSimpleBinding=t.parseBinding=void 0,t.parseBinding=function(e){return r(e,Fi.parseNgBinding)},t.parseSimpleBinding=function(e){return r(e,Fi.parseNgSimpleBinding)},t.parseInterpolation=function(e){return r(e,Fi.parseNgInterpolation)},t.parseAction=function(e){return r(e,Fi.parseNgAction)},t.parseTemplateBindings=function(e){return mi.transformTemplateBindings(Fi.parseNgTemplateBindings(e),new cn.Context(e))}}));const{locStart:xi,locEnd:Ai}=on;function Si(e){return{astFormat:"estree",parse:(t,r,n)=>{const i=e(t,yi);return{type:"NGRoot",node:"__ng_action"===n.parser&&"NGChainedExpression"!==i.type?Object.assign(Object.assign({},i),{},{type:"NGChainedExpression",expressions:[i]}):i}},locStart:xi,locEnd:Ai}}return{parsers:{__ng_action:Si(((e,t)=>t.parseAction(e))),__ng_binding:Si(((e,t)=>t.parseBinding(e))),__ng_interpolation:Si(((e,t)=>t.parseInterpolation(e))),__ng_directive:Si(((e,t)=>t.parseTemplateBindings(e)))}}}));
