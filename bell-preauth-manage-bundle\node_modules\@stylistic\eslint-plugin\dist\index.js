'use strict';

var configs = require('./configs.js');
require('./utils.js');
require('eslint-visitor-keys');
require('espree');
require('estraverse');
require('./rules/array-bracket-newline.js');
require('./rules/array-bracket-spacing.js');
require('./rules/array-element-newline.js');
require('./rules/arrow-parens.js');
require('./rules/arrow-spacing.js');
require('./rules/block-spacing.js');
require('@typescript-eslint/utils');
require('@typescript-eslint/utils/ast-utils');
require('./rules/brace-style.js');
require('./rules/comma-dangle.js');
require('./rules/comma-spacing.js');
require('./rules/comma-style.js');
require('./rules/computed-property-spacing.js');
require('./rules/curly-newline.js');
require('./rules/dot-location.js');
require('./rules/eol-last.js');
require('./rules/function-call-argument-newline.js');
require('./rules/function-call-spacing.js');
require('./rules/function-paren-newline.js');
require('./rules/generator-star-spacing.js');
require('./rules/implicit-arrow-linebreak.js');
require('./rules/indent-binary-ops.js');
require('./rules/indent.js');
require('./rules/jsx-child-element-spacing.js');
require('./rules/jsx-closing-bracket-location.js');
require('./rules/jsx-closing-tag-location.js');
require('./rules/jsx-curly-brace-presence.js');
require('./rules/jsx-curly-newline.js');
require('./rules/jsx-curly-spacing.js');
require('./rules/jsx-equals-spacing.js');
require('./rules/jsx-first-prop-new-line.js');
require('./rules/jsx-function-call-newline.js');
require('./rules/jsx-indent-props.js');
require('./rules/jsx-indent.js');
require('./rules/jsx-max-props-per-line.js');
require('./rules/jsx-newline.js');
require('./rules/jsx-one-expression-per-line.js');
require('./rules/jsx-pascal-case.js');
require('picomatch');
require('./rules/jsx-props-no-multi-spaces.js');
require('./rules/jsx-quotes.js');
require('./rules/jsx-self-closing-comp.js');
require('./rules/jsx-sort-props.js');
require('./rules/jsx-tag-spacing.js');
require('./rules/jsx-wrap-multilines.js');
require('./rules/key-spacing.js');
require('./rules/keyword-spacing.js');
require('./rules/line-comment-position.js');
require('./rules/linebreak-style.js');
require('./rules/lines-around-comment.js');
require('./rules/lines-between-class-members.js');
require('./rules/max-len.js');
require('./rules/max-statements-per-line.js');
require('./rules/member-delimiter-style.js');
require('./rules/multiline-comment-style.js');
require('./rules/multiline-ternary.js');
require('./rules/new-parens.js');
require('./rules/newline-per-chained-call.js');
require('./rules/no-confusing-arrow.js');
require('./rules/no-extra-parens.js');
require('./rules/no-extra-semi.js');
require('./rules/no-floating-decimal.js');
require('./rules/no-mixed-operators.js');
require('./rules/no-mixed-spaces-and-tabs.js');
require('./rules/no-multi-spaces.js');
require('./rules/no-multiple-empty-lines.js');
require('./rules/no-tabs.js');
require('./rules/no-trailing-spaces.js');
require('./rules/no-whitespace-before-property.js');
require('./rules/nonblock-statement-body-position.js');
require('./rules/object-curly-newline.js');
require('./rules/object-curly-spacing.js');
require('./rules/object-property-newline.js');
require('./rules/one-var-declaration-per-line.js');
require('./rules/operator-linebreak.js');
require('./rules/padded-blocks.js');
require('./rules/padding-line-between-statements.js');
require('./rules/quote-props.js');
require('./rules/quotes.js');
require('./rules/rest-spread-spacing.js');
require('./rules/semi-spacing.js');
require('./rules/semi-style.js');
require('./rules/semi.js');
require('./rules/space-before-blocks.js');
require('./rules/space-before-function-paren.js');
require('./rules/space-in-parens.js');
require('./rules/space-infix-ops.js');
require('./rules/space-unary-ops.js');
require('./rules/spaced-comment.js');
require('./vendor.js');
require('./rules/switch-colon-spacing.js');
require('./rules/template-curly-spacing.js');
require('./rules/template-tag-spacing.js');
require('./rules/type-annotation-spacing.js');
require('./rules/type-generic-spacing.js');
require('./rules/type-named-tuple-spacing.js');
require('./rules/wrap-iife.js');
require('./rules/wrap-regex.js');
require('./rules/yield-star-spacing.js');

var index = Object.assign(configs.plugin, { configs: configs.configs });

module.exports = index;
