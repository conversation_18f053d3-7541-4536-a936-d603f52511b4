import { Store } from "./StoreServices";
import { Action } from "redux-actions";
export declare abstract class Localization {
    abstract createReducer(): (state: LocalizationState, action: Action<any>) => LocalizationState;
    abstract loadMessages(bypassCheck?: boolean): void;
    abstract loadMessagesOnce(): void;
    abstract disableNetwork(disable?: boolean): void;
    abstract destroy(): void;
}
export interface Localization {
    store: Store;
}
export interface LocalizationState {
    locale: LocaleValue;
    fullLocale: FullLocaleValue;
    messages: Dictionary;
    formats: {
        [key: string]: any;
    };
    loaded: boolean;
}
export declare abstract class LocalizationServices {
    abstract locale: LocaleValue;
    abstract fullLocale: FullLocaleValue;
    abstract formats: {
        [key: string]: any;
    };
    abstract init(): void;
    abstract createChildLocalization(path: string, widgetStore: Store, namespace?: string): Localization;
}
export interface MessagesDictionary {
    [locale: string]: Dictionary;
}
export interface Dictionary {
    [key: string]: string;
}
export interface LocaleData {
    [key: string]: Dictionary;
}
export type LocaleValue = "en" | "fr";
export type FullLocaleValue = "en-CA" | "fr-CA";
