"use strict";

exports.__esModule = true;
exports.default = hasGeneratorInterface;

var _ownKeys = _interopRequireDefault(require("./ownKeys"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function hasGeneratorInterface(handler) {
  var keys = (0, _ownKeys.default)(handler);
  var hasOnlyInterfaceNames = keys.every(function (ownKey) {
    return ownKey === 'next' || ownKey === 'throw';
  });
  return keys.length && keys.length <= 2 && hasOnlyInterfaceNames;
}