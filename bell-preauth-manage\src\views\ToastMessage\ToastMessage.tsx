import React from "react";
import {<PERSON><PERSON>, <PERSON>dalContent, ModalBody} from "@bell/bell-ui-library";
import {injectIntl } from "react-intl";

interface ToastSuccessModalProps {
    isModalOpen: boolean;
    setIsModalOpen: Function;
    intl: any;
}


const ToastSuccessModalComponent = (props: ToastSuccessModalProps) => {
    const { isModalOpen, setIsModalOpen, intl } = props;

    const onToggleModal = (isOpen: boolean) => {
        setIsModalOpen(isOpen);
    };

    const handleCloseClick = () => {
        setIsModalOpen(false);
        window.location.href = "/";
    }

    return (
        <div>
            {isModalOpen && (
                <Modal
                className="payment-toaster"
                id="cancel-preauth-success-modal"
                aria-labelledby="cancel-preauth-success-modal-title"      
                onEscapeKeyPressed={() => onToggleModal(false)}
              >
                <ModalContent
                  useDefaultRadius={false}
                >
                  <ModalBody
                    isDefaultPadding={false}
                    className="payment-py-15 payment-px-15 payment-border-b-4 payment-border-[#339043] payment-rounded-2"
                  >
                      <div className="payment-flex payment-gap-10 payment-leading-18 payment-w-full">
                          <span className="bi_brui bi_small_checkmark_fill brui-text-green brui-text-24" role="img" aria-hidden="true" aria-label=" "></span>
                          <div id="account-fetched" className="brui-flex brui-flex-col brui-text-14 ">
                            <span className="payment-mt-[3px] payment-text-gray">
                                {intl.formatMessage({id: "SUCCESS_TOAST_MESSAGE"})}
                            </span>
                          </div>
                          <button onClick={handleCloseClick} type="button" aria-label="Close dialog box" className="brui-flex brui-rounded-2 focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 payment-h-12 payment-ml-auto" id="no-name-on-card-close-button">
                              <span className="bi_close_bold bi_brui payment-text-12 payment-text-gray" role="img" aria-hidden="true" aria-label=" "></span><span className="brui-sr-only">
                              {intl.formatMessage({id: "CLOSE_ICON_SR"})}
                              </span>
                          </button>
                      </div>
                  </ModalBody>
                </ModalContent>
                
              </Modal>
            )}
        </div>
    );
};

export const ToastMessage = injectIntl(ToastSuccessModalComponent);