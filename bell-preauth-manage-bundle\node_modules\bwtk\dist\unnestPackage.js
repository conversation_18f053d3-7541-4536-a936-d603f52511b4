const fs = require('fs');
var exec = require('child_process');
let rawdata = fs.readFileSync(`package.json`);  
let packages = JSON.parse(rawdata);  
devDep=packages['devDependencies'];
//console.log(devDep)
if(devDep != undefined){
    for(var i in devDep){
        //console.log(i)
        start= i.indexOf('@');
        end = i.indexOf('/');
        var name = JSON.stringify(i)
        if(start != -1){
            if(name.substring(start,end)=="@types"){
                console.log("no moves needed")
            }
            else{
                //there is a scoped package
                var scopePackage = name.substring(start+1,end+1);
                //console.log(scopePackage)
                exec.execSync(`mv node_modules/${scopePackage}/* node_modules/ `)
                var dashIndex = scopePackage.lastIndexOf("-")
                var actualName = name.substring(scopePackage.length+2,dashIndex);
                var longname = name.substring(scopePackage.length+2,name.length-1)
                console.log(longname)
                exec.execSync(`mv node_modules/${longname} node_modules/${actualName}`)
            }
        }
    }
}
devDep2=packages['dependencies'];
//console.log(devDep)
if(devDep2 != undefined){
    for(var i in devDep2){
        //console.log(i)
        start= i.indexOf('@');
        end = i.indexOf('/');
        var name = JSON.stringify(i)
        if(start != -1){
            if(name.substring(start,end)=="@types"){
                console.log("no moves needed")
            }
            else{
                //there is a scoped package
                var scopePackage = name.substring(start+1,end+1);
                //console.log(scopePackage)
                exec.execSync(`mv node_modules/${scopePackage}/* node_modules/`)
                var dashIndex = name.lastIndexOf("-")
                var actualName = name.substring(scopePackage.length+2,dashIndex);
                var longname = name.substring(scopePackage.length+2,name.length-1)
                console.log(longname)
                exec.execSync(`mv node_modules/${longname} node_modules/${actualName}`)
            }
        }
    }
}