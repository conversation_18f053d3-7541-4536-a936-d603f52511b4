import { YAMLMap } from '../nodes/YAMLMap';
import type { BlockMap } from '../parse/cst';
import type { CollectionTag } from '../schema/types';
import type { ComposeContext, ComposeNode } from './compose-node';
import type { ComposeErrorHandler } from './composer';
export declare function resolveBlockMap({ composeNode, composeEmptyNode }: ComposeNode, ctx: ComposeContext, bm: BlockMap, onError: ComposeErrorHandler, tag?: CollectionTag): YAMLMap.Parsed;
