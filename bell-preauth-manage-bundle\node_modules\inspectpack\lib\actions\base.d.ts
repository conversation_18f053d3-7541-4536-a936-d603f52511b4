import { IModule } from "../interfaces/modules";
import { IWebpackStats, IWebpackStatsAsset, IWebpackStatsAssets, IWebpackStatsChunk, IWebpackStatsModules } from "../interfaces/webpack-stats";
export interface IActionConstructor {
    stats: IWebpackStats;
    ignoredPackages?: (string | RegExp)[];
}
export interface IModulesByAsset {
    [asset: string]: {
        asset: IWebpackStatsAsset;
        mods: IModule[];
    };
}
export declare const nodeModulesParts: (name: string) => string[];
export declare const _isNodeModules: (name: string) => boolean;
export declare const _normalizeWebpackPath: (identifier: string, name?: string | undefined) => string;
export declare const _getBaseName: (name: string) => string | null;
export declare abstract class Action {
    stats: IWebpackStats;
    private _data?;
    private _modules?;
    private _assets?;
    private _template?;
    private _ignoredPackages;
    constructor({ stats, ignoredPackages }: IActionConstructor);
    validate(): Promise<IAction>;
    getData(): Promise<object>;
    get modules(): IModule[];
    shouldBail(): Promise<boolean>;
    protected ignorePackage(baseName: string): boolean;
    protected getSourceMods(mods: IWebpackStatsModules, parentChunks?: IWebpackStatsChunk[]): IModule[];
    get assets(): IModulesByAsset;
    protected getSourceAssets(assets: IWebpackStatsAssets): IModulesByAsset;
    get template(): ITemplate;
    protected abstract _getData(): Promise<object>;
    protected abstract _createTemplate(): ITemplate;
}
export declare type IAction = Action;
interface ITemplateConstructor {
    action: IAction;
}
export declare enum TemplateFormat {
    json = "json",
    text = "text",
    tsv = "tsv"
}
export interface ITemplate {
    json(): Promise<string>;
    text(): Promise<string>;
    tsv(): Promise<string>;
    render(format: TemplateFormat): Promise<string>;
}
export declare abstract class Template implements ITemplate {
    protected action: IAction;
    constructor({ action }: ITemplateConstructor);
    json(): Promise<string>;
    abstract text(): Promise<string>;
    abstract tsv(): Promise<string>;
    render(format: TemplateFormat): Promise<string>;
    protected trim(str: string, num: number): string;
}
export {};
