const { RuleTester } = require("eslint");
const rule = require("../lib/rules/triple-equals");

const ruleTester = new RuleTester({
  languageOptions: {
    ecmaVersion: 2021,
  },
});

describe("Triple Equals", () => {
  ruleTester.run("triple-equals", rule, {
    valid: [
      { code: "if (x === 5) {}" },
      { code: "if (y !== null) {}" }, // Null check is allowed
    ],
    invalid: [
      {
        code: "if (x == 5) {}",
        errors: [
          { message: "Use `===` instead of `==` (except for null checks)." },
        ],
        output: "if (x === 5) {}",
      },
      {
        code: "if (x != 10) {}",
        errors: [
          { message: "Use `!==` instead of `!=` (except for null checks)." },
        ],
        output: "if (x !== 10) {}",
      },
    ],
  });
});
