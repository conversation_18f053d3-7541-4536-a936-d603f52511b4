import { AjaxServices, Client, AjaxOptions, EventStreamServices } from "../../../@types";
export declare class FetchAjaxServices implements AjaxServices {
    private stream;
    constructor(stream: EventStreamServices);
    get<T>(url: string, data?: string, options?: AjaxOptions): Promise<T>;
    post<T>(url: string, data: {}, options?: AjaxOptions): Promise<T>;
    head<T>(url: string, options?: AjaxOptions): Promise<T>;
    put<T>(url: string, data: {}, options?: AjaxOptions): Promise<T>;
    patch<T>(url: string, data: {}, options?: AjaxOptions): Promise<T>;
    del(url: string, options?: AjaxOptions): Promise<any>;
    createClient(options?: AjaxOptions): Client;
}
