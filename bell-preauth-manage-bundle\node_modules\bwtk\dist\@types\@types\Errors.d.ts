export declare abstract class InternalError extends Error {
    message: string;
    constructor(message: string);
}
export declare class ApplicationError extends InternalError {
    message: string;
    constructor(message: string);
}
export declare class ValidationError extends InternalError {
    message: string;
    constructor(message: string);
}
export declare class ReduxError extends Error {
    constructor(params?: {
        [key: string]: any;
    });
}
