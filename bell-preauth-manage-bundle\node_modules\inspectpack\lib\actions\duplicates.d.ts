import { IActionModule } from "../interfaces/modules";
import { IAction, IActionConstructor } from "./base";
interface IDuplicatesSummary {
    extraFiles: {
        num: number;
    };
    extraSources: {
        num: number;
        bytes: number;
    };
}
interface IDuplicatesSource {
    meta: IDuplicatesSummary;
    modules: IActionModule[];
}
export interface IDuplicatesFiles {
    [baseName: string]: {
        meta: IDuplicatesSummary;
        sources: IDuplicatesSource[];
    };
}
interface IDuplicatesDataAssets {
    [asset: string]: {
        meta: IDuplicatesSummary;
        files: IDuplicatesFiles;
    };
}
export interface IDuplicatesData {
    meta: IDuplicatesSummary;
    assets: IDuplicatesDataAssets;
}
export declare const create: (opts: IActionConstructor) => IAction;
export {};
