{"name": "husky", "version": "4.3.8", "description": "Prevents bad commit or push (git hooks, pre-commit/precommit, pre-push/prepush, post-merge/postmerge and all that stuff...)", "bin": {"husky-run": "./bin/run.js", "husky-upgrade": "./lib/upgrader/bin.js"}, "engines": {"node": ">=10"}, "scripts": {"test": "npm run lint && jest", "install": "node husky install", "preuninstall": "node husky uninstall", "build": "del-cli lib && tsc", "version": "jest -u && git add -A src/installer/__tests__/__snapshots__", "postversion": "git push && git push --tags", "prepublishOnly": "npm run test && npm run build && pinst --enable && pkg-ok", "postpublish": "pinst --disable", "lint": "eslint . --ext .js,.ts --ignore-path .gitignore", "fix": "npm run lint -- --fix", "doc": "markdown-toc -i README.md", "postinstall": "opencollective-postinstall || exit 0"}, "repository": {"type": "git", "url": "git+https://github.com/typicode/husky.git"}, "keywords": ["git", "hook", "hooks", "pre-commit", "precommit", "post-commit", "postcommit", "pre-push", "prepush", "post-merge", "postmerge", "test", "lint"], "author": "Typicode <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/typicode/husky/issues"}, "homepage": "https://github.com/typicode/husky#readme", "dependencies": {"chalk": "^4.0.0", "ci-info": "^2.0.0", "compare-versions": "^3.6.0", "cosmiconfig": "^7.0.0", "find-versions": "^4.0.0", "opencollective-postinstall": "^2.0.2", "pkg-dir": "^5.0.0", "please-upgrade-node": "^3.2.0", "slash": "^3.0.0", "which-pm-runs": "^1.0.0"}, "devDependencies": {"@types/ci-info": "^2.0.0", "@types/cosmiconfig": "^6.0.0", "@types/is-ci": "^2.0.0", "@types/jest": "^25.2.1", "@types/mkdirp": "^1.0.0", "@types/node": "^13.11.1", "@typescript-eslint/eslint-plugin": "^2.27.0", "@typescript-eslint/parser": "^2.27.0", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.1", "eslint-config-xo-space": "^0.24.0", "eslint-plugin-prettier": "^3.1.2", "formatree": "^1.0.2", "jest": "^25.3.0", "markdown-toc": "^1.2.0", "mkdirp": "^1.0.4", "pinst": "^2.1.1", "pkg-ok": "^2.3.1", "prettier": "^2.0.4", "tempy": "^0.5.0", "ts-jest": "^25.3.1", "type-fest": "^0.13.1", "typescript": "^3.8.3"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "testPathIgnorePatterns": ["node_modules", "__env__.ts"], "moduleFileExtensions": ["ts", "js", "json", "node"]}, "collective": {"type": "opencollective", "url": "https://opencollective.com/husky"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/husky"}}