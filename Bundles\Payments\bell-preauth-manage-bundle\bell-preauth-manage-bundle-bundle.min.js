/*! bell-preauth-manage-bundle (bundle) 1.0.0 | bwtk 6.1.0 | 2025-07-16T20:03:16.563Z */
var factory;factory=function(e,t,n,r,a,i,o,l,u){return function(){function s(e){var t,n=d[e];return void 0!==n?n.exports:(t=d[e]={exports:{}},m[e](t,t.exports,s),t.exports)}var c,m=[,function(t){"use strict";t.exports=e},function(e){"use strict";e.exports=t},function(e,t,n){var r,a,i,o,l,u,s,c,m;self,e.exports=(r=n(1),a=n(4),i=n(5),o=n(6),l=n(7),u=n(8),s=n(9),c=n(10),m=n(2),function(){function e(t){var r,a=d[t];return void 0!==a?a.exports:(r=d[t]={exports:{}},n[t].call(r.exports,r,r.exports,e),r.exports)}var t,n=[,function(e,t,n){"use strict";function r(e,t){function n(){this.constructor=e}if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");D(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function a(e,t){var n,r,a={};for(n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]]);return a}function i(e,t,n,r){var a,i,o=arguments.length,l=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)l=Reflect.decorate(e,t,n,r);else for(i=e.length-1;i>=0;i--)(a=e[i])&&(l=(o<3?a(l):o>3?a(t,n,l):a(t,n))||l);return o>3&&l&&Object.defineProperty(t,n,l),l}function o(e,t){return function(n,r){t(n,r,e)}}function l(e,t,n,r,a,i){function o(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}var l,u,s,c,m,d=r.kind,p="getter"===d?"get":"setter"===d?"set":"value",f=!t&&e?r.static?e:e.prototype:null,b=t||(f?Object.getOwnPropertyDescriptor(f,r.name):{}),E=!1;for(u=n.length-1;u>=0;u--){for(c in s={},r)s[c]="access"===c?{}:r[c];for(c in r.access)s.access[c]=r.access[c];if(s.addInitializer=function(e){if(E)throw new TypeError("Cannot add initializers after decoration has completed");i.push(o(e||null))},m=(0,n[u])("accessor"===d?{get:b.get,set:b.set}:b[p],s),"accessor"===d){if(void 0===m)continue;if(null===m||"object"!=typeof m)throw new TypeError("Object expected");(l=o(m.get))&&(b.get=l),(l=o(m.set))&&(b.set=l),(l=o(m.init))&&a.unshift(l)}else(l=o(m))&&("field"===d?a.unshift(l):b[p]=l)}f&&Object.defineProperty(f,r.name,b),E=!0}function u(e,t,n){var r,a=arguments.length>2;for(r=0;r<t.length;r++)n=a?t[r].call(e,n):t[r].call(e);return a?n:void 0}function s(e){return"symbol"==typeof e?e:"".concat(e)}function c(e,t,n){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:n?"".concat(n," ",t):t})}function m(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function d(e,t,n,r){return new(n||(n=Promise))(function(a,i){function o(e){try{u(r.next(e))}catch(t){i(t)}}function l(e){try{u(r.throw(e))}catch(t){i(t)}}function u(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(o,l)}u((r=r.apply(e,t||[])).next())})}function p(e,t){function n(n){return function(u){return function(n){if(r)throw new TypeError("Generator is already executing.");for(;l&&(l=0,n[0]&&(o=0)),o;)try{if(r=1,a&&(i=2&n[0]?a.return:n[0]?a.throw||((i=a.return)&&i.call(a),0):a.next)&&!(i=i.call(a,n[1])).done)return i;switch(a=0,i&&(n=[2&n[0],i.value]),n[0]){case 0:case 1:i=n;break;case 4:return o.label++,{value:n[1],done:!1};case 5:o.label++,a=n[1],n=[0];continue;case 7:n=o.ops.pop(),o.trys.pop();continue;default:if(!((i=(i=o.trys).length>0&&i[i.length-1])||6!==n[0]&&2!==n[0])){o=0;continue}if(3===n[0]&&(!i||n[1]>i[0]&&n[1]<i[3])){o.label=n[1];break}if(6===n[0]&&o.label<i[1]){o.label=i[1],i=n;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(n);break}i[2]&&o.ops.pop(),o.trys.pop();continue}n=t.call(e,o)}catch(u){n=[6,u],a=0}finally{r=i=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}([n,u])}}var r,a,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},l=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return l.next=n(0),l.throw=n(1),l.return=n(2),"function"==typeof Symbol&&(l[Symbol.iterator]=function(){return this}),l}function f(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||B(t,e,n)}function b(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function E(e,t){var n,r,a,i,o="function"==typeof Symbol&&e[Symbol.iterator];if(!o)return e;n=o.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=n.next()).done;)a.push(r.value)}catch(l){i={error:l}}finally{try{r&&!r.done&&(o=n.return)&&o.call(n)}finally{if(i)throw i.error}}return a}function _(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(E(arguments[t]));return e}function y(){var e,t,n,r,a,i,o,l;for(e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(r=Array(e),a=0,t=0;t<n;t++)for(o=0,l=(i=arguments[t]).length;o<l;o++,a++)r[a]=i[o];return r}function g(e,t,n){if(n||2===arguments.length)for(var r,a=0,i=t.length;a<i;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))}function v(e){return this instanceof v?(this.v=e,this):new v(e)}function N(e,t,n){function r(e,t){s[e]&&(u[e]=function(t){return new Promise(function(n,r){c.push([e,t,n,r])>1||a(e,t)})},t&&(u[e]=t(u[e])))}function a(e,t){try{(n=s[e](t)).value instanceof v?Promise.resolve(n.value.v).then(i,o):l(c[0][2],n)}catch(r){l(c[0][3],r)}var n}function i(e){a("next",e)}function o(e){a("throw",e)}function l(e,t){e(t),c.shift(),c.length&&a(c[0][0],c[0][1])}if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var u,s=n.apply(e,t||[]),c=[];return u=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),r("next"),r("throw"),r("return",function(e){return function(t){return Promise.resolve(t).then(e,o)}}),u[Symbol.asyncIterator]=function(){return this},u}function A(e){function t(t,a){n[t]=e[t]?function(n){return(r=!r)?{value:v(e[t](n)),done:!1}:a?a(n):n}:a}var n,r;return n={},t("next"),t("throw",function(e){throw e}),t("return"),n[Symbol.iterator]=function(){return this},n}function C(e){function t(t){n[t]=e[t]&&function(n){return new Promise(function(r,a){!function(e,t,n,r){Promise.resolve(r).then(function(t){e({value:t,done:n})},t)}(r,a,(n=e[t](n)).done,n.value)})}}if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,r=e[Symbol.asyncIterator];return r?r.call(e):(e=b(e),n={},t("next"),t("throw"),t("return"),n[Symbol.asyncIterator]=function(){return this},n)}function h(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}function T(e){var t,n,r;if(e&&e.__esModule)return e;if(t={},null!=e)for(n=w(e),r=0;r<n.length;r++)"default"!==n[r]&&B(t,e,n[r]);return k(t,e),t}function I(e){return e&&e.__esModule?e:{default:e}}function R(e,t,n,r){if("a"===n&&!r)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)}function x(e,t,n,r,a){if("m"===r)throw new TypeError("Private method is not writable");if("a"===r&&!a)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!a:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?a.call(e,n):a?a.value=n:t.set(e,n),n}function S(e,t){if(null===t||"object"!=typeof t&&"function"!=typeof t)throw new TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?t===e:e.has(t)}function O(e,t,n){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var r,a;if(n){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");r=t[Symbol.asyncDispose]}if(void 0===r){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");r=t[Symbol.dispose],n&&(a=r)}if("function"!=typeof r)throw new TypeError("Object not disposable.");a&&(r=function(){try{a.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:r,async:n})}else n&&e.stack.push({async:!0});return t}function M(e){function t(t){e.error=e.hasError?new U(t,e.error,"An error was suppressed during disposal."):t,e.hasError=!0}var n,r=0;return function a(){for(;n=e.stack.pop();)try{if(!n.async&&1===r)return r=0,e.stack.push(n),Promise.resolve().then(a);if(n.dispose){var i=n.dispose.call(n.value);if(n.async)return r|=2,Promise.resolve(i).then(a,function(e){return t(e),a()})}else r|=1}catch(o){t(o)}if(1===r)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()}function L(e,t){return"string"==typeof e&&/^\.\.?\//.test(e)?e.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(e,n,r,a,i){return n?t?".jsx":".js":!r||a&&i?r+a+"."+i.toLowerCase()+"js":e}):e}var D,P,B,k,w,U;n.r(t),n.d(t,{__addDisposableResource:function(){return O},__assign:function(){return P},__asyncDelegator:function(){return A},__asyncGenerator:function(){return N},__asyncValues:function(){return C},__await:function(){return v},__awaiter:function(){return d},__classPrivateFieldGet:function(){return R},__classPrivateFieldIn:function(){return S},__classPrivateFieldSet:function(){return x},__createBinding:function(){return B},__decorate:function(){return i},__disposeResources:function(){return M},__esDecorate:function(){return l},__exportStar:function(){return f},__extends:function(){return r},__generator:function(){return p},__importDefault:function(){return I},__importStar:function(){return T},__makeTemplateObject:function(){return h},__metadata:function(){return m},__param:function(){return o},__propKey:function(){return s},__read:function(){return E},__rest:function(){return a},__rewriteRelativeImportExtension:function(){return L},__runInitializers:function(){return u},__setFunctionName:function(){return c},__spread:function(){return _},__spreadArray:function(){return g},__spreadArrays:function(){return y},__values:function(){return b}}),D=function(e,t){return D=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},D(e,t)},P=function(){return P=Object.assign||function(e){var t,n,r,a;for(n=1,r=arguments.length;n<r;n++)for(a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},P.apply(this,arguments)},B=Object.create?function(e,t,n,r){void 0===r&&(r=n);var a=Object.getOwnPropertyDescriptor(t,n);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,a)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]},k=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t},w=function(e){return w=Object.getOwnPropertyNames||function(e){var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},w(e)},U="function"==typeof SuppressedError?SuppressedError:function(e,t,n){var r=new Error(n);return r.name="SuppressedError",r.error=e,r.suppressed=t,r},t.default={__extends:r,__assign:P,__rest:a,__decorate:i,__param:o,__esDecorate:l,__runInitializers:u,__propKey:s,__setFunctionName:c,__metadata:m,__awaiter:d,__generator:p,__createBinding:B,__exportStar:f,__values:b,__read:E,__spread:_,__spreadArrays:y,__spreadArray:g,__await:v,__asyncGenerator:N,__asyncDelegator:A,__asyncValues:C,__makeTemplateObject:h,__importStar:T,__importDefault:I,__classPrivateFieldGet:R,__classPrivateFieldSet:x,__classPrivateFieldIn:S,__addDisposableResource:O,__disposeResources:M,__rewriteRelativeImportExtension:L}},function(e){"use strict";e.exports=r},function(e){"use strict";e.exports=a},function(e){"use strict";e.exports=i},function(e){"use strict";e.exports=o},function(e){"use strict";e.exports=l},function(e){"use strict";e.exports=JSON.parse('{"en":{"ALERT_CONFIRMATION_INFO_HEADING":"Avoid late fees by paying your current balance.","ALERT_CONFIRMATION_INFO_DESC":"Pre-authorized payments will only begin on your next billing period. Any existing balance on your account must be paid separately with a final one-time payment, or risk late fees.","ALERT_CONFIRMATION_INFO_BUTTON_DESC":"Make a payment","ALERT_CONFIRMATION_INFO_DESC_1":"Pre-authorized payments will only begin on your next billing period. You must pay your ","ALERT_CONFIRMATION_INFO_DESC_2":"balance in a separate and final one-time payment, or risk late fees. ","EXISTING_CC_TITLE":"Select a credit Card","EXISTING_BANK_TITLE":"Select a bank account","EXISTING_BANK_TITLE_SR":"Requried, Select a bank account","EXISTING_BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Choose a bank account already on file or add a new one manually","SELECT_BILLS_HEADING":"Select bills","SELECT_BILLS_HEADING_SINGULAR":"Select bill","SELECT_BILLS_ACCOUNT_TITLE":"{accounttype} account number","SELECT_BILLS_HEADING_DESC":"Set up pre-authorized payments for the following bill(s):","SELECT_BANK_TITLE":"Select a bank account","SELECT_BANK_PLACEHOLDER":"Select an option","ACCOUNT_TYPE_ONE_BILL":"One Bill","ACCOUNT_TYPE_MYBILL":"One Bill","ACCOUNT_TYPENAME_ONEBILL":"One Bill","ACCOUNT_TYPENAME_MYBILL":"My bill","ACCOUNT_TYPENAME_MOBILITY":"Mobility","ACCOUNT_TYPENAME_TV":"TV","ACCOUNT_TYPENAME_INTERNET":"Internet","ACCOUNT_TYPENAME_HOMEPHONE":"Home Phone","ACCOUNT_TYPENAME_MOBILITY_ONEBILL":"Mobility and One Bill","ACCOUNT_TYPENAME_SINGLEBAN":"Single Ban","ACCOUNT_BALANCE":"Your current balance is","BACK_TO_MY_BELL":"Back to MyBell","CTA_NEXT":"Next","CTA_EDIT":"Edit","CTA_CLOSE":"Close dialog box","CTA_EXPAND_TERMS":"Expand terms of service","CTA_CONFIRM":"Confirm and submit","CTA_CANCEL":"Cancel","CTA_COLLAPSE_TERMS":"Collapse terms of service","CTA_INTERAC":"Sign in with Interac®  verification service","CTA_INTERAC_SR":"Sign in with Interac registered trademark verification service","CREDIT_CARD_LABEL":"Credit card","CREDIT_CARD_TYPE_LABEL":"Card type","CREDIT_CARD_NAME_LABEL":"Cardholder name","CREDIT_CARD_NAME_DESC_INPUT_LABEL":"As it appears on the card","CREDIT_CARD_NUMBER_LABEL":"Credit card number","CREDIT_CARD_NUMBER_DESC_INPUT_LABEL":"15 or 16 digits","CREDIT_CARD_EXPIRY_LABEL":"Expiration date","CREDIT_CARD_EXPIRY_SR_MONTH_LABEL":"Required, Expiration date month","CREDIT_CARD_EXPIRY_SR_YEAR_LABEL":"Required, Expiration date year","CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Card security code","CREDIT_CARD_NAME_SR_LABEL":"Required, Cardholder name","CREDIT_CARD_NUMBER_SR_LABEL":"Required, Credit card number","CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL":"Required, Card security code","ERROR_CREDIT_CARD_NAME_INPUT_LABEL":"Enter a valid cardholder name","ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL":"Enter a valid credit card number","ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL":"Enter a valid expiration date","ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Enter a valid card security code","CREDIT_CARD_VALID":"Valid Until","CREDIT_CARD_MONTH_PLACEHOLDER":"MM","CREDIT_CARD_YEAR_PLACEHOLDER":"YY","REQUIRED_LABEL":"*Required information","MODAL_NO_NAME":"No name on card?","MODAL_SECURITY_CODE":"What is a card security code?","MODAL_NO_NAME_TITLE":"No name on the credit card?","MODAL_NO_NAME_DESC":"If you’re using a prepaid credit card that doesn’t have a cardholder name, enter the name of the Bell account holder instead.","MODAL_SECURITY_CODE_TITLE":"What is a card security code?","MODAL_SECURITY_CODE_DESC":"The card security code (CSC) is a fraud prevention feature used to verify that the credit card is in your possession. It’s sometimes called a card verification code (CVC) or card verification value (CVV). See where to find your security code below:","CARD_TYPE_VISA_MASTERCARD":"Visa and Mastercard","CARD_TYPE_VISA_MASTERCARD_DESC":"A 3-digit number on the back of your credit card.","CARD_TYPE_AMERICAN_EXP":"American Express","CARD_TYPE_AMERICAN_EXP_DESC":"A 4-digit number on the front of your credit card.","MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE":"Find your transit and account numbers","MODAL_BANK_STATEMENT_TITLE":"Look at a bank statement or cheque","MODAL_BANK_STATEMENT_DESC":"You can find your bank account details on a bank statement or cheque. You can also find your account information in your banking app or online account.","MODAL_TRANSIT_NUMBER_DESC":"The <strong>transit number</strong> is 5 digits.","MODAL_ACCOUNT_NUMBER_DESC":"Your <strong>account number</strong> is 7 to 12 digits.","ALERT_ERROR_HEADING":"An error occurred while processing your request.","ALERT_ERROR_HEADING_SR":"Warning, An error occured while processing your request.","ALERT_ERROR_INFO_REQUIRED":"- This information is required.","ALERT_ERROR_SELECT_BILL_INFO_REQUIRED":"Select a bill – This information is required.","ALERT_ERROR_SELECT_BILL_INFO":"Select a bill","ALERT_ERROR_SELECT_BILL_DESC":"This information is required.","ALERT_ERROR_ONE_SELECT_BILL":"Select at least one bill","ALERT_ERROR_HEADING_SOME_BALANCE":"<strong>Some of your current balances</strong> were not paid due to an error processing your request.","ALERT_ERROR_OTP_ALL_BALANCE":"<strong>Your current balance(s) were not paid</strong> due to an error processing your request.","ALERT_ERROR_OTP_BALANCE":"<strong>Your balance of ${balance} was not paid.</strong>","ALERT_ERROR_OTP_BALANCE_SR":"Your balance of {balance} dollars was not paid","ALERT_ERROR_OTP_BALANCE_DESC":"There was an error processing your one-time payment. Please pay your current account balance to avoid late payment charges.","ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR":"Though you opted not to pay the following balance, they will also require a separate payment:","ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL":"Though you opted not to pay the following balance(s), they will also require a separate payment:","SELECT_PAYMENT_METHOD_HEADING":"Choose a payment method","PAY_CURRENT_BALANCE_HEADING":"Pay your current balance","PAY_CURRENT_BALANCE_DESC":"Pre-authorized payments will  <strong class=\'brui-text-black\'>begin on your next bill.</strong> You can pay your current account balance separately with a one-time payment to avoid late payment charges.","PAY_CURRENT_BALANCE_OPTED_IN":"Your one-time payment will be processed in <strong>3 to 5 business days</strong>. After that, you’ll have a balance of {balance} on your account.","PAY_CURRENT_BALANCE_OPTED_IN_PACC":"Your one-time payment will be processed <strong>as soon as you submit this transaction</strong>.","PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR":"<p>There’s an outstanding balance on your account.</p><p class=\'brui-mt-10\'>Pre-authorized payments will begin on your next bill. You can pay your current account balance separately with a one-time payment to avoid late payment charges.</p>","PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL":"<p>There are outstanding balances on your accounts.</p><p class=\'brui-mt-10\'>Pre-authorized payments will begin on your next bill. You can pay your current account balances separately with a one-time payment to avoid late payment charges.</p>","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_IN":"Your one-time payment will be processed in <strong>3 to 5 business days</strong>. After that, you’ll have a balance of {balance} on your account.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_SINGULAR":"You chose not to pay your current account balance.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_PLURAL":"You chose not to pay your current account balances.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_SINGULAR":"There’s an outstanding balance on your other account.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_PLURAL":"There are outstanding balances on your other accounts.","PAYMENT_AMOUNT":"Payment amount","UNPAID_BALANCE":"Unpaid balance","PAY_MY_BALANCE":"Pay my balance of","PAY_CURRENT_BALANCE_NOTE_1":"We’ll use the bank account information provided above to make the payment.","PAY_CURRENT_BALANCE_NOTE_2":"Note: If you made a payment in the last 3 to 5 business days, your balance owing may not be updated here yet.","TERMS_AND_CONDITION_HEADING":"Pre-authorized Payment Authorization","TERMS_AND_CONDITION_DISCLAIMER":"By clicking confirm and submit, I am confirming that I have read and agree to the Bell Terms of Service and the pricing details of my selected service(s).","TRANSACTION_SUBMITTED_HEADING":"Transaction submitted","CONFIRMATION_HEADING":"Confirmation","ALERT_CONFIRMATION_SUCCESS_HEADING":"Here’s what’s happening:","ALERT_CONFIRMATION_SUCCESS_MESSAGE":"Pre-authorized payments have been set up and will begin on your <strong>next bill.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_V2":"Pre-authorized payments have been set up and will begin on your <strong>next billing cycle.</strong>","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC":"Your one-time payment for <strong> {account} </strong> has been processed.","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD":"Your one-time payment for <strong> {account} </strong> will be processed in <strong>3 to 5 business days.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS":"Your Autopay credit will start within 1 to 2 billing periods.","ALERT_CONFIRMATION_SUCCESS_NUMBER":"Confirmation number:","ALERT_CONFIRMATION_SUCCESS_DESC":"We’ve sent a confirmation to {email}. If this isn’t your correct email address, please","ALERT_CONFIRMATION_SUCCESS_DESC_LINK":"update your profile.","ALERT_CONFIRMATION_SUCCESS_CONFIRM_NO":"Confirmation Number:","ALERT_CONFIRMATION_CURRENT_BALANCE":"The current balance for {account} has been paid.","PAYMENT_SUMMARY_TITLE":"Payment summary","PAYMENT_INFORMATION_TITLE":"Payment information","BILL_INFORMATION_TITLE":"Bill information","BILL_INFORMATION_ACCOUNT_NUMBER":"Account","PAYMENT_METHOD_TITLE":"Payment method","CURRENT_BALANCE_TITLE":"Current balance","SUMMARY_CURRENT_BAL_OTP_FAILURE":"Payment for your current balance was not processed. Please make a separate one-time payment.","TERMS_AND_CON_TITLE":"Pre-authorized Payment Authorization","TERMS_AND_CON_DESC_1":"You authorize us, Bell, to set up pre-authorized payment using the payment information you provided, as follows","TERMS_AND_CON_DESC_LIST_1":"For <strong>monthly payments:</strong>","TERMS_AND_CON_DESC_LIST_1_ITEM1":"We will debit your bank account or charge your credit card the total amount due, on the same date or close to that date each month (for example, the date may be different if it falls on a Sunday or a holiday);","TERMS_AND_CON_DESC_LIST_1_ITEM2":"You will be notified at least 10 days in advance on your bill of the amount due, which may vary due to charges you incurred;","TERMS_AND_CON_DESC_LIST_1_ITEM3":"For services for which a bill is not provided, you waive the requirement to be notified of the amount before the payment when the amount remains the same or is as previously agreed.","TERMS_AND_CON_DESC_LIST_2":"To <strong>add funds</strong> to your account for prepaid services:","TERMS_AND_CON_DESC_LIST_2_ITEM1":"We will debit your bank account or charge your credit card the amount set according to the criteria you selected;","TERMS_AND_CON_DESC_LIST_2_ITEM2":"<strong>You waive the requirement to be notified of the amount before the payment when the amount is the same as you selected or lower.</strong>","TERMS_AND_CON_DESC_2":"For <strong>other types of payments</strong>, we will obtain your authorization before the payment. In some circumstances, we may also use your pre-authorized payment method to make refunds.","TERMS_AND_CON_DESC_3":"If any payment is not compliant, you may have certain rights such as requesting its refund. For more information or to cancel this authorization, call us or go to MyBell (mybell.ca). When you cancel your authorization, you must notify us at least 30 days before the next pre-authorized payment date. For pre-authorized debits made with a bank account, to obtain a sample cancellation form, or for more information on your right to cancel this authorization, contact your financial institution or <a class=\\"focus-visible:payment-outline-blue focus-visible:payment-outline focus-visible:payment-outline-2 focus-visible:payment-outline-offset-3 focus-visible:payment-rounded-6 payment-underline-offset-2 payment-text-14 payment-text-blue payment-underline payment-leading-18 hover:payment-text-blue-1 hover:payment-no-underline\\" href=\\"#\\">visit payments.ca.</a>","TERMS_AND_CON_DESC_4":"Bell ","TERMS_AND_CON_ZIP_CODE":"P.O Box 9000","TERMS_AND_CON_REGION":"North York, Ontario M3C 2X7","TERMS_AND_CON_TEL":"**************","TERMS_AND_CONDITION_HEADING_QC":"Autorisation pour prélèvement automatique","TERMS_AND_CON_TITLE_QC":"Autorisation pour prélèvement automatique","TERMS_AND_CON_DESC_1_QC":"Vous nous autorisez, Bell, à mettre en place le paiement par prélèvement automatique en utilisant les renseignements de paiement que vous avez fournis, comme suit.","TERMS_AND_CON_DESC_LIST_1_QC":"Pour les paiements mensuels:","TERMS_AND_CON_DESC_LIST_1_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit le montant total dû, à la même date ou à une date près de cette date chaque mois (par exemple, la date peut être différente si elle tombe un dimanche ou un jour férié);","TERMS_AND_CON_DESC_LIST_1_ITEM2_QC":"Vous serez avisé au moins 10 jours à l’avance sur votre facture du montant dû, qui peut varier en fonction des frais que vous avez engagés;","TERMS_AND_CON_DESC_LIST_1_ITEM3_QC":"Pour les services pour lesquels aucune facture n’est fournie, vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant demeure le même ou correspond à celui convenu précédemment.","TERMS_AND_CON_DESC_LIST_2_QC":"Pour <strong>ajouter des fonds</strong> à votre compte pour les services prépayés :","TERMS_AND_CON_DESC_LIST_2_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit, le montant établi selon les critères que vous avez sélectionnés;","TERMS_AND_CON_DESC_LIST_2_ITEM2_QC":"<strong>Vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant est le même que celui que vous avez sélectionné ou inférieur.</strong>","TERMS_AND_CON_DESC_2_QC":"Pour les <strong>autres types de paiements</strong>,  nous obtiendrons votre autorisation avant le prélèvement. Dans certaines circonstances, nous pouvons également utiliser votre mode de paiement par prélèvement automatique pour effectuer des remboursements.","TERMS_AND_CON_DESC_3_QC":"Si un prélèvement n’est pas conforme à l’autorisation, vous pouvez avoir certains droits, comme demander son remboursement. Pour en savoir plus ou pour annuler cette autorisation, appelez-nous ou rendez-vous sur MonBell (monbell.ca). Lorsque vous annulez votre autorisation, vous devez nous en aviser au moins 30 jours avant la date du prochain prélèvement. Pour les prélèvements automatiques effectués avec un compte bancaire, pour obtenir un exemple de formulaire d’annulation ou pour en savoir plus sur votre droit d’annuler cette autorisation, communiquez avec votre institution financière ou visitez paiements.ca.","TERMS_AND_CON_DESC_4_QC":"Bell Canada ","TERMS_AND_CON_ZIP_CODE_QC":"C.P. 8712, succ. Centre-Ville","TERMS_AND_CON_REGION_QC":"Montréal (Québec) H3C 3P6","TERMS_AND_CON_TEL_QC":"Tél.: **************","BANK_ACCOUNT_LABEL":"Bank Account","UPDATE_BANK_ACCOUNT_LABEL":"Bank account","TRANSIT_ACC_NO_PNG":"/Styles/BRF3/content/img/transit-account-number.png","INTERACT_VERIFICATION_V2_PNG":"/Styles/BRF3/content/img/interac-logos/interac-verification-v2-en.png","MASTER_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/mastercard.png","VISA_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/visa.png","AMEX_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/amex.png","FRONT_CC_PNG":"/Styles/BRF3/content/img/Front.png","BACK_CC_PNG":"/Styles/BRF3/content/img/Back.png","SELECT_BILLS_CC_DESC":"{CreditCardType} ending in {CCFourDigits}, expiring {ExpiryDate}","NEW_CREDIT_ACCOUNT_LABEL":"New credit account","SELECT_BILLS_BANK_DESC":"{BankName} account {BankMaskedDigits}","ALERT_GREAT_NEWS":"Great news","ALERT_GREAT_NEWS_DESC":" - by setting up pre-authorized debit payments, the following services will be eligible for an Autopay credit:","ALERT_GREAT_NEWS_DESC_1":" - you’ll receive an Autopay credit.","ALERT_GREAT_NEWS_NOTE":"Note: ","ALERT_GREAT_NEWS_NOTE_DESC":"If you have any upcoming changes to your account, they may impact the Autopay credit above. Any changes to the credit will take effect within 1 to 2 billing periods.","BANK_ACCOUNT_AUTOMATIC_LABEL":"Fill in your bank account details automatically","BANK_ACCOUNT_AUTOMATIC_DESCRIPTION":"Simply use the Interac® verification service to securely sign in to your bank’s website. Available for the following banks:","BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Enter your bank account details manually","BANK_ACCOUNT_MANUAL_DETAILS_DESCRIPTION":"Bell accepts payments from most banks and credit unions in Canada.","BANK_NAME_LABEL":"Bank","BANK_HOLDER_NAME_LABEL":"Account holder name","BANK_TRANSIT_NUMBER_LABEL":"Transit number","BANK_TRANSIT_NUMBER_DESCRIPTION":"5 digits","BANK_ACCOUNT_NUMBER_LABEL":"Account number","BANK_ACCOUNT_NUMBER_DESCRIPTION":"7 to 12 digits","BANK_ACCOUNT_FETCHED_LABEL":"Account number filled automatically Please confirm this is the correct account.","BANK_NEED_HELP":"Need help?","BANK_NEW_BANK_ACCOUNT_LABEL":"New bank account","SELECT_REQUIRED_LEGEND":"Required","CC_IMAGE_SR_LABEL":"Accepted credit cards: MasterCard Visa AMEX","LOAD_MORE":"Load More","PAYMENT_METHOD":"Payment method","ACCOUNT_HOLDER":"Account holder name ","BANK_NAME":"Bank ","TRANSIT_NUMER":"Transit number ","ACCOUNT_NUMBER":"Account number ","BANK_HOLDER_NAME_ERROR_LABEL":"Enter a valid name","BANK_NAME_ERROR_LABEL":"Select a bank","BANK_TRANSIT_ERROR_LABEL":"Enter a valid, 5-digit transit number","BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL":"Enter a valid bank account number of 7 to 12 digits","ALERT_ERROR_GENERAL_DESC":"This information is required.","PAGE_TITLE_CONFIRMATON":"Confirmation: Change pre-authorized payments document","INTERAC_BOX_LOGO":"/Styles/BRF3/content/img/interac-logos/Interac_box_logo.png","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_SINGLE":"Avoid late payment charges by paying your current account balance.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_1":"Pre-authorized payments will begin on your next bill. You can pay your current account balance of ","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_2":" separately with a one-time payment to avoid late payment charges.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_MULTI":"Avoid late payment charges by paying your current account balance.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_MULTI":"Avoid late payment charges by paying your current account balances.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_MULTI":"Pre-authorized payments will begin on your next bill. You can pay your current account balance separately with a one-time payment to avoid late payment charges.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_DESC_MULTI":"Pre-authorized payments will begin on your next bill. You can pay your current account balances separately with a one-time payment to avoid late payment charges.","CTA_MAKE_PAYMENT_LINK":"/Payment/MakePayment","LOADER":"Loading data. Please wait…","INTERAC_FETCHED_LABEL":"Account number filled automatically","INTERAC_FETCHED_SUBTITLE":"Please confirm this is the correct account.","ALERT_ERROR_HEADING_INTERAC":"We experienced a technical issue","ALERT_ERROR_HEADING_INTERAC_SR":"Error: We experienced a technical issue","ALERT_ERROR_HEADING_INTERAC_DESC":"Sorry, we were unable to process your request due to a technical issue. You can enter your bank account details manually instead.","FAILURE_API_BAN_HEADING":"Something went wrong","FAILURE_API_BAN_HEADING_SR":"Error, Something went wrong","FAILURE_API_BAN_MAIN_DESC":"Sorry, we were unable to process your request.","FAILURE_API_BAN_MAIN_DESC_2":"Please try again.","FAILURE_API_BAN_SUB_DESC":"If the issue persists:","FAILURE_API_BAN_SUB_DESC_LISTITEM_1":"Use a different credit card or set up pre-authorized debit payments instead.","FAILURE_API_BAN_SUB_DESC_LISTITEM_2":"Wait a little while and try again later.","FAILURE_API_BAN_BUTTON":"Try again","LOADER_SUBMIT":"Submitting payment information","LOADER_SUBMIT_DESC":"Please wait","LABEL_LOADED_OFFERS_DEBIT_TITLE":"-the following services are eligible for an Autopay credit:","LABEL_LOADED_OFFER_DEBIT_TITLE":"-the following service is eligible for an Autopay credit:","GREAT_NEWS":"Great news","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING":"Note:","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE":"If you have any upcoming changes to your account, they may impact the Autopay credits above. Any changes to the credits will take effect within 1 to 2 billing periods.","MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE":"If you have any upcoming changes to your account, they may impact the Autopay credit above. Any changes to the credit will take effect within 1 to 2 billing periods.","SORRY_MESSAGE":"Sorry, the Autopay credit isn’t available for this payment method.","LABEL_LOADED_OFFERS_CREDIT_TITLE":"– by setting up pre-authorized credit card payments, the following services will be eligible for an Autopay credit:","LABEL_LOADED_OFFER_CREDIT_TITLE":"– by setting up pre-authorized credit card payments, the following service will be eligible for an Autopay credit:","REVIEW_PAGE_AUTOPAY_CREDIT":"Great news – you’ll receive an Autopay credit.","AUTOPAY_ALERT":"Your Autopay credit will start within 1 to 2 billing periods.","CHANGE_PAYMENT":"Change payment information","SWITCH_TO_DEBIT_PAYMENT":"Switch to debit payments","UPDATE_CREDITCARD_PAYMENT":"Credit card","SWITCH_TO_CREDIT_CARD":"Switch to credit card payments","SELECT_BILLS_HEADING_DESC_MANAGE":"Choose the bill(s) you want to pay with this update:","CANCEL_PAYMENT_LABEL":"Cancel pre-authorized payments","CANCEL_PREAUTH_SUBHEADING":"Are you sure you want to cancel pre-authorized payments?","CANCEL_PREAUTH_SUBHEADING_INFO":"Pre-authorized payments ensure your Bell bill is always paid on time and help you avoid late payment charges.","CANCEL_PREAUTH_POSSIBLE_CREDITS_NOTE":"<strong>Note:</strong> Any autopay credits you may currently be receiving will be lost as a result of this change.","FINAL_PAYMENT":"Final payment","FINAL_PAYMENT_HEADING":"One final pre-authorized payment will be withdrawn.","ALERT_DEBIT_SUCCES":"Pre-authorized payments have been updated and will begin on your <strong>next billing cycle.</strong>","ALERT_CREDIT_SUCCESS":"Pre-authorized payments have been set up and will begin on your <strong>next bill.</strong>","PAYMENT_CHANGE_HEADING":"Change payment information","PAYMENT_METHOD_DEBIT":"Debit","LABEL_LOADED_OFFER_REMOVED_DEBIT_TITLE":"The following Autopay credit will be removed:","LABEL_LOADED_OFFERS_REMOVED_DEBIT_TITLE":"The following Autopay credits will be removed:","AUTOPAY_CREDITS_TITLE":"Autopay credits","AUTOPAY_TRANSACTION_NOTE":"<strong>Note:</strong> Your recent or pending transaction(s) may impact the credits above; any changes to them will be reflected on your bill in 1-2 billing periods.","DEBIT_PAYMENT_OFFER_AMOUNT":"${amount}/mo.","DEBIT_PAYMENT_OFFER_AMOUNT_SR":"{amount} dollars per month","CREDIT_CARD_NUMBER_LABEL_1":"Credit card number","CREDIT_CARD_NUMBER_SR_LABEL_1":"Required, Credit card number","CREDIT_CARD_NAME_LABEL_1":"Cardholder name","CREDIT_CARD_NAME_SR_LABEL_1":"Required, Cardholder name","CREDIT_CARD_EXPIRY_DATE_LABEL":"Required, Expiration date","CREDIT_CARD_EXPIRY_DATE_SR_LABEL":" {month} / {year}","CREDIT_CARD_MONTH_TEXT":"month","CREDIT_CARD_YEAR_TEXT":"year","DEBIT_ACCOUNT_HOLDER_NAME_SR_TEXT":"Required, Account holder name","DEBIT_ACCOUNT_TRANSIT_SR_TEXT":"Required, Transit number,","DEBIT_ACCOUNT_ACCOUNT_NUMBER_SR_TEXT":"Required, Account number,","BANK_NAME_SR_LABEL":"Required, Bank Select an option","REVIEW_BANK_ACCOUNT_NUMBER":"Account number ending in","REVIEW_CREDIT_CARD_NUMBER":"Card number ending in","EDIT_SR_TEXT":"Edit payment method","NOT_PREAUTH_NOTE":"<strong>Note: </strong> You also selected {account} which is not currently on pre-authorized payments. No change will be made.","CLOSE_ICON_SR":"Close dialog box","SUCCESS_TOAST_MESSAGE":"Your pre-authorized payments have been cancelled.","CANCEL_FAILED_BANS_ACCOUNT_TITLE":"{accounttype} account number","CANCEL_FAILED_HEADING":"<strong>An error occurred –</strong> We were unable to cancel pre-authorized payments for the following bills. Please try again.","CANCEL_FAILED_HEADING_SINGLE":"<strong>An error occurred –</strong> We were unable to cancel pre-authorized payment for the following bill. Please try again.","CANCEL_FAILED_HEADING_MULTI":"<strong>An error occurred –</strong> We were unable to cancel pre-authorized payments for the following bills. Please try again.","CANCEL_FAILED_SUCCESS_SCENARIO_HEADING":"Pre-authorized payments have been cancelled for the remaining account(s) requested, and will take effect on your <strong>next billing cycle.</strong>","SELECT_ALL_BAN":"Select all bills","NOT_ON_PREAUTH":"Not on pre-authorized payments","InteracSupportedFinancialInstitutions":"BMO,CIBC,Desjardins,RBC,Scotiabank,TD","LABEL_LOADED_OFFER_REMOVED_DEBIT_SUMMARY":"Your Autopay credit will be removed.","LABEL_LOADED_OFFERS_REMOVED_DEBIT_SUMMARY":"Your Autopay credits will be removed."},"fr":{"NOT_ON_PREAUTH":"Prélèvements automatique non configurés","SELECT_ALL_BAN":"Sélectionnez toutes les factures","ALERT_CONFIRMATION_INFO_HEADING":"Évitez des frais de retard en payant le solde actuel de votre compte.","ALERT_CONFIRMATION_INFO_DESC":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément votre solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","ALERT_CONFIRMATION_INFO_BUTTON_DESC":"Effectuer un paiement","ALERT_CONFIRMATION_INFO_DESC_1":"Pre-authorized payments will only begin on your next billing period. You must pay your ","ALERT_CONFIRMATION_INFO_DESC_2":"balance in a separate and final one-time payment, or risk late fees. ","EXISTING_CC_TITLE":"Sélectionnez une carte de crédit","EXISTING_BANK_TITLE":"Sélectionner un compte bancaire","EXISTING_BANK_TITLE_SR":"Requis, Sélectionner un compte bancaire","EXISTING_BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Choisir un compte bancaire déjà associé à votre compte Bell, ou en ajouter un autre manuellement","SELECT_BILLS_HEADING":"Sélectionner les factures","SELECT_BILLS_HEADING_SINGULAR":"Sélectionner les factures","SELECT_BILLS_ACCOUNT_TITLE":"Numéro de compte {accounttype}","SELECT_BILLS_HEADING_DESC":"Configurez les paiements par prélèvement automatique pour la ou les factures suivantes :","SELECT_BANK_TITLE":"Requis, Sélectionner un compte bancaire","SELECT_BANK_PLACEHOLDER":"Sélectionner votre institution financière","ACCOUNT_TYPE_ONE_BILL":"Facture unique","ACCOUNT_TYPE_MYBILL":"One Bill","ACCOUNT_TYPENAME_ONEBILL":"Facture unique","ACCOUNT_TYPENAME_MYBILL":"Ma facture","ACCOUNT_TYPENAME_MOBILITY":"Mobilité","ACCOUNT_TYPENAME_TV":"TV","ACCOUNT_TYPENAME_INTERNET":"Internet","ACCOUNT_TYPENAME_HOMEPHONE":"Home Phone","ACCOUNT_TYPENAME_MOBILITY_ONEBILL":"Mobility and One Bill","ACCOUNT_TYPENAME_SINGLEBAN":"Single Ban","ACCOUNT_BALANCE":"Votre solde actuel est de","BACK_TO_MY_BELL":"Retour à MonBell","CTA_NEXT":"Suivant","CTA_EDIT":"Modifier","CTA_CLOSE":"Fermer la boîte de dialogue","CTA_EXPAND_TERMS":"Afficher les modalités de service","CTA_COLLAPSE_TERMS":"Masquer les modalités de service","CTA_CONFIRM":"Confirmer et envoyer","CTA_CANCEL":"Annuler","CTA_INTERAC":"Se connecter avec Interac®","CTA_INTERAC_SR":"Se connecter avec Interac marque déposée","CREDIT_CARD_LABEL":"Carte de crédit","CREDIT_CARD_TYPE_LABEL":"Type de carte","CREDIT_CARD_NAME_LABEL":"Nom du titulaire de la carte","CREDIT_CARD_NAME_DESC_INPUT_LABEL":"Tel qu’il apparaît sur la carte","CREDIT_CARD_NUMBER_LABEL":"Numéro de carte de crédit","CREDIT_CARD_NUMBER_DESC_INPUT_LABEL":"15 ou 16 chiffres","CREDIT_CARD_EXPIRY_LABEL":"Date d’expiration","CREDIT_CARD_EXPIRY_SR_MONTH_LABEL":"Requis, Date d’expiration mois","CREDIT_CARD_EXPIRY_SR_YEAR_LABEL":"Requis, Date d’expiration année","CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Code de sécurité","CREDIT_CARD_NAME_SR_LABEL":"Requis, Nom du titulaire de la carte ","CREDIT_CARD_NUMBER_SR_LABEL":"Requis, Numéro de carte de crédit","CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL":"Requis, Code de sécurité","ERROR_CREDIT_CARD_NAME_INPUT_LABEL":"Entrer un nom de titulaire de carte valide ","ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL":"Entrer un numéro de carte de crédit valide ","ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL":"Entrer une date d’expiration valide","ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Entrer un code de sécurité valide ","CREDIT_CARD_VALID":"Valable jusqu\'au","CREDIT_CARD_MONTH_PLACEHOLDER":"MM","CREDIT_CARD_YEAR_PLACEHOLDER":"AA","REQUIRED_LABEL":"*Renseignement requis","MODAL_NO_NAME":"Il n’y a pas de nom sur la carte?","MODAL_NO_NAME_TITLE":"Pas de nom sur la carte de crédit?","MODAL_NO_NAME_DESC":"Si vous utilisez une carte de crédit prépayée sur laquelle ne figure aucun nom, entrez le nom du titulaire de compte Bell. ","MODAL_SECURITY_CODE":"Qu’est-ce que le code de sécurité de la carte?","MODAL_SECURITY_CODE_TITLE":"Qu’est-ce que le code de sécurité?","MODAL_SECURITY_CODE_DESC":"Le code de sécurité de la carte de crédit (CSC) est une mesure de prévention contre la fraude. Il permet de vérifier que la carte de crédit est en votre possession. Ce code est parfois appelé code de vérification de carte (CVC) ou valeur de vérification de carte (CVV). Voici où trouver votre code de sécurité :","CARD_TYPE_VISA_MASTERCARD":"Visa et Mastercard","CARD_TYPE_VISA_MASTERCARD_DESC":"Un numéro de 3 chiffres au verso de la carte de crédit.","CARD_TYPE_AMERICAN_EXP":"American Express","CARD_TYPE_AMERICAN_EXP_DESC":"Un numéro de 4 chiffres au recto de la carte de crédit.","MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE":"Trouver votre numéro de succursale et votre numéro de compte","MODAL_BANK_STATEMENT_TITLE":"Sur un relevé bancaire ou un chèque","MODAL_BANK_STATEMENT_DESC":"Vous trouverez vos renseignements bancaires sur un relevé bancaire ou un chèque. Vous pouvez aussi trouver ces renseignements dans votre application bancaire ou en accédant à votre compte bancaire en ligne.","MODAL_TRANSIT_NUMBER_DESC":"Le numéro de succursale comporte 5 chiffres.","MODAL_ACCOUNT_NUMBER_DESC":"Votre numéro de compte comporte de 7 à 12 chiffres.","ALERT_ERROR_HEADING":"Une erreur est survenue pendant le traitement de votre demande.","ALERT_ERROR_HEADING_SR":"Une erreur est survenue pendant le traitement de votre demande.","ALERT_ERROR_INFO_REQUIRED":"– Ce renseignement est requis.","ALERT_ERROR_SELECT_BILL_INFO":"Sélectionner une facture","ALERT_ERROR_SELECT_BILL_DESC":"Ce renseignement est requis.","ALERT_ERROR_SELECT_BILL_INFO_REQUIRED":"Select a bill – This information is required.","ALERT_ERROR_ONE_SELECT_BILL":"Sélectionnez au moins une facture","ALERT_ERROR_HEADING_SOME_BALANCE":"<strong>Certains de vos soldes actuels</strong> n’ont pas été payés en raison d’une erreur lors du traitement de votre demande.","ALERT_ERROR_OTP_ALL_BALANCE":"<strong>Votre ou vos soldes actuels</strong> n’ont pas été payés en raison d’une erreur lors du traitement de votre demande.","ALERT_ERROR_OTP_BALANCE":"<strong>Votre solde de {balance} $ n\'a pas été payé.</strong>","ALERT_ERROR_OTP_BALANCE_SR":"Votre solde de {balance} dollars n\'a pas été payé.","ALERT_ERROR_OTP_BALANCE_DESC":"Une erreur s’est produite lors du traitement de votre paiement unique. Veuillez payer le solde actuel de votre compte afin d’éviter des frais de retard.","ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR":"Même si vous avez choisi de ne pas payer le solde suivant, un paiement séparé sera également requis:","ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL":"Même si vous avez choisi de ne pas payer le(s) solde(s) suivant(s), ils nécessiteront également un paiement séparé:","SELECT_PAYMENT_METHOD_HEADING":"Choisir le mode de paiement","PAY_CURRENT_BALANCE_HEADING":"Payer votre solde actuel","PAY_CURRENT_BALANCE_DESC":"Les paiements par prélèvement automatique débuteront <strong class=\'brui-text-black\'>à partir de votre prochaine facture.</strong> Vous pouvez payer séparément votre solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","PAY_CURRENT_BALANCE_OPTED_IN":"Votre paiement unique sera traité dans un délai de <strong>trois à cinq jours ouvrables</strong>. Par la suite, il restera un solde de {balance} dans votre compte.","PAY_CURRENT_BALANCE_OPTED_IN_PACC":"Votre paiement unique sera traité <strong>dès que vous enverrez cette transaction</strong>.","PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR":"<p>Votre compte comporte un solde à payer.</p><p class=\'brui-mt-10\'>Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément votre solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.</p>","PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL":"<p>Vos comptes comportent des soldes à payer.</p><p class=\'brui-mt-10\'>Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément vos soldes actuels au moyen d’un paiement unique, afin d’éviter des frais de retard.</p>","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_IN":"Votre paiement unique sera traité dans un délai de <strong>trois à cinq jours ouvrables</strong>. Par la suite, il restera un solde de {balance} dans votre compte.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_SINGULAR":"Vous avez choisi de ne pas payer le solde actuel de votre compte.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_PLURAL":"Vous avez choisi de ne pas payer le solde actuel de votre compte.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_SINGULAR":"Votre autre compte comporte un solde à payer.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_PLURAL":"Vos autres comptes comportent des soldes à payer.","PAYMENT_AMOUNT":"Montant du paiement","UNPAID_BALANCE":"Solde impayé","PAY_MY_BALANCE":"Payer mon solde de","PAY_CURRENT_BALANCE_NOTE_1":"Nous utiliserons les renseignements relatifs au compte bancaire ci-dessus pour le paiement.","PAY_CURRENT_BALANCE_NOTE_2":"Remarque : Si vous avez effectué un paiement au cours des trois à cinq derniers jours ouvrables, il se peut que votre solde ne soit pas encore mis à jour.","TERMS_AND_CONDITION_HEADING":"Modalités et conditions","TERMS_AND_CONDITION_DISCLAIMER":"En cliquant sur Confirmer et envoyer, je confirme avoir lu et accepté les modalités de service Bell et les tarifs du ou des services sélectionnés","TRANSACTION_SUBMITTED_HEADING":"Transaction envoyée","CONFIRMATION_HEADING":"Confirmation","ALERT_CONFIRMATION_SUCCESS_HEADING":"Voici ce qui se passe :","ALERT_CONFIRMATION_SUCCESS_MESSAGE":"Des paiements par prélèvement automatique ont été configurés et commenceront sur votre <strong>prochaine facture.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_V2":"Pre-authorized payments have been set up and will begin on your <strong>next billing cycle.</strong>","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC":"Votre paiement unique pour le compte <strong> {account} </strong> a bien été traité.","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD":"Votre paiement unique porté au compte <strong> {account} </strong> sera traité dans un délai de <strong>trois à cinq jours ouvrables.</strong>","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD_FR":"Votre paiement unique porté au compte no <strong> {account} </strong> sera traité dans un délai de <strong>trois à cinq jours ouvrables.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS":"Votre crédit pour autopaiement débutera d’ici une à deux périodes de facturation.","ALERT_CONFIRMATION_SUCCESS_NUMBER":"Numéro de confirmation :","ALERT_CONFIRMATION_SUCCESS_DESC":"Nous avons envoyé un courriel de confirmation à l’adresse {email}. S’il ne s’agit pas de la bonne adresse de courriel, veuillez","ALERT_CONFIRMATION_SUCCESS_DESC_LINK":"mettre votre profil à jour.","ALERT_CONFIRMATION_SUCCESS_CONFIRM_NO":"Confirmation Number:","ALERT_CONFIRMATION_CURRENT_BALANCE":"The current balance for {account} has been paid.","PAYMENT_SUMMARY_TITLE":"Sommaire des prélèvements","PAYMENT_INFORMATION_TITLE":"Renseignements de paiement","BILL_INFORMATION_TITLE":"Renseignements sur la facture","BILL_INFORMATION_ACCOUNT_NUMBER":"Compte","PAYMENT_METHOD_TITLE":"Mode de paiement","CURRENT_BALANCE_TITLE":"Solde actuel","SUMMARY_CURRENT_BAL_OTP_FAILURE":"Le paiement de votre solde actuel n’a pas été traité. Veuillez effectuer un paiement unique distinct.","TERMS_AND_CON_TITLE":"Autorisation pour prélèvement automatique","TERMS_AND_CON_DESC_1":"Vous nous autorisez, Bell, à mettre en place le paiement par prélèvement automatique en utilisant les renseignements de paiement que vous avez fournis, comme suit.","TERMS_AND_CON_DESC_LIST_1":"Pour les paiements mensuels:","TERMS_AND_CON_DESC_LIST_1_ITEM1":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit le montant total dû, à la même date ou à une date près de cette date chaque mois (par exemple, la date peut être différente si elle tombe un dimanche ou un jour férié);","TERMS_AND_CON_DESC_LIST_1_ITEM2":"Vous serez avisé au moins 10 jours à l’avance sur votre facture du montant dû, qui peut varier en fonction des frais que vous avez engagés;","TERMS_AND_CON_DESC_LIST_1_ITEM3":"Pour les services pour lesquels aucune facture n’est fournie, vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant demeure le même ou correspond à celui convenu précédemment.","TERMS_AND_CON_DESC_LIST_2":"Pour <strong>ajouter des fonds</strong> à votre compte pour les services prépayés :","TERMS_AND_CON_DESC_LIST_2_ITEM1":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit, le montant établi selon les critères que vous avez sélectionnés;","TERMS_AND_CON_DESC_LIST_2_ITEM2":"<strong>Vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant est le même que celui que vous avez sélectionné ou inférieur.</strong>","TERMS_AND_CON_DESC_2":"Pour les <strong>autres types de paiements</strong>,  nous obtiendrons votre autorisation avant le prélèvement. Dans certaines circonstances, nous pouvons également utiliser votre mode de paiement par prélèvement automatique pour effectuer des remboursements.","TERMS_AND_CON_DESC_3":"Si un prélèvement n’est pas conforme à l’autorisation, vous pouvez avoir certains droits, comme demander son remboursement. Pour en savoir plus ou pour annuler cette autorisation, appelez-nous ou rendez-vous sur MonBell (monbell.ca). Lorsque vous annulez votre autorisation, vous devez nous en aviser au moins 30 jours avant la date du prochain prélèvement. Pour les prélèvements automatiques effectués avec un compte bancaire, pour obtenir un exemple de formulaire d’annulation ou pour en savoir plus sur votre droit d’annuler cette autorisation, communiquez avec votre institution financière ou visitez paiements.ca.","TERMS_AND_CON_DESC_4":"Bell Canada ","TERMS_AND_CON_ZIP_CODE":"C.P. 8712, succ. Centre-Ville","TERMS_AND_CON_REGION":"Montréal (Québec) H3C 3P6","TERMS_AND_CON_TEL":"Tél.: **************","TERMS_AND_CONDITION_HEADING_QC":"Modalités et conditions","TERMS_AND_CON_TITLE_QC":"Autorisation pour prélèvement automatique","TERMS_AND_CON_DESC_1_QC":"Vous nous autorisez, Bell, à mettre en place le paiement par prélèvement automatique en utilisant les renseignements de paiement que vous avez fournis, comme suit.","TERMS_AND_CON_DESC_LIST_1_QC":"Pour les paiements mensuels:","TERMS_AND_CON_DESC_LIST_1_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit le montant total dû, à la même date ou à une date près de cette date chaque mois (par exemple, la date peut être différente si elle tombe un dimanche ou un jour férié);","TERMS_AND_CON_DESC_LIST_1_ITEM2_QC":"Vous serez avisé au moins 10 jours à l’avance sur votre facture du montant dû, qui peut varier en fonction des frais que vous avez engagés;","TERMS_AND_CON_DESC_LIST_1_ITEM3_QC":"Pour les services pour lesquels aucune facture n’est fournie, vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant demeure le même ou correspond à celui convenu précédemment.","TERMS_AND_CON_DESC_LIST_2_QC":"Pour <strong>ajouter des fonds</strong> à votre compte pour les services prépayés :","TERMS_AND_CON_DESC_LIST_2_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit, le montant établi selon les critères que vous avez sélectionnés;","TERMS_AND_CON_DESC_LIST_2_ITEM2_QC":"<strong>Vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant est le même que celui que vous avez sélectionné ou inférieur.</strong>","TERMS_AND_CON_DESC_2_QC":"Pour les <strong>autres types de paiements</strong>,  nous obtiendrons votre autorisation avant le prélèvement. Dans certaines circonstances, nous pouvons également utiliser votre mode de paiement par prélèvement automatique pour effectuer des remboursements.","TERMS_AND_CON_DESC_3_QC":"Si un prélèvement n’est pas conforme à l’autorisation, vous pouvez avoir certains droits, comme demander son remboursement. Pour en savoir plus ou pour annuler cette autorisation, appelez-nous ou rendez-vous sur MonBell (monbell.ca). Lorsque vous annulez votre autorisation, vous devez nous en aviser au moins 30 jours avant la date du prochain prélèvement. Pour les prélèvements automatiques effectués avec un compte bancaire, pour obtenir un exemple de formulaire d’annulation ou pour en savoir plus sur votre droit d’annuler cette autorisation, communiquez avec votre institution financière ou visitez paiements.ca.","TERMS_AND_CON_DESC_4_QC":"Bell Canada ","TERMS_AND_CON_ZIP_CODE_QC":"C.P. 8712, succ. Centre-Ville","TERMS_AND_CON_REGION_QC":"Montréal (Québec) H3C 3P6","TERMS_AND_CON_TEL_QC":"Tél.: **************","BANK_ACCOUNT_LABEL":"Du compte bancaire","TRANSIT_ACC_NO_PNG":"/Styles/BRF3/content/img/transit-account-number.png","INTERACT_VERIFICATION_V2_PNG":"/Styles/BRF3/content/img/interac-logos/interac-verification-v2-en.png","MASTER_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/mastercard.png","VISA_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/visa.png","AMEX_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/amex.png","FRONT_CC_PNG":"/Styles/BRF3/content/img/Front.png","BACK_CC_PNG":"/Styles/BRF3/content/img/Back.png","INTERAC_BOX_LOGO":"/Styles/BRF3/content/img/interac-logos/Interac_box_logo.png","SELECT_BILLS_CC_DESC":"{CreditCardType} se terminant par {CCFourDigits}, expirant le {ExpiryDate}","NEW_CREDIT_ACCOUNT_LABEL":"Nouvelle carte de crédit","SELECT_BILLS_BANK_DESC":"{BankName} account {BankMaskedDigits}","ALERT_GREAT_NEWS":"Bonne nouvelle","ALERT_GREAT_NEWS_DESC":" : avec des prélèvements automatiques par débit, les services suivants donneront droit à un crédit pour autopaiement :","ALERT_GREAT_NEWS_DESC_1":" : vous aurez droit à un crédit pour autopaiement.","ALERT_GREAT_NEWS_NOTE":"Remarque : ","ALERT_GREAT_NEWS_NOTE_DESC":"S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur le crédit pour autopaiement ci-dessus. Tout changement apporté au crédit entrera en vigueur d’ici une à deux périodes de facturation.","BANK_ACCOUNT_AUTOMATIC_LABEL":"Entrer automatiquement les renseignements relatifs à votre compte","BANK_ACCOUNT_AUTOMATIC_DESCRIPTION":"Utilisez le service de vérification InteracMD pour vous connecter en toute sécurité au site Web de votre institution financière. Offert pour les institutions suivantes :","BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Entrer manuellement les renseignements relatifs à votre compte","BANK_ACCOUNT_MANUAL_DETAILS_DESCRIPTION":"Bell accepte les paiements de la plupart des banques, caisses populaires et coopératives de crédit au Canada.","BANK_NAME_LABEL":"Institution financière","BANK_HOLDER_NAME_LABEL":"Nom du titulaire du compte","BANK_TRANSIT_NUMBER_LABEL":"Numéro de succursale","BANK_TRANSIT_NUMBER_DESCRIPTION":"5 chiffres","BANK_ACCOUNT_NUMBER_LABEL":"Numéro de compte","BANK_ACCOUNT_NUMBER_DESCRIPTION":"7 à 12 chiffres","BANK_ACCOUNT_FETCHED_LABEL":"Numéro de compte entré automatiquement Veuillez confirmer qu’il s’agit du bon compte.","BANK_NEED_HELP":"Besoin d’aide?","BANK_NEW_BANK_ACCOUNT_LABEL":"Nouvelle carte bancaire","SELECT_REQUIRED_LEGEND":"Requis","CC_IMAGE_SR_LABEL":"Cartes de crédit acceptées: Visa MasterCard American Express","LOAD_MORE":"Charger Plus","PAYMENT_METHOD":"Mode de paiement","ACCOUNT_HOLDER":"Nom du titulaire du compte ","BANK_NAME":"Institution financière  ","TRANSIT_NUMER":"Numéro de succursale ","ACCOUNT_NUMBER":"Numéro de compte ","BANK_HOLDER_NAME_ERROR_LABEL":"Entrer un nom valide","BANK_NAME_ERROR_LABEL":"Sélectionner votre institution financière","BANK_TRANSIT_ERROR_LABEL":"Entrer un numéro de succursale de 5 chiffres valide","BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL":"Entrer un numéro de compte bancaire de 7 à 12 chiffres valide","ALERT_ERROR_GENERAL_DESC":"Ce renseignement est requis.","PAGE_TITLE_CONFIRMATON":"Confirmation: Changer les paiements par prélèvement automatique document","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_SINGLE":"Évitez des frais de retard en payant le solde actuel de votre compte.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_1":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément votre solde actuel de ","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_2":" $ au moyen d’un paiement unique, afin d’éviter des frais de retard.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_MULTI":"Évitez des frais de retard en payant les solde actuel de vos comptes.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_MULTI":"Évitez des frais de retard en payant les solde actuel de vos comptes.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_MULTI":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément vos solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_DESC_MULTI":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément vos solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","CTA_MAKE_PAYMENT_LINK":"/Payment/MakePayment","LOADER":"Chargement des données. Veuillez patienter…","INTERAC_FETCHED_LABEL":"Numéro de compte entré automatiquement","INTERAC_FETCHED_SUBTITLE":"Veuillez confirmer qu’il s’agit du bon compte.","ALERT_ERROR_HEADING_INTERAC":"Un problème technique est survenu","ALERT_ERROR_HEADING_INTERAC_SR":"Erreur: Un problème technique est survenu","ALERT_ERROR_HEADING_INTERAC_DESC":"Il nous est malheureusement impossible de traiter votre demande en raison d’un problème technique. Vous pouvez entrer vos renseignements bancaires manuellement.","FAILURE_API_BAN_HEADING":"Un problème est survenu","FAILURE_API_BAN_HEADING_SR":"Erreur, Un problème est survenu","FAILURE_API_BAN_MAIN_DESC":"Il nous est malheureusement impossible de traiter votre demande.","FAILURE_API_BAN_MAIN_DESC_2":"Veuillez réessayer.","FAILURE_API_BAN_SUB_DESC":"Si le problème persiste :","FAILURE_API_BAN_SUB_DESC_LISTITEM_1":"Essayez d’utiliser une autre carte de crédit ou encore de configurer des prélèvements automatiques par débit.","FAILURE_API_BAN_SUB_DESC_LISTITEM_2":"Attendez un moment, puis réessayez.","FAILURE_API_BAN_BUTTON":"Réessayer","LOADER_SUBMIT":"Transmission des renseignements de paiement en cours","LOADER_SUBMIT_DESC":"Veuillez patienter","LABEL_LOADED_OFFERS_TITLE":" – avec des paiements par prélèvement automatique, les services suivants donneront droit à un crédit pour autopaiement :","LABEL_LOADED_OFFER_TITLE":" – avec des paiements par prélèvement automatique, le service suivant donnera droit à un crédit pour autopaiement :","GREAT_NEWS":"Bonne nouvelle","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING":"Remarque :","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE":" S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur les crédits pour autopaiement ci-dessus. Tout changement apporté aux crédits entrera en vigueur d’ici une à deux périodes de facturation.","MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE":" S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur le crédit pour autopaiement ci-dessus. Tout changement apporté au crédit entrera en vigueur d’ici une à deux périodes de facturation.","SORRY_MESSAGE":"Désolé, aucun crédit pour autopaiement n’est offert pour ce mode de paiement.","LABEL_LOADED_OFFERS_CREDIT_TITLE":" : avec des paiements par prélèvement automatique sur carte de crédit, les services suivants donneront droit à un crédit pour autopaiement :","LABEL_LOADED_OFFER_CREDIT_TITLE":" : avec des paiements par prélèvement automatique sur carte de crédit, le service suivant donnera droit à un crédit pour autopaiement :","LABEL_LOADED_OFFERS_DEBIT_TITLE":"- les services suivants donnent droit à un crédit pour autopaiement :","LABEL_LOADED_OFFER_DEBIT_TITLE":"- le service suivant donne droit à un crédit pour autopaiement :","REVIEW_PAGE_AUTOPAY_CREDIT":"Bonne nouvelle : vous aurez droit à un crédit pour autopaiement.","AUTOPAY_ALERT":"Votre crédit pour autopaiement débutera d’ici une à deux périodes de facturation.","CHANGE_PAYMENT":"Modifier les renseignements de paiement","UPDATE_BANK_ACCOUNT_LABEL":"Du compte bancaire","SWITCH_TO_DEBIT_PAYMENT":"Passer aux paiements par débit","UPDATE_CREDITCARD_PAYMENT":"Carte de crédit","SWITCH_TO_CREDIT_CARD":"Passer aux paiements par carte de crédit","SELECT_BILLS_HEADING_DESC_MANAGE":"Choose the bill(s) you want to pay with this update","CANCEL_PAYMENT_LABEL":"Annuler les paiements préautorisés","CANCEL_PREAUTH_SUBHEADING":"Voulez-vous bel et bien annuler les paiements par prélèvement automatique?","CANCEL_PREAUTH_SUBHEADING_INFO":"Avec les prélèvements automatiques, votre facture Bell est toujours payée à temps et vous évitez ainsi des frais de retard.","CANCEL_PREAUTH_POSSIBLE_CREDITS_NOTE":"<strong>Note:</strong> Any autopay credits you may currently be receiving will be lost as a result of this change.","FINAL_PAYMENT":"Paiement final","FINAL_PAYMENT_HEADING":"Un dernier prélèvement automatique sera effectué.","ALERT_DEBIT_SUCCES":"Des paiements par prélèvement automatique ont été configurés et commenceront sur votre <strong>prochaine facture.</strong>","ALERT_CREDIT_SUCCESS":"Des paiements par prélèvement automatique ont été configurés et commenceront sur votre <strong>prochaine facture.</strong>","PAYMENT_CHANGE_HEADING":"Modifier les renseignements de paiement","PAYMENT_METHOD_DEBIT":"Débit","LABEL_LOADED_OFFER_REMOVED_DEBIT_TITLE":"Le crédit pour autopaiement suivant sera retiré :","LABEL_LOADED_OFFERS_REMOVED_DEBIT_TITLE":"Les crédits pour autopaiement suivants seront retirés :","AUTOPAY_CREDITS_TITLE":"Crédits pour autopaiement","AUTOPAY_TRANSACTION_NOTE":"<strong>Note:</strong> Your recent or pending transaction(s) may impact the credits above; any changes to them will be reflected on your bill in 1-2 billing periods.","DEBIT_PAYMENT_OFFER_AMOUNT":"{amount}$/mois","DEBIT_PAYMENT_OFFER_AMOUNT_SR":"{amount} dollars par mois","CREDIT_CARD_NUMBER_LABEL_1":"Numéro de carte de crédit","CREDIT_CARD_NUMBER_SR_LABEL_1":"Requis, Numéro de carte de crédit","CREDIT_CARD_NAME_LABEL_1":"Nom du titulaire de la carte","CREDIT_CARD_NAME_SR_LABEL_1":"Nom du titulaire de la carte","CREDIT_CARD_EXPIRY_DATE_LABEL":"Requis, Date d’expiration","CREDIT_CARD_EXPIRY_DATE_SR_LABEL":" {{month} / {year}}","CREDIT_CARD_MONTH_TEXT":"mois","CREDIT_CARD_YEAR_TEXT":"année","DEBIT_ACCOUNT_HOLDER_NAME_SR_TEXT":"Requis, Nom du titulaire du compte","DEBIT_ACCOUNT_TRANSIT_SR_TEXT":"Requis, Numéro de succursale,","DEBIT_ACCOUNT_ACCOUNT_NUMBER_SR_TEXT":"Requis, Numéro de compte,","BANK_NAME_SR_LABEL":"Requis, Institution financière Sélectionner votre institution financière","REVIEW_BANK_ACCOUNT_NUMBER":"Numéro de compte se terminant par","REVIEW_CREDIT_CARD_NUMBER":"Numéro de carte se terminant par","EDIT_SR_TEXT":"Modifier le mode de paiement","NOT_PREAUTH_NOTE":"<strong>Remarque : </strong>Vous avez également sélectionné le compte {account}, pour lequel les prélèvements automatiques ne sont pas configurés. Aucun changement ne sera apporté.","CLOSE_ICON_SR":"Fermer la boîte de dialogue","SUCCESS_TOAST_MESSAGE":"Vos prélèvements automatiques ont été annulés.","CANCEL_FAILED_BANS_ACCOUNT_TITLE":"Numéro de compte {accounttype}","CANCEL_FAILED_HEADING":"<strong>Une erreur est survenue –</strong> Nous n’avons pu annuler les paiements par prélèvement automatique pour les factures suivantes. Veuillez réessayer.","CANCEL_FAILED_HEADING_SINGLE":"<strong>Une erreur est survenue –</strong> Nous n’avons pu annuler le paiement par prélèvement automatique pour la facture suivante. Veuillez réessayer.","CANCEL_FAILED_HEADING_MULTI":"<strong>Une erreur est survenue –</strong> Nous n’avons pu annuler les paiements par prélèvement automatique pour les factures suivantes. Veuillez réessayer.","CANCEL_FAILED_SUCCESS_SCENARIO_HEADING":"Les paiements par prélèvement automatique ont été annulés pour le ou les comptes restant de votre demande. L’annulation entrera en vigueur lors de votre prochain <strong>cycle de facturation.</strong>","InteracSupportedFinancialInstitutions":"BMO,CIBC,Desjardins,RBC,Scotiabank,TD","LABEL_LOADED_OFFER_REMOVED_DEBIT_SUMMARY":"Votre crédit pour autopaiement sera retiré.","LABEL_LOADED_OFFERS_REMOVED_DEBIT_SUMMARY":"Vos crédits pour autopaiement seront retirés."}}')},function(e,t,n){"use strict";var r,a,i,o,l,u,s,c,m,d,p,f,b,E,_,y,g,v,N,A,C,h,T,I,R,x,S,O,M,L,D,P,B,k,w,U,F,H,Y,j,G,V,z,q,K,W,X,Q,Z,$,J,ee,te,ne,re,ae,ie,oe,le,ue,se,ce,me,de;n.r(t),n.d(t,{OmnitureOnApiFailure:function(){return le},OmnitureOnBoxNameLightBox:function(){return $},OmnitureOnCancelationCompleted:function(){return re},OmnitureOnCancelationFailed:function(){return ie},OmnitureOnCancelationPartiallyCompleted:function(){return ae},OmnitureOnCancelationPartiallyFailed:function(){return oe},OmnitureOnConfirmation:function(){return te},OmnitureOnConfirmationFailure:function(){return ne},OmnitureOnFindTransactionLightBox:function(){return Z},OmnitureOnInteracFailure:function(){return ue},OmnitureOnLoad:function(){return X},OmnitureOnPaymentSelect:function(){return Q},OmnitureOnReview:function(){return ee},OmnitureOnSecurityCodeLightBox:function(){return J},apiConfirmationStatus:function(){return se},cancelPreauthAction:function(){return ce},cancelPreauthFailureAction:function(){return de},cancelPreauthSuccessAction:function(){return me},cardTokenizationError:function(){return F},cardTokenizationSuccess:function(){return H},clearCardNumber:function(){return U},createMultiPaymentAction:function(){return T},createMultiPaymentCompleted:function(){return I},createMultiPaymentFailed:function(){return R},createPaymentAction:function(){return E},createPaymentCompleted:function(){return _},createPaymentFailed:function(){return y},fetchPaymentItems:function(){return i},fetchPaymentItemsFailed:function(){return l},getConfig:function(){return s},getInteracBankInfo:function(){return V},getPassKey:function(){return B},getRedirectUrl:function(){return Y},interacBankInfoFailure:function(){return q},interacBankInfoSuccess:function(){return z},onCardHolderNameChange:function(){return m},onCreditCardExpiryDateChange:function(){return p},onCreditCardNumberChange:function(){return c},onSecurityCodeChange:function(){return d},redirectUrlFailure:function(){return G},redirectUrlSuccess:function(){return j},resetValidationErrors:function(){return b},setConfig:function(){return u},setCreditCardInfo:function(){return w},setInteractBankInfoFailure:function(){return W},setIsLoading:function(){return K},setPassKey:function(){return k},setPaymentItems:function(){return o},setValidationErrors:function(){return f},submitMultiOrderPaymentAction:function(){return M},submitMultiOrderPaymentActionCompleted:function(){return L},submitMultiOrderPaymentActionFailed:function(){return D},submitOrderPaymentAction:function(){return A},submitOrderPaymentActionCompleted:function(){return C},submitOrderPaymentActionFailed:function(){return h},tokenizeAndPropagateFormValues:function(){return P},validateMultiOrderPaymentAction:function(){return x},validateMultiOrderPaymentActionCompleted:function(){return S},validateMultiOrderPaymentActionFailed:function(){return O},validateOrderPaymentAction:function(){return g},validateOrderPaymentActionCompleted:function(){return v},validateOrderPaymentActionFailed:function(){return N}}),r=n(6),a=n(9),i=(0,r.createAction)("FETCH_PREAUTHORIZED_PAYMENT"),o=(0,r.createAction)("SET_PREAUTHORIZED_PAYMENT"),l=(0,r.createAction)("FETCH_PREAUTHORIZED_PAYMENT_FAILED"),u=(0,r.createAction)("SET_CONFIG"),s=(0,r.createAction)("GET_CONFIG"),c=(0,r.createAction)(a.CreditCardDetailsAction.ONCHANGE_CREDITCARD_NUMBER),m=(0,r.createAction)(a.CreditCardDetailsAction.ONCHANGE_CARDHOLDER_NAME),d=(0,r.createAction)(a.CreditCardDetailsAction.ONCHANGE_SECURITY_CODE),p=(0,r.createAction)(a.CreditCardDetailsAction.ONCHANGE_EXPIRY_DATE),f=(0,r.createAction)(a.CreditCardDetailsAction.SET_CREDIT_CARD_VALIDATION),b=(0,r.createAction)(a.CreditCardDetailsAction.RESET_CREDIT_CARD_VALIDATION),E=(0,r.createAction)("CREATE_PAYMENT"),_=(0,r.createAction)("CREATE_PAYMENT_COMPLETED"),y=(0,r.createAction)("CREATE_PAYMENT_FAILED"),g=(0,r.createAction)("VALIDATE_ORDER_PAYMENT"),v=(0,r.createAction)("VALIDATE_ORDER_PAYMENT_COMPLETED"),N=(0,r.createAction)("VALIDATE_ORDER_PAYMENT_FAILED"),A=(0,r.createAction)("SUBMIT_ORDER_PAYMENT"),C=(0,r.createAction)("SUBMIT_ORDER_PAYMENT_COMPLETED"),h=(0,r.createAction)("SUBMIT_ORDER_PAYMENT_FAILED"),T=(0,r.createAction)("CREATE_MULTI_PAYMENT"),I=(0,r.createAction)("CREATE_MULTI_PAYMENT_COMPLETED"),R=(0,r.createAction)("CREATE_MULTI_PAYMENT_FAILED"),x=(0,r.createAction)("VALIDATE_MULTI_ORDER_PAYMENT"),S=(0,r.createAction)("VALIDATE_MULTI_ORDER_PAYMENT_COMPLETED"),O=(0,r.createAction)("VALIDATE_MULTI_ORDER_PAYMENT_FAILED"),M=(0,r.createAction)("SUBMIT_MULTI_ORDER_PAYMENT"),L=(0,r.createAction)("SUBMIT_MULTI_ORDER_PAYMENT_COMPLETED"),D=(0,r.createAction)("SUBMIT_MULTI_ORDER_PAYMENT_FAILED"),P=(0,r.createAction)("TOKENIZE_AND_PROPAGE_FORM_VALUES"),B=(0,r.createAction)("GET_PASSKEY"),k=(0,r.createAction)("SET_PASSKEY"),w=(0,r.createAction)("SET_STATE",function(e){return e.cardNumberToken&&e.cardNumberToken.length>0?e.cardNumberToken.length>16?e.maskdCardNumber=e.creditCardNumber?e.creditCardNumber.replace(/\d(?=\d{4})/g,"*"):"":e.maskdCardNumber=e.cardNumberToken.replace(/\d(?=\d{4})/g,"*"):e.maskdCardNumber="",e.expiration&&e.expiration.indexOf("/")>-1&&(e.expirationMonth=e.expiration.split("/")[0],e.expirationYear=e.expiration.split("/")[1]),e}),U=(0,r.createAction)("CLEAR_CARD_NUMBER"),F=(0,r.createAction)("TOKENIZATION_ERROR"),H=(0,r.createAction)("TOKENIZATION_SUCCESS"),Y=(0,r.createAction)("GET_REDIRECT_URL"),j=(0,r.createAction)("GET_REDIRECT_URL_SUCCESS"),G=(0,r.createAction)("GET_REDIRECT_URL_FAILED"),V=(0,r.createAction)("GET_INTERAC_BANK_INFO"),z=(0,r.createAction)("GET_INTERAC_BANK_INFO_SUCCESS"),q=(0,r.createAction)("GET_INTERAC_BANK_INFO_FAILED"),K=(0,r.createAction)("SET_IS_LOADING"),W=(0,r.createAction)("RESET_FAILED_INTERACT_BANK_INFO"),X=(0,r.createAction)("OMNITURE_ON_LOAD"),Q=(0,r.createAction)("OMNITURE_ON_PAYMENT_SELECT"),Z=(0,r.createAction)("OMNITURE_ON_FIND_TRANSACTION_LIGHT_BOX"),$=(0,r.createAction)("OMNITURE_ON_BOX_NAME_LIGHT_BOX"),J=(0,r.createAction)("OMNITURE_ON_SECURITY_CODE_LIGHT_BOX"),ee=(0,r.createAction)("OMNITURE_ON_REVIEW"),te=(0,r.createAction)("OMNITURE_ON_CONFIRMATION"),ne=(0,r.createAction)("OMNITURE_ON_CONFIRMATION_FAILURE"),re=(0,r.createAction)("OMNITURE_ON_CANCELATION_COMPLETED"),ae=(0,r.createAction)("OMNITURE_ON_CANCELATION_PARTIALLY_COMPLETED"),ie=(0,r.createAction)("OMNITURE_ON_CANCELATION_FAILED"),oe=(0,r.createAction)("OMNITURE_ON_CANCELATION_PARTIALLY_FAILED"),le=(0,r.createAction)("OMNITURE_ON_VALIDATION_FAILURE"),ue=(0,r.createAction)("OMNITURE_ON_INTERAC_FAILURE"),se=(0,r.createAction)("API_CONFIRMATION_STATUS"),ce=(0,r.createAction)("CANCEL_PREAUTH"),me=(0,r.createAction)("CANCEL_PREAUTH_SUCCESS"),de=(0,r.createAction)("CANCEL_PREAUTH_FAILED")},function(e,t,n){"use strict";var r,a,i,o,l,u,s,c,m,d,p,f,b,E;n.r(t),n.d(t,{AccountInputDefaultValue:function(){return E},BankAccountDetail:function(){return m},CCDetails:function(){return o},CCDetailsDefault:function(){return l},CreditCardDetails:function(){return a},CreditCardDetailsAction:function(){return u},CreditCardInputValue:function(){return s},CreditCardType:function(){return r.CreditCardType},CreditCardValidationServiceEnum:function(){return r.CreditCardValidationServiceEnum},CreditCardValidationStatus:function(){return r.CreditCardValidationStatus},CurrentSection:function(){return r.CurrentSection},FieldType:function(){return r.FieldType},FormSubmit:function(){return b},InputBankAccountDetail:function(){return c},MakeOneTimePaymentStatus:function(){return r.MakeOneTimePaymentStatus},OfferImpactType:function(){return r.OfferImpactType},PageTitleCurrentSection:function(){return r.PageTitleCurrentSection},PaymentItemAccountType:function(){return r.PaymentItemAccountType},PaymentItemBillStatus:function(){return r.PaymentItemBillStatus},PaymentMethod:function(){return r.PaymentMethod},PaymentMethodCreditOption:function(){return r.PaymentMethodCreditOption},PaymentMethodDebitOption:function(){return r.PaymentMethodDebitOption},PreAuthorizedPaymentMethod:function(){return r.PreAuthorizedPaymentMethod},RegisterPACCStatus:function(){return r.RegisterPACCStatus},RegisterPADStatus:function(){return r.RegisterPADStatus},UnEnrollStatus:function(){return r.UnEnrollStatus},defaultBankInputValue:function(){return d},defaultCreditCardDetails:function(){return i},defaultCreditCardInputValue:function(){return p},defaultPreAuthorizedPayment:function(){return f}}),r=n(10),a=function(){},i={ID:"",Token:"",CreditCardNumber:"",CreditCardNumberMasked:"",CreditCardNumberMaskedDisplayView:"",CreditCardNumberMaskedDisplayViewB:"",CreditCardNumberDTSValidated:!1,CreditCardType:r.CreditCardType.VI,CardholderName:"",ExpireYear:"",ExpireMonth:"",SecurityCodeMasked:"",SecurityCode:"",ShowAgreement:!1,ShowPreAuthorizedInfo:!1,IsSaveCreditCard:!1,IsPrepaid:!1,Months:[],Years:[],ExpirationDateDisplayViewA:"",BackEndModelState:null,ValidationServiceSource:null,ValidationStatus:null,IsDTSOutage:!1,IsDTSValidated:!1,IsValid:!1,IsTokenizationValid:!1,HasSpecialCharacters:!1,HasExtraSpaces:!1,ValidatedCVV:null,ValidatedExpireYear:null,ValidatedExpireMonth:null,ValidatedToken:null,AuthorizationCode:null,AccountNumber:null},o=function(){},l={CreditCardNumber:"",CreditCardNumberMasked:"",CardholderName:"",ExpireYear:"",ExpireMonth:"",SecurityCode:""},function(e){e.ONCHANGE_CREDITCARD_NUMBER="ONCHANGE_CREDITCARD_NUMBER",e.ONCHANGE_CARDHOLDER_NAME="ONCHANGE_CARDHOLDER_NAME",e.ONCHANGE_EXPIRY_MONTH="ONCHANGE_EXPIRY_MONTH",e.ONCHANGE_EXPIRY_YEAR="ONCHANGE_EXPIRY_YEAR",e.ONCHANGE_EXPIRY_DATE="ONCHANGE_EXPIRY_DATE",e.ONCHANGE_SECURITY_CODE="ONCHANGE_SECURITY_CODE",e.SET_CREDIT_CARD_DEFAULT="SET_CREDIT_CARD_DEFAULT",e.SET_CREDIT_CARD_VALIDATION="SET_CREDIT_CARD_VALIDATION",e.RESET_CREDIT_CARD_VALIDATION="RESET_CREDIT_CARD_VALIDATION"}(u||(u={})),s=function(){},c=function(){},m=function(){},d={PaymentMethod:"",AccountHolder:"",BankName:"",TransitNumber:"",AccountNumber:""},p={cardNumber:"",cardType:"",cardName:"",expiryDate:""},f={items:[],selectedItems:[],failedItems:[],successfullItems:[],currentItem:{},selectedBanID:"",selectedCreditCard:{},bankAccountInfo:{},transactionID:"",completedTransactionID:"",padPaccTransactionID:"",customerEmail:"<EMAIL>",contactUs:"",preAuthPaymentMethods:[],isAllBillsPreAuthorized:!1,isSignUpPreAuthorized:!1,isSignUpPreAuthorizedCompleted:!1,hasSuccessPADPACCSignUp:!1,hasFailedPADPACCSignUp:!1,enableValidation:!1,enableTransactionSummary:!1,isCreditCardAgreementSelected:!1,isBankAgreementSelected:!1,selectedPaymentMethod:void 0,selectedUpdatePaymentMethod:r.PreAuthorizedPaymentMethod.Debit,selectedPaymentMethodCreditOption:void 0,selectedPaymentMethodDebitOption:void 0,bankAccountCacheKey:"",creditCardCacheKey:"",showNoNameOnCard:!1,acceptPrepaidCardNumber:!1,showPACCAgreement:!1,errorCode_CreditCard:[],errorCode_Bank:[],uniqueCreditCards:[],uniqueBankAccounts:[],reUsePadPaccFromBan:"",message:"",isFail:!1,flowInitUrl:""},b=function(){},E={accountNumber:"",subNumber:"",transactionID:"",payBalanceAmnt:0}},function(e,t,n){"use strict";var r,a,i,o,l,u,s,c,m,d,p,f,b,E,_,y,g;n.r(t),n.d(t,{CreditCardType:function(){return p},CreditCardValidationServiceEnum:function(){return f},CreditCardValidationStatus:function(){return b},CurrentSection:function(){return _},FieldType:function(){return E},MakeOneTimePaymentStatus:function(){return c},OfferImpactType:function(){return g},PageTitleCurrentSection:function(){return y},PaymentItemAccountType:function(){return d},PaymentItemBillStatus:function(){return m},PaymentMethod:function(){return o},PaymentMethodCreditOption:function(){return a},PaymentMethodDebitOption:function(){return i},PreAuthorizedPaymentMethod:function(){return r},RegisterPACCStatus:function(){return s},RegisterPADStatus:function(){return u},UnEnrollStatus:function(){return l}}),function(e){e[e.Creditcard=0]="Creditcard",e[e.Debit=1]="Debit",e[e.ExistingCreditcard=2]="ExistingCreditcard"}(r||(r={})),function(e){e[e.ChangeCreditCardInfo=0]="ChangeCreditCardInfo",e[e.SwitchToBankAccount=1]="SwitchToBankAccount",e[e.UnEnroll=2]="UnEnroll"}(a||(a={})),function(e){e[e.ChangeBankAccountInfo=0]="ChangeBankAccountInfo",e[e.SwitchToCreditCard=1]="SwitchToCreditCard",e[e.UnEnroll=2]="UnEnroll"}(i||(i={})),function(e){e[e.Regular=0]="Regular",e[e.CreditCard=1]="CreditCard",e[e.PreAuthBank=2]="PreAuthBank",e[e.PreAuthCreditCard=3]="PreAuthCreditCard",e[e.Invoice=4]="Invoice",e[e.ECoupon=5]="ECoupon",e[e.Certificate=6]="Certificate"}(o||(o={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(l||(l={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(u||(u={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(s||(s={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(c||(c={})),function(e){e[e.Unknown=0]="Unknown",e[e.Active=1]="Active",e[e.Suspended=2]="Suspended",e[e.Cancelled=3]="Cancelled"}(m||(m={})),function(e){e[e.OneBill=0]="OneBill",e[e.Mobility=1]="Mobility",e[e.TV=2]="TV",e[e.Internet=3]="Internet",e[e.HomePhone=4]="HomePhone",e[e.MobilityAndOneBill=5]="MobilityAndOneBill",e[e.SingleBan=6]="SingleBan"}(d||(d={})),function(e){e[e.DC=0]="DC",e[e.VI=1]="VI",e[e.MC=2]="MC",e[e.AX=3]="AX"}(p||(p={})),function(e){e[e.OneBill=0]="OneBill",e[e.Mobility=1]="Mobility",e[e.TV=2]="TV"}(f||(f={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(b||(b={})),function(e){e.CardNumber="CREDIT_CARD_NUMBER",e.CardHolderName="CREDIT_CARD_HOLDER_NAME",e.ExpirationDate="CREDIT_CARD_EXPIRE_DATE",e.SecurityCode="CREDIT_CARD_SECURITY_CODE",e.BankName="BANK_NAME",e.BankAccountHolderName="BANK_ACCOUNT_HOLDER_NAME",e.BankTransitCode="BANK_TRANSIT_CODE",e.BankAccountNumber="BANK_ACCOUNT_NUM",e.ServerValidation="SERVER_VALIDATION"}(E||(E={})),function(e){e[e.Default=0]="Default",e[e.SelectBills=1]="SelectBills",e[e.PaymentMethod=2]="PaymentMethod",e[e.CurrentBalance=3]="CurrentBalance",e[e.TermsAndCondition=4]="TermsAndCondition",e[e.Confirmation=5]="Confirmation"}(_||(_={})),function(e){e[e.Default=0]="Default",e.SelectBills="SelectBills",e.PaymentMethod="PaymentMethod",e.CurrentBalance="CurrentBalance",e.TermsAndCondition="TermsAndCondition",e.Confirmation="Confirmation"}(y||(y={})),function(e){e.REDUCE="REDUCE",e.GAIN="GAIN",e.REMOVE="REMOVE",e.INCREASE="INCREASE"}(g||(g={}))},function(e){"use strict";e.exports=u},function(e){"use strict";e.exports=s},function(e){"use strict";e.exports=c},function(e,t,n){var r;r=(e,t)=>(()=>{"use strict";function n(e){var t,r=ao[e];return void 0!==r?r.exports:(t=ao[e]={exports:{}},ro[e](t,t.exports,n),t.exports)}function r(e){e.length=0}function a(e,t,n){return Array.prototype.slice.call(e,t,n)}function i(e){return e.bind.apply(e,[null].concat(a(arguments,1)))}function o(e){return requestAnimationFrame(e)}function l(e,t){return typeof t===e}function u(e){return!s(e)&&l("object",e)}function s(e){return null===e}function c(e){try{return e instanceof(e.ownerDocument.defaultView||window).HTMLElement}catch(e){return!1}}function m(e){return ln(e)?e:[e]}function d(e,t){m(e).forEach(t)}function p(e,t){return e.indexOf(t)>-1}function f(e,t){return e.push.apply(e,m(t)),e}function b(e,t,n){e&&d(t,function(t){t&&e.classList[n?"add":"remove"](t)})}function E(e,t){b(e,sn(t)?t.split(" "):t,!0)}function _(e,t){d(t,e.appendChild.bind(e))}function y(e,t){d(e,function(e){var n=(t||e).parentNode;n&&n.insertBefore(e,t)})}function g(e,t){return c(e)&&(e.msMatchesSelector||e.matches).call(e,t)}function v(e,t){var n=e?a(e.children):[];return t?n.filter(function(e){return g(e,t)}):n}function N(e,t){return t?v(e,t)[0]:e.firstElementChild}function A(e,t,n){return e&&(n?mn(e).reverse():mn(e)).forEach(function(n){"__proto__"!==n&&t(e[n],n)}),e}function C(e){return a(arguments,1).forEach(function(t){A(t,function(n,r){e[r]=t[r]})}),e}function h(e){return a(arguments,1).forEach(function(t){A(t,function(t,n){ln(t)?e[n]=t.slice():u(t)?e[n]=h({},u(e[n])?e[n]:{},t):e[n]=t})}),e}function T(e,t){d(t||mn(e),function(t){delete e[t]})}function I(e,t){d(e,function(e){d(t,function(t){e&&e.removeAttribute(t)})})}function R(e,t,n){u(t)?A(t,function(t,n){R(e,n,t)}):d(e,function(e){s(n)||""===n?I(e,t):e.setAttribute(t,String(n))})}function x(e,t,n){var r=document.createElement(e);return t&&(sn(t)?E(r,t):R(r,t)),n&&_(n,r),r}function S(e,t,n){if(cn(n))return getComputedStyle(e)[t];s(n)||(e.style[t]=""+n)}function O(e,t){S(e,"display",t)}function M(e){e.setActive&&e.setActive()||e.focus({preventScroll:!0})}function L(e,t){return e.getAttribute(t)}function D(e,t){return e&&e.classList.contains(t)}function P(e){return e.getBoundingClientRect()}function B(e){d(e,function(e){e&&e.parentNode&&e.parentNode.removeChild(e)})}function k(e){return N((new DOMParser).parseFromString(e,"text/html").body)}function w(e,t){e.preventDefault(),t&&(e.stopPropagation(),e.stopImmediatePropagation())}function U(e,t){return e&&e.querySelector(t)}function F(e,t){return t?a(e.querySelectorAll(t)):[]}function H(e,t){b(e,t,!1)}function Y(e){return e.timeStamp}function j(e){return sn(e)?e:e?e+"px":""}function G(e,t){if(!e)throw new Error("["+dn+"] "+(t||""))}function V(e,t,n){return yn(e-t)<n}function z(e,t,n,r){var a=fn(t,n),i=bn(t,n);return r?a<e&&e<i:a<=e&&e<=i}function q(e,t,n){var r=fn(t,n),a=bn(t,n);return fn(bn(r,e),a)}function K(e){return+(e>0)-+(e<0)}function W(e,t){return d(t,function(t){e=e.replace("%s",""+t)}),e}function X(e){return e<10?"0"+e:""+e}function Q(){function e(e,t,n){d(e,function(e){e&&d(t,function(t){t.split(" ").forEach(function(t){var r=t.split(".");n(e,r[0],r[1])})})})}var t=[];return{bind:function(n,r,a,i){e(n,r,function(e,n,r){var o="addEventListener"in e,l=o?e.removeEventListener.bind(e,n,a,i):e.removeListener.bind(e,a);o?e.addEventListener(n,a,i):e.addListener(a),t.push([e,n,r,a,l])})},unbind:function(n,r,a){e(n,r,function(e,n,r){t=t.filter(function(t){return!!(t[0]!==e||t[1]!==n||t[2]!==r||a&&t[3]!==a)||(t[4](),!1)})})},dispatch:function(e,t,n){var r,a=!0;return"function"==typeof CustomEvent?r=new CustomEvent(t,{bubbles:a,detail:n}):(r=document.createEvent("CustomEvent")).initCustomEvent(t,a,!1,n),e.dispatchEvent(r),r},destroy:function(){t.forEach(function(e){e[4]()}),r(t)}}}function Z(e){var t=e?e.event.bus:document.createDocumentFragment(),n=Q();return e&&e.event.on(Un,n.destroy),C(n,{bus:t,on:function(e,r){n.bind(t,m(e).join(" "),function(e){r.apply(r,ln(e.detail)?e.detail:[])})},off:i(n.unbind,t),emit:function(e){n.dispatch(t,e,a(arguments,1))}})}function $(e,t,n,r){function a(){if(!d){if(m=e?fn((c()-u)/e,1):1,n&&n(m),m>=1&&(t(),u=c(),r&&++p>=r))return i();s=o(a)}}function i(){d=!0}function l(){s&&cancelAnimationFrame(s),m=0,s=0,d=!0}var u,s,c=Date.now,m=0,d=!0,p=0;return{start:function(t){t||l(),u=c()-(t?m*e:0),d=!1,s=o(a)},rewind:function(){u=c(),m=0,n&&n(m)},pause:i,cancel:l,set:function(t){e=t},isPaused:function(){return d}}}function J(e){return e=sn(e)?e:e.key,$r[e]||e}function ee(e,t,n){function r(){a.forEach(function(e){e.style("transform","translateX(-"+100*e.index+"%)")})}var a=t.Slides;return{mount:function(){Z(e).on([vn,Sn],r)},start:function(e,t){a.style("transition","opacity "+n.speed+"ms "+n.easing),an(t)},cancel:on}}function te(e,t,n){function r(){c(""),u.cancel()}var a,o=t.Move,l=t.Controller,u=t.Scroll,s=t.Elements.list,c=i(S,s,"transition");return{mount:function(){Z(e).bind(s,"transitionend",function(e){e.target===s&&a&&(r(),a())})},start:function(t,r){var i=o.toPosition(t,!0),s=o.getPosition(),m=function(t){var r,a,i=n.rewindSpeed;return e.is(Kr)&&i&&(r=l.getIndex(!0),a=l.getEnd(),0===r&&t>=a||r>=a&&0===t)?i:n.speed}(t);yn(i-s)>=1&&m>=1?n.useScroll?u.scroll(i,m,!1,r):(c("transform "+m+"ms "+n.easing),o.translate(i,!0),a=r):(o.jump(t),r())},cancel:r}}function ne(...e){return e.filter(Boolean).join(" ")}function re(e){return null!==e&&"object"==typeof e}function ae(e,t){if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&!e.some((e,n)=>!ae(e,t[n]));if(re(e)&&re(t)){const n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&!n.some(n=>!Object.prototype.hasOwnProperty.call(t,n)||!ae(e[n],t[n]))}return e===t}function ie(e,t){const n=e;return function(e,t){if(e){const n=Object.keys(e);for(let r=0;r<n.length;r++){const a=n[r];if("__proto__"!==a&&!1===t(e[a],a))break}}}(t,(e,t)=>{Array.isArray(e)?n[t]=e.slice():re(e)?n[t]=ie(re(n[t])?n[t]:{},e):n[t]=e}),n}function oe(){return oe=Object.assign?Object.assign.bind():function(e){var t,n,r;for(t=1;t<arguments.length;t++)for(r in n=arguments[t])({}).hasOwnProperty.call(n,r)&&(e[r]=n[r]);return e},oe.apply(null,arguments)}function le(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}function ue(e){return e}function se(e,t){void 0===t&&(t=ue);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var a=t(e,r);return n.push(a),function(){n=n.filter(function(e){return e!==a})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){var t,a,i,o;r=!0,t=[],n.length&&(a=n,n=[],a.forEach(e),t=n),i=function(){var n=t;t=[],n.forEach(e)},(o=function(){return Promise.resolve().then(i)})(),n={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),n}}}}}function ce(e,t){return void 0===t&&(t=ue),se(e,t)}function me(e,t){return me=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},me(e,t)}function de(e){return de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},de(e)}function pe(e){if(!e)return null;if("undefined"==typeof WeakRef)return function(){return e||null};var t=e?new WeakRef(e):null;return function(){return(null==t?void 0:t.deref())||null}}function fe(e){setTimeout(e,1)}function be(e,t,n,r){var a,i=null,o=e;do{if((a=r[o]).guard)a.node.dataset.focusAutoGuard&&(i=a);else{if(!a.lockItem)break;if(o!==e)return;i=null}}while((o+=n)!==t);i&&(i.node.tabIndex=0)}function Ee(){return"undefined"!=typeof window}function _e(e){return ve(e)?(e.nodeName||"").toLowerCase():"#document"}function ye(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ge(e){var t;return null==(t=(ve(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ve(e){return!!Ee()&&(e instanceof Node||e instanceof ye(e).Node)}function Ne(e){return!!Ee()&&(e instanceof Element||e instanceof ye(e).Element)}function Ae(e){return!!Ee()&&(e instanceof HTMLElement||e instanceof ye(e).HTMLElement)}function Ce(e){return!(!Ee()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof ye(e).ShadowRoot)}function he(e){const{overflow:t,overflowX:n,overflowY:r,display:a}=Oe(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!au.has(a)}function Te(e){return iu.has(_e(e))}function Ie(e){return ou.some(t=>{try{return e.matches(t)}catch(e){return!1}})}function Re(e){const t=xe(),n=Ne(e)?Oe(e):e;return lu.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||uu.some(e=>(n.willChange||"").includes(e))||su.some(e=>(n.contain||"").includes(e))}function xe(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function Se(e){return cu.has(_e(e))}function Oe(e){return ye(e).getComputedStyle(e)}function Me(e){return Ne(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Le(e){if("html"===_e(e))return e;const t=e.assignedSlot||e.parentNode||Ce(e)&&e.host||ge(e);return Ce(t)?t.host:t}function De(e){const t=Le(e);return Se(t)?e.ownerDocument?e.ownerDocument.body:e.body:Ae(t)&&he(t)?t:De(t)}function Pe(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const a=De(e),i=a===(null==(r=e.ownerDocument)?void 0:r.body),o=ye(a);if(i){const e=Be(o);return t.concat(o,o.visualViewport||[],he(a)?a:[],e&&n?Pe(e):[])}return t.concat(a,Pe(a,[],n))}function Be(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ke(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;t=t.shadowRoot.activeElement}return t}function we(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&Ce(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function Ue(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(e=>{let{brand:t,version:n}=e;return t+"/"+n}).join(" "):navigator.userAgent}function Fe(){const e=/android/i;return e.test(function(){const e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}())||e.test(Ue())}function He(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function Ye(e){return(null==e?void 0:e.ownerDocument)||document}function je(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return null!=n.target&&t.contains(n.target)}function Ge(e){return"composedPath"in e?e.composedPath()[0]:e.target}function Ve(e){return Ae(e)&&e.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])")}function ze(e){e.preventDefault(),e.stopPropagation()}function qe(e,t,n){return du(e,mu(t,n))}function Ke(e,t){return"function"==typeof e?e(t):e}function We(e){return e.split("-")[0]}function Xe(e){return e.split("-")[1]}function Qe(e){return"x"===e?"y":"x"}function Ze(e){return"y"===e?"height":"width"}function $e(e){return yu.has(We(e))?"y":"x"}function Je(e){return Qe($e(e))}function et(e){return e.replace(/start|end/g,e=>_u[e])}function tt(e){return e.replace(/left|right|bottom|top/g,e=>Eu[e])}function nt(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function rt(e){const{x:t,y:n,width:r,height:a}=e;return{width:r,height:a,top:n,left:t,right:t+r,bottom:n+a,x:t,y:n}}function at(e,t,n){let{reference:r,floating:a}=e;const i=$e(t),o=Je(t),l=Ze(o),u=We(t),s="y"===i,c=r.x+r.width/2-a.width/2,m=r.y+r.height/2-a.height/2,d=r[l]/2-a[l]/2;let p;switch(u){case"top":p={x:c,y:r.y-a.height};break;case"bottom":p={x:c,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:m};break;case"left":p={x:r.x-a.width,y:m};break;default:p={x:r.x,y:r.y}}switch(Xe(t)){case"start":p[o]-=d*(n&&s?-1:1);break;case"end":p[o]+=d*(n&&s?-1:1)}return p}async function it(e,t){var n;void 0===t&&(t={});const{x:r,y:a,platform:i,rects:o,elements:l,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:m="floating",altBoundary:d=!1,padding:p=0}=Ke(t,e),f=nt(p),b=l[d?"floating"===m?"reference":"floating":m],E=rt(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(b)))||n?b:b.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:s,rootBoundary:c,strategy:u})),_="floating"===m?{x:r,y:a,width:o.floating.width,height:o.floating.height}:o.reference,y=await(null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),g=await(null==i.isElement?void 0:i.isElement(y))&&await(null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},v=rt(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:_,offsetParent:y,strategy:u}):_);return{top:(E.top-v.top+f.top)/g.y,bottom:(v.bottom-E.bottom+f.bottom)/g.y,left:(E.left-v.left+f.left)/g.x,right:(v.right-E.right+f.right)/g.x}}function ot(e){const t=Oe(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const a=Ae(e),i=a?e.offsetWidth:n,o=a?e.offsetHeight:r,l=pu(n)!==i||pu(r)!==o;return l&&(n=i,r=o),{width:n,height:r,$:l}}function lt(e){return Ne(e)?e:e.contextElement}function ut(e){const t=lt(e);if(!Ae(t))return bu(1);const n=t.getBoundingClientRect(),{width:r,height:a,$:i}=ot(t);let o=(i?pu(n.width):n.width)/r,l=(i?pu(n.height):n.height)/a;return o&&Number.isFinite(o)||(o=1),l&&Number.isFinite(l)||(l=1),{x:o,y:l}}function st(e){const t=ye(e);return xe()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:hu}function ct(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const a=e.getBoundingClientRect(),i=lt(e);let o=bu(1);t&&(r?Ne(r)&&(o=ut(r)):o=ut(e));const l=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==ye(e))&&t}(i,n,r)?st(i):bu(0);let u=(a.left+l.x)/o.x,s=(a.top+l.y)/o.y,c=a.width/o.x,m=a.height/o.y;if(i){const e=ye(i),t=r&&Ne(r)?ye(r):r;let n=e,a=Be(n);for(;a&&r&&t!==n;){const e=ut(a),t=a.getBoundingClientRect(),r=Oe(a),i=t.left+(a.clientLeft+parseFloat(r.paddingLeft))*e.x,o=t.top+(a.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,s*=e.y,c*=e.x,m*=e.y,u+=i,s+=o,n=ye(a),a=Be(n)}}return rt({width:c,height:m,x:u,y:s})}function mt(e,t){const n=Me(e).scrollLeft;return t?t.left+n:ct(ge(e)).left+n}function dt(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:mt(e,r)),y:r.top+t.scrollTop}}function pt(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=ye(e),r=ge(e),a=n.visualViewport;let i=r.clientWidth,o=r.clientHeight,l=0,u=0;if(a){i=a.width,o=a.height;const e=xe();(!e||e&&"fixed"===t)&&(l=a.offsetLeft,u=a.offsetTop)}return{width:i,height:o,x:l,y:u}}(e,n);else if("document"===t)r=function(e){const t=ge(e),n=Me(e),r=e.ownerDocument.body,a=du(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=du(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let o=-n.scrollLeft+mt(e);const l=-n.scrollTop;return"rtl"===Oe(r).direction&&(o+=du(t.clientWidth,r.clientWidth)-a),{width:a,height:i,x:o,y:l}}(ge(e));else if(Ne(t))r=function(e,t){const n=ct(e,!0,"fixed"===t),r=n.top+e.clientTop,a=n.left+e.clientLeft,i=Ae(e)?ut(e):bu(1);return{width:e.clientWidth*i.x,height:e.clientHeight*i.y,x:a*i.x,y:r*i.y}}(t,n);else{const n=st(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return rt(r)}function ft(e,t){const n=Le(e);return!(n===t||!Ne(n)||Se(n))&&("fixed"===Oe(n).position||ft(n,t))}function bt(e,t,n){function r(){s.x=mt(i)}const a=Ae(t),i=ge(t),o="fixed"===n,l=ct(e,!0,o,t);let u={scrollLeft:0,scrollTop:0};const s=bu(0);if(a||!a&&!o)if(("body"!==_e(t)||he(i))&&(u=Me(t)),a){const e=ct(t,!0,o,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else i&&r();o&&!a&&i&&r();const c=!i||a||o?bu(0):dt(i,u);return{x:l.left+u.scrollLeft-s.x-c.x,y:l.top+u.scrollTop-s.y-c.y,width:l.width,height:l.height}}function Et(e){return"static"===Oe(e).position}function _t(e,t){if(!Ae(e)||"fixed"===Oe(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ge(e)===n&&(n=n.ownerDocument.body),n}function yt(e,t){const n=ye(e);if(Ie(e))return n;if(!Ae(e)){let t=Le(e);for(;t&&!Se(t);){if(Ne(t)&&!Et(t))return t;t=Le(t)}return n}let r=_t(e,t);for(;r&&Te(r)&&Et(r);)r=_t(r,t);return r&&Se(r)&&Et(r)&&!Re(r)?n:r||function(e){let t=Le(e);for(;Ae(t)&&!Se(t);){if(Re(t))return t;if(Ie(t))return null;t=Le(t)}return null}(e)||n}function gt(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function vt(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:a=!0,ancestorResize:i=!0,elementResize:o="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,s=lt(e),c=a||i?[...s?Pe(s):[],...Pe(t)]:[];c.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)});const m=s&&l?function(e,t){function n(){var e;clearTimeout(r),null==(e=a)||e.disconnect(),a=null}let r,a=null;const i=ge(e);return function o(l,u){function s(t){const n=t[0].intersectionRatio;if(n!==u){if(!E)return o();n?o(!1,n):r=setTimeout(()=>{o(!1,1e-7)},1e3)}1!==n||gt(c,e.getBoundingClientRect())||o(),E=!1}void 0===l&&(l=!1),void 0===u&&(u=1),n();const c=e.getBoundingClientRect(),{left:m,top:d,width:p,height:f}=c;if(l||t(),!p||!f)return;const b={rootMargin:-fu(d)+"px "+-fu(i.clientWidth-(m+p))+"px "+-fu(i.clientHeight-(d+f))+"px "+-fu(m)+"px",threshold:du(0,mu(1,u))||1};let E=!0;try{a=new IntersectionObserver(s,{...b,root:i.ownerDocument})}catch(e){a=new IntersectionObserver(s,b)}a.observe(e)}(!0),n}(s,n):null;let d,p=-1,f=null;o&&(f=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&f&&(f.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=f)||e.observe(t)})),n()}),s&&!u&&f.observe(s),f.observe(t));let b=u?ct(e):null;return u&&function t(){const r=ct(e);b&&!gt(b,r)&&n(),b=r,d=requestAnimationFrame(t)}(),n(),()=>{var e;c.forEach(e=>{a&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=f)||e.disconnect(),f=null,u&&cancelAnimationFrame(d)}}function Nt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,a;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!Nt(e[r],t[r]))return!1;return!0}if(a=Object.keys(e),n=a.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,a[r]))return!1;for(r=n;0!==r--;){const n=a[r];if(!("_owner"===n&&e.$$typeof||Nt(e[n],t[n])))return!1}return!0}return e!=e&&t!=t}function At(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Ct(e,t){const n=At(e);return Math.round(t*n)/n}function ht(e){const t=Wt.useRef(e);return to(()=>{t.current=e}),t}function Tt(e){return Wt.useMemo(()=>e.every(e=>null==e)?null:t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})},e)}function It(e){const t=Wt.useRef(()=>{});return ku(()=>{t.current=e}),Wt.useCallback(function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function Rt(){return Rt=Object.assign?Object.assign.bind():function(e){var t,n,r;for(t=1;t<arguments.length;t++)for(r in n=arguments[t])Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r]);return e},Rt.apply(this,arguments)}function xt(e){return"data-floating-ui-"+e}function St(e){const t=(0,Wt.useRef)(e);return no(()=>{t.current=e}),t}function Ot(e,t,n){return n&&!He(n)?0:"number"==typeof e?e:null==e?void 0:e[t]}function Mt(e,t){void 0===t&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:a=!1}=t;r&&cancelAnimationFrame(Ku);const i=()=>null==e?void 0:e.focus({preventScroll:n});a?i():Ku=requestAnimationFrame(i)}function Lt(e,t){let n=e.filter(e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)}),r=n;for(;r.length;)r=e.filter(e=>{var t;return null==(t=r)?void 0:t.some(t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)})}),n=n.concat(r);return n}function Dt(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);const r=Ye(e[0]).body;return function(e,t,n,r){const a="data-floating-ui-inert",i=r?"inert":n?"aria-hidden":null,o=(l=t,e.map(e=>{if(l.contains(e))return e;const t=$u(e);return l.contains(t)?t:null}).filter(e=>null!=e));var l;const u=new Set,s=new Set(o),c=[];Qu[a]||(Qu[a]=new WeakMap);const m=Qu[a];return o.forEach(function e(t){t&&!u.has(t)&&(u.add(t),t.parentNode&&e(t.parentNode))}),function e(t){t&&!s.has(t)&&[].forEach.call(t.children,t=>{if("script"!==_e(t))if(u.has(t))e(t);else{const e=i?t.getAttribute(i):null,n=null!==e&&"false"!==e,r=(Wu.get(t)||0)+1,o=(m.get(t)||0)+1;Wu.set(t,r),m.set(t,o),c.push(t),1===r&&n&&Xu.add(t),1===o&&t.setAttribute(a,""),!n&&i&&t.setAttribute(i,"true")}})}(t),u.clear(),Zu++,()=>{c.forEach(e=>{const t=(Wu.get(e)||0)-1,n=(m.get(e)||0)-1;Wu.set(e,t),m.set(e,n),t||(!Xu.has(e)&&i&&e.removeAttribute(i),Xu.delete(e)),n||e.removeAttribute(a)}),Zu--,Zu||(Wu=new WeakMap,Wu=new WeakMap,Xu=new WeakSet,Qu={})}}(e.concat(Array.from(r.querySelectorAll("[aria-live]"))),r,t,n)}function Pt(e,t){const n=eo(e,Ju());"prev"===t&&n.reverse();const r=n.indexOf(ke(Ye(e)));return n.slice(r+1)[0]}function Bt(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!we(n,r)}function kt(e){"Tab"===e.key&&(e.target,clearTimeout(void 0))}function wt(e){as=as.filter(e=>e.isConnected);let t=e;if(t&&"body"!==_e(t)){if(!function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==Yi.call(e,Fi)&&Zi(t,e)}(t,Ju())){const e=eo(t,Ju())[0];e&&(t=e)}as.push(t),as.length>20&&(as=as.slice(-20))}}function Ut(){return as.slice().reverse().find(e=>e.isConnected)}function Ft(e){function t(e){return!o&&p&&d?Wt.createElement(is,{ref:"start"===e?L:D,onClick:e=>y(!1,e.nativeEvent)},"string"==typeof p?p:"Dismiss"):null}var n,r;const{context:a,children:i,disabled:o=!1,order:l=["content"],guards:u=!0,initialFocus:s=0,returnFocus:c=!0,restoreFocus:m=!1,modal:d=!0,visuallyHiddenDismiss:p=!1,closeOnFocusOut:f=!0}=e,{open:b,refs:E,nodeId:_,onOpenChange:y,events:g,dataRef:v,floatingId:N,elements:{domReference:A,floating:C}}=a,h="number"==typeof s&&s<0,T=!!(n=A)&&"combobox"===n.getAttribute("role")&&Ve(n)&&h,I="undefined"==typeof HTMLElement||!("inert"in HTMLElement.prototype)||u,R=St(l),x=St(s),S=St(c),O=zu(),M=Wt.useContext(ns),L=Wt.useRef(null),D=Wt.useRef(null),P=Wt.useRef(!1),B=Wt.useRef(!1),k=Wt.useRef(-1),w=null!=M,U=(r=C)?r.hasAttribute(rs)?r:r.querySelector("["+rs+"]")||r:null,F=It(function(e){return void 0===e&&(e=U),e?eo(e,Ju()):[]}),H=It(e=>{const t=F(e);return R.current.map(e=>A&&"reference"===e?A:U&&"floating"===e?U:t).filter(Boolean).flat()});Wt.useEffect(()=>{function e(e){if("Tab"===e.key){we(U,ke(Ye(U)))&&0===F().length&&!T&&ze(e);const t=H(),n=Ge(e);"reference"===R.current[0]&&n===A&&(ze(e),e.shiftKey?Mt(t[t.length-1]):Mt(t[1])),"floating"===R.current[1]&&n===U&&e.shiftKey&&(ze(e),Mt(t[0]))}}if(o)return;if(!d)return;const t=Ye(U);return t.addEventListener("keydown",e),()=>{t.removeEventListener("keydown",e)}},[o,A,U,d,R,T,F,H]),Wt.useEffect(()=>{function e(e){const t=Ge(e),n=F().indexOf(t);-1!==n&&(k.current=n)}if(!o&&C)return C.addEventListener("focusin",e),()=>{C.removeEventListener("focusin",e)}},[o,C,F]),Wt.useEffect(()=>{function e(){B.current=!0,setTimeout(()=>{B.current=!1})}function t(e){const t=e.relatedTarget;queueMicrotask(()=>{const n=!(we(A,t)||we(C,t)||we(t,C)||we(null==M?void 0:M.portalNode,t)||null!=t&&t.hasAttribute(xt("focus-guard"))||O&&(Lt(O.nodesRef.current,_).find(e=>{var n,r;return we(null==(n=e.context)?void 0:n.elements.floating,t)||we(null==(r=e.context)?void 0:r.elements.domReference,t)})||function(e,t){var n;let r=[],a=null==(n=e.find(e=>e.id===t))?void 0:n.parentId;for(;a;){const t=e.find(e=>e.id===a);a=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r}(O.nodesRef.current,_).find(e=>{var n,r;return(null==(n=e.context)?void 0:n.elements.floating)===t||(null==(r=e.context)?void 0:r.elements.domReference)===t})));if(m&&n&&ke(Ye(U))===Ye(U).body){Ae(U)&&U.focus();const e=k.current,t=F(),n=t[e]||t[t.length-1]||U;Ae(n)&&n.focus()}!T&&d||!t||!n||B.current||t===Ut()||(P.current=!0,y(!1,e,"focus-out"))})}if(!o&&f)return C&&Ae(A)?(A.addEventListener("focusout",t),A.addEventListener("pointerdown",e),C.addEventListener("focusout",t),()=>{A.removeEventListener("focusout",t),A.removeEventListener("pointerdown",e),C.removeEventListener("focusout",t)}):void 0},[o,A,C,U,d,_,O,M,y,f,m,F,T]),Wt.useEffect(()=>{var e;if(o)return;const t=Array.from((null==M||null==(e=M.portalNode)?void 0:e.querySelectorAll("["+xt("portal")+"]"))||[]);if(C){const e=[C,...t,L.current,D.current,R.current.includes("reference")||T?A:null].filter(e=>null!=e),n=d||T?Dt(e,I,!I):Dt(e);return()=>{n()}}},[o,A,C,d,R,M,T,I]),no(()=>{if(o||!Ae(U))return;const e=ke(Ye(U));queueMicrotask(()=>{const t=H(U),n=x.current,r=("number"==typeof n?t[n]:n.current)||U,a=we(U,e);h||a||!b||Mt(r,{preventScroll:r===U})})},[o,b,U,h,H,x]),no(()=>{function e(e){let{open:n,reason:r,event:i,nested:o}=e;n&&(a=i),"escape-key"===r&&E.domReference.current&&wt(E.domReference.current),"hover"===r&&"mouseleave"===i.type&&(P.current=!0),"outside-press"===r&&(o?(P.current=!1,t=!0):P.current=!(function(e){return!(0!==e.mozInputSource||!e.isTrusted)||(Fe()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}(i)||function(e){return!Ue().includes("jsdom/")&&(!Fe()&&0===e.width&&0===e.height||Fe()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)}(i)))}if(o||!U)return;let t=!1;const n=Ye(U),r=ke(n);let a=v.current.openEvent;wt(r),g.on("openchange",e);const i=n.createElement("span");return i.setAttribute("tabindex","-1"),i.setAttribute("aria-hidden","true"),Object.assign(i.style,es),w&&A&&A.insertAdjacentElement("afterend",i),()=>{g.off("openchange",e);const r=ke(n),o=we(C,r)||O&&Lt(O.nodesRef.current,_).some(e=>{var t;return we(null==(t=e.context)?void 0:t.elements.floating,r)});(o||a&&["click","mousedown"].includes(a.type))&&E.domReference.current&&wt(E.domReference.current);const l="boolean"==typeof S.current?Ut()||i:S.current.current||i;queueMicrotask(()=>{S.current&&!P.current&&Ae(l)&&(l===r||r===n.body||o)&&l.focus({preventScroll:t}),i.remove()})}},[o,C,U,S,v,E,g,O,_,w,A]),Wt.useEffect(()=>{queueMicrotask(()=>{P.current=!1})},[o]),no(()=>{if(!o&&M)return M.setFocusManagerState({modal:d,closeOnFocusOut:f,open:b,onOpenChange:y,refs:E}),()=>{M.setFocusManagerState(null)}},[o,M,d,b,y,E,f]),no(()=>{if(o)return;if(!U)return;if("function"!=typeof MutationObserver)return;if(h)return;const e=()=>{const e=U.getAttribute("tabindex"),t=F(),n=ke(Ye(C)),r=t.indexOf(n);-1!==r&&(k.current=r),R.current.includes("floating")||n!==E.domReference.current&&0===t.length?"0"!==e&&U.setAttribute("tabindex","0"):"-1"!==e&&U.setAttribute("tabindex","-1")};e();const t=new MutationObserver(e);return t.observe(U,{childList:!0,subtree:!0,attributes:!0}),()=>{t.disconnect()}},[o,C,U,E,R,F,h]);const Y=!o&&I&&(!d||!T)&&(w||d);return Wt.createElement(Wt.Fragment,null,Y&&Wt.createElement(ts,{"data-type":"inside",ref:null==M?void 0:M.beforeInsideRef,onFocus:e=>{if(d){const e=H();Mt("reference"===l[0]?e[0]:e[e.length-1])}else if(null!=M&&M.preserveTabOrder&&M.portalNode)if(P.current=!1,Bt(e,M.portalNode)){const e=Pt(document.body,"next")||A;null==e||e.focus()}else{var t;null==(t=M.beforeOutsideRef.current)||t.focus()}}}),!T&&t("start"),i,t("end"),Y&&Wt.createElement(ts,{"data-type":"inside",ref:null==M?void 0:M.afterInsideRef,onFocus:e=>{if(d)Mt(H()[0]);else if(null!=M&&M.preserveTabOrder&&M.portalNode)if(f&&(P.current=!0),Bt(e,M.portalNode)){const e=Pt(document.body,"prev")||A;null==e||e.focus()}else{var t;null==(t=M.afterOutsideRef.current)||t.focus()}}}))}function Ht(e){return Ae(e.target)&&"BUTTON"===e.target.tagName}function Yt(e){return Ve(e)}function jt(e,t,n){const r=new Map,a="item"===n;let i=e;if(a&&e){const{[ss]:t,[cs]:n,...r}=e;i=r}return{..."floating"===n&&{tabIndex:-1,[rs]:""},...i,...t.map(t=>{const r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r}).concat(e).reduce((e,t)=>t?(Object.entries(t).forEach(t=>{let[n,i]=t;var o;a&&[ss,cs].includes(n)||(0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof i&&(null==(o=r.get(n))||o.push(i),e[n]=function(){for(var e,t=arguments.length,a=new Array(t),i=0;i<t;i++)a[i]=arguments[i];return null==(e=r.get(n))?void 0:e.map(e=>e(...a)).find(e=>void 0!==e)})):e[n]=i)}),e):e,{})}}function Gt(e,t){const[n,r]=e;let a=!1;const i=t.length;for(let o=0,l=i-1;o<i;l=o++){const[e,i]=t[o]||[0,0],[u,s]=t[l]||[0,0];i>=r!=s>=r&&n<=(u-e)*(r-i)/(s-i)+e&&(a=!a)}return a}function Vt(e){void 0===e&&(e={});const{buffer:t=.5,blockPointerEvents:n=!1,requireIntent:r=!0}=e;let a,i=!1,o=null,l=null,u=performance.now();const s=e=>{let{x:n,y:s,placement:c,elements:m,onClose:d,nodeId:p,tree:f}=e;return function(e){function b(){clearTimeout(a),d()}if(clearTimeout(a),!m.domReference||!m.floating||null==c||null==n||null==s)return;const{clientX:E,clientY:_}=e,y=[E,_],g=Ge(e),v="mouseleave"===e.type,N=we(m.floating,g),A=we(m.domReference,g),C=m.domReference.getBoundingClientRect(),h=m.floating.getBoundingClientRect(),T=c.split("-")[0],I=n>h.right-h.width/2,R=s>h.bottom-h.height/2,x=function(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}(y,C),S=h.width>C.width,O=h.height>C.height,M=(S?C:h).left,L=(S?C:h).right,D=(O?C:h).top,P=(O?C:h).bottom;if(N&&(i=!0,!v))return;if(A&&(i=!1),A&&!v)return void(i=!0);if(v&&Ne(e.relatedTarget)&&we(m.floating,e.relatedTarget))return;if(f&&Lt(f.nodesRef.current,p).some(e=>{let{context:t}=e;return null==t?void 0:t.open}))return;if("top"===T&&s>=C.bottom-1||"bottom"===T&&s<=C.top+1||"left"===T&&n>=C.right-1||"right"===T&&n<=C.left+1)return b();let B=[];switch(T){case"top":B=[[M,C.top+1],[M,h.bottom-1],[L,h.bottom-1],[L,C.top+1]];break;case"bottom":B=[[M,h.top+1],[M,C.bottom-1],[L,C.bottom-1],[L,h.top+1]];break;case"left":B=[[h.right-1,P],[h.right-1,D],[C.left+1,D],[C.left+1,P]];break;case"right":B=[[C.right-1,P],[C.right-1,D],[h.left+1,D],[h.left+1,P]]}if(!Gt([E,_],B)){if(i&&!x)return b();if(!v&&r){const t=function(e,t){const n=performance.now(),r=n-u;if(null===o||null===l||0===r)return o=e,l=t,u=n,null;const a=e-o,i=t-l,s=Math.sqrt(a*a+i*i);return o=e,l=t,u=n,s/r}(e.clientX,e.clientY);if(null!==t&&t<.1)return b()}Gt([E,_],function(e){let[n,r]=e;switch(T){case"top":return[[S?n+t/2:I?n+4*t:n-4*t,r+t+1],[S?n-t/2:I?n+4*t:n-4*t,r+t+1],[h.left,I||S?h.bottom-t:h.top],[h.right,I?S?h.bottom-t:h.top:h.bottom-t]];case"bottom":return[[S?n+t/2:I?n+4*t:n-4*t,r-t],[S?n-t/2:I?n+4*t:n-4*t,r-t],[h.left,I||S?h.top+t:h.bottom],[h.right,I?S?h.top+t:h.bottom:h.top+t]];case"left":{const e=[n+t+1,O?r+t/2:R?r+4*t:r-4*t],a=[n+t+1,O?r-t/2:R?r+4*t:r-4*t];return[[R||O?h.right-t:h.left,h.top],[R?O?h.right-t:h.left:h.right-t,h.bottom],e,a]}case"right":return[[n-t,O?r+t/2:R?r+4*t:r-4*t],[n-t,O?r-t/2:R?r+4*t:r-4*t],[R||O?h.left+t:h.right,h.top],[R?O?h.left+t:h.right:h.left+t,h.bottom]]}}([n,s]))?!i&&r&&(a=window.setTimeout(b,40)):b()}}};return s.__options={blockPointerEvents:n},s}var zt,qt,Kt,Wt,Xt,Qt,Zt,$t,Jt,en,tn,nn,rn,an,on,ln,un,sn,cn,mn,dn,pn,fn,bn,En,_n,yn,gn,vn,Nn,An,Cn,hn,Tn,In,Rn,xn,Sn,On,Mn,Ln,Dn,Pn,Bn,kn,wn,Un,Fn,Hn,Yn,jn,Gn,Vn,zn,qn,Kn,Wn,Xn,Qn,Zn,$n,Jn,er,tr,nr,rr,ar,ir,or,lr,ur,sr,cr,mr,dr,pr,fr,br,Er,_r,yr,gr,vr,Nr,Ar,Cr,hr,Tr,Ir,Rr,xr,Sr,Or,Mr,Lr,Dr,Pr,Br,kr,wr,Ur,Fr,Hr,Yr,jr,Gr,Vr,zr,qr,Kr,Wr,Xr,Qr,Zr,$r,Jr,ea,ta,na,ra,aa,ia,oa,la,ua,sa,ca,ma,da,pa,fa,ba,Ea,_a,ya,ga,va,Na,Aa,Ca,ha,Ta,Ia,Ra,xa,Sa,Oa,Ma,La,Da,Pa,Ba,ka,wa,Ua,Fa,Ha,Ya,ja,Ga,Va,za,qa,Ka,Wa,Xa,Qa,Za,$a,Ja,ei,ti,ni,ri,ai,ii,oi,li,ui,si,ci,mi,di,pi,fi,bi,Ei,_i,yi,gi,vi,Ni,Ai,Ci,hi,Ti,Ii,Ri,xi,Si,Oi,Mi,Li,Di,Pi,Bi,ki,wi,Ui,Fi,Hi,Yi,ji,Gi,Vi,zi,qi,Ki,Wi,Xi,Qi,Zi,$i,Ji,eo,to,no,ro={155:t=>{t.exports=e},514:e=>{e.exports=t}},ao={};n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},qt=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,n.t=function(e,t){var r,a,i;if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}for(r=Object.create(null),n.r(r),a={},zt=zt||[null,qt({}),qt([]),qt(qt)],i=2&t&&e;"object"==typeof i&&!~zt.indexOf(i);i=qt(i))Object.getOwnPropertyNames(i).forEach(t=>a[t]=()=>e[t]);return a.default=()=>e,n.d(r,a),r},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},Kt={},n.r(Kt),n.d(Kt,{Accordion:()=>oo,AccordionContent:()=>bo,AccordionIcon:()=>_o,AccordionItem:()=>uo,AccordionToggleTitle:()=>yo,AccordionTrigger:()=>go,Alert:()=>ho,Button:()=>xo,Card:()=>No,Carousel:()=>Oo,CarouselArrows:()=>Mo,CarouselContent:()=>Lo,CarouselItem:()=>Do,CarouselPagination:()=>Po,Checkbox:()=>Ho,CheckboxCard:()=>zo,CheckboxCardBody:()=>qo,CheckboxCardInput:()=>jo,CheckboxCardPrice:()=>Ko,Container:()=>Wo,Divider:()=>Xo,DockBar:()=>Zo,DynamicRadioContent:()=>Xl,Fixed:()=>$o,FooterLegal:()=>nl,FormControl:()=>al,FormGroup:()=>ol,Heading:()=>ul,HeadingStep:()=>sl,Icon:()=>Eo,IconLink:()=>fl,InputError:()=>Uo,InputText:()=>_l,Label:()=>gl,Link:()=>pl,ListItem:()=>el,Modal:()=>Tl,ModalBody:()=>Rl,ModalContent:()=>Ml,ModalFooter:()=>Dl,ModalHeader:()=>Bl,Popover:()=>Os,PopoverContent:()=>Bs,PopoverTrigger:()=>Ds,Portal:()=>Cl,Price:()=>Fl,RadioButton:()=>Gl,RadioCard:()=>Wl,RadioCardBody:()=>Ql,RadioCardInput:()=>zl,RadioCardPrice:()=>Zl,SameHeightGroup:()=>Jl,SameHeightItem:()=>eu,Select:()=>ys,SelectContext:()=>ru,SelectCustom:()=>fs,SelectDropdown:()=>Es,SelectNativeHidden:()=>ds,SelectOption:()=>gs,SimpleFooter:()=>Ns,SimpleHeader:()=>vs,SrOnly:()=>Ao,Static:()=>Jo,Tab:()=>Is,TabList:()=>Rs,TabPanel:()=>xs,Tabs:()=>hs,Tag:()=>As,Text:()=>wo,default:()=>ks,useBodyHeightObserver:()=>fo,useHeightResizeObserver:()=>mo,useKeyboardListener:()=>so,useResponsiveHeight:()=>po,useWindowResize:()=>co}),Wt=n(155),Xt=n.t(Wt,2),Qt=n.n(Wt);const io=(0,Wt.createContext)({activeItems:null,toggleAccordionItems:null}),oo=({mode:e,children:t,onActive:n,onInactive:r})=>{const[a,i]=(0,Wt.useState)([]);return(0,Wt.useEffect)(()=>{0===a.length?r&&r():n&&n()},[a]),Qt().createElement(io.Provider,{value:{activeItems:a,toggleAccordionItems:t=>{"multiple"===e?a.includes(t)?i(e=>e.filter(e=>e!==t)):i(e=>[...e,t]):a.includes(t)?i([]):(i([]),i(e=>[...e,t]))}}},t)},lo=(0,Wt.createContext)(null),uo=({children:e,index:t,activeByDefault:n})=>{const{toggleAccordionItems:r}=(0,Wt.useContext)(io);return(0,Wt.useEffect)(()=>{n&&r&&r(t)},[n]),Qt().createElement(lo.Provider,{value:t},e)},so=(e,t="keydown")=>{(0,Wt.useEffect)(()=>(document.addEventListener(t,e),()=>{document.removeEventListener(t,e)}),[])},co=(e=300)=>{const[t,n]=(0,Wt.useState)({width:window.innerWidth,height:window.innerHeight});return(0,Wt.useEffect)(()=>{const t=()=>{let t=null;t&&clearTimeout(t),t=setTimeout(()=>{n({width:window.innerWidth,height:window.innerHeight})},e)};return window.addEventListener("resize",t),()=>{window.removeEventListener("resize",t)}},[e]),t};Zt=new WeakMap,$t=new WeakMap,Jt={},en=0,tn=function(e){return e&&(e.host||tn(e.parentNode))},nn=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),a=t||function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return a?(r.push.apply(r,Array.from(a.querySelectorAll("[aria-live], script"))),function(e,t,n,r){var a,i,o,l,u,s,c=function(e,t){return t.map(function(t){if(e.contains(t))return t;var n=tn(t);return n&&e.contains(n)?n:null}).filter(function(e){return Boolean(e)})}(t,Array.isArray(e)?e:[e]);return Jt[n]||(Jt[n]=new WeakMap),a=Jt[n],i=[],o=new Set,l=new Set(c),u=function(e){e&&!o.has(e)&&(o.add(e),u(e.parentNode))},c.forEach(u),s=function(e){e&&!l.has(e)&&Array.prototype.forEach.call(e.children,function(e){if(o.has(e))s(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,u=(Zt.get(e)||0)+1,c=(a.get(e)||0)+1;Zt.set(e,u),a.set(e,c),i.push(e),1===u&&l&&$t.set(e,!0),1===c&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){}})},s(t),o.clear(),en++,function(){i.forEach(function(e){var t=Zt.get(e)-1,i=a.get(e)-1;Zt.set(e,t),a.set(e,i),t||($t.has(e)||e.removeAttribute(r),$t.delete(e)),i||e.removeAttribute(n)}),--en||(Zt=new WeakMap,Zt=new WeakMap,$t=new WeakMap,Jt={})}}(r,a,n,"aria-hidden")):function(){return null}};const mo=(e,t,n)=>{(0,Wt.useEffect)(()=>{const r=new ResizeObserver(r=>{for(const a of r)if(a.target===e.current){const e=a.contentRect.height;e!==t&&n(e)}});return e.current&&r.observe(e.current),()=>{r.disconnect()}},[])},po=(e,t)=>(0,Wt.useMemo)(()=>e<768?t.mobile:e>=768&&e<992?t.tablet:t.desktop,[e,t]),fo=(e=300)=>{const[t,n]=(0,Wt.useState)(0);return(0,Wt.useEffect)(()=>{const t=new ResizeObserver(t=>{const r=t[0].target;let a=null;a&&clearTimeout(a),a=setTimeout(()=>{n(r.scrollHeight)},e)});return t.observe(document.body),()=>{t.disconnect()}},[e]),t},bo=({children:e,id:t,className:n,collapseHeight:r,expandHeight:a,"aria-labelledby":i,...o})=>{const{activeItems:l}=(0,Wt.useContext)(io),u=(0,Wt.useContext)(lo),s=(0,Wt.useRef)(null),c=l?.includes(u),[m,d]=(0,Wt.useState)(s.current?.scrollHeight||0),p=a?parseInt(a,10):m,f=c?`${Math.min(m,p)}px`:r;return mo(s,m,d),Qt().createElement("div",{role:"region",className:["brui-overflow-hidden brui-transition-height brui-ease-in-out brui-duration-200 brui-h-0 brui-group brui-max-h-full",r?.trim()?"brui-scrollbar":"",a?.trim()?"brui-scrollbar":"",l?.includes(u)||r?"":"brui-collapse",n].join(" ").trim(),id:t,"aria-labelledby":i,...o,style:{height:f}},Qt().createElement("div",{className:c||a?"":"brui-hidden",ref:s},e))},Eo=({iconClass:e,iconName:t,className:n,...r})=>Qt().createElement("span",{className:[t,e,n].join(" ").trim(),role:"img","aria-hidden":"true","aria-label":" ",...r}),_o=({iconCollapse:e,iconExpand:t,iconClass:n,className:r})=>{const{activeItems:a}=(0,Wt.useContext)(io),i=(0,Wt.useContext)(lo);return Qt().createElement(Eo,{className:r,iconClass:a?.includes(i)?t:e,iconName:n})},yo=({titleCollapse:e,titleExpand:t,className:n})=>{const{activeItems:r}=(0,Wt.useContext)(io),a=(0,Wt.useContext)(lo);return Qt().createElement("div",{className:n},r?.includes(a)?e:t)},go=({children:e,id:t,className:n,"aria-controls":r,...a})=>{const{activeItems:i,toggleAccordionItems:o}=(0,Wt.useContext)(io),l=(0,Wt.useContext)(lo);return Qt().createElement("button",{"aria-expanded":!!i?.includes(l),className:["focus-visible:brui-outline-blue focus-visible:brui-outline focus-visible:brui-outline-2 focus-visible:brui-outline-offset-3 focus-visible:brui-rounded-6 brui-underline-offset-2",n].join(" ").trim(),onClick:()=>{o&&o(l)},id:t,"aria-controls":r,...a},e)},vo={gray:"brui-bg-gray-3",yellow:"brui-bg-yellow brui-border brui-border-yellow-1",solidGray:"brui-bg-gray-1",solidBlue:"brui-bg-blue",solidRed:"brui-bg-red-2",solidYellow:"brui-bg-yellow brui-border brui-border-yellow-1",solidWhite:"brui-bg-white",dropShadow:"brui-shadow-xl brui-border brui-border-gray-4",default:""},No=({variant:e,radius:t,children:n,className:r,defaultPadding:a=!1,...i})=>Qt().createElement("div",{className:[vo[e],t?"brui-rounded-10":"",a?"brui-p-30":"",r].join(" ").trim(),...i},n),Ao=({children:e,className:t})=>Qt().createElement("span",{className:["brui-sr-only",t].join(" ").trim()},e),Co={error:{alertCss:"",iconCss:"brui-text-red",srText:"Error",iconName:"bi_exclamation_c",cardVariant:"default"},info:{alertCss:"brui-border",iconCss:"brui-text-blue",iconName:"bi_big_info_c",srText:"Information",cardVariant:"default"},success:{alertCss:"brui-border",iconCss:"brui-text-green",iconName:"bi_checkmark_c",srText:"Success",cardVariant:"default"},warning:{alertCss:"",iconCss:"brui-text-yellow",srText:"Warning",iconName:"bi_exclamation_c",cardVariant:"default"}},ho=({variant:e,children:t,className:n,iconSize:r="36",...a})=>Qt().createElement(No,{className:[Co[e].alertCss,n].join(" ").trim(),variant:Co[e].cardVariant,role:"alert",...a},Qt().createElement("div",null,Qt().createElement(Eo,{iconName:Co[e].iconName,iconClass:"bi_brui",className:Co[e].iconCss,role:"img","aria-hidden":"true",style:{fontSize:`${r}px`}}),Qt().createElement(Ao,null,Co[e].srText)),t),To={textBlue:"brui-inline-block brui-rounded-4 brui-bg-transparent brui-text-blue brui-underline brui-underline-offset-2 enabled:hover:brui-text-blue-1 enabled:hover:brui-no-underline focus:brui-text-blue-1 focus:brui-no-underline focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0 disabled:brui-opacity-40",icon:"focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3",default:"",primary:"brui-inline-block brui-rounded-30 brui-bg-blue-1 brui-text-white brui-border-blue-1 brui-border-2 enabled:hover:brui-bg-blue enabled:hover:brui-border-blue focus:brui-outline-blue-2 focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-bg-gray-2 disabled:brui-text-white disabled:brui-border-gray-2",secondary:"brui-inline-block brui-rounded-30 brui-bg-white brui-text-blue-1 brui-border-blue-1 brui-border-2 enabled:hover:brui-bg-blue-4 focus:brui-outline-blue-2 focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-bg-gray-2 disabled:brui-text-white",primaryReverse:"brui-inline-block brui-rounded-30 brui-bg-white brui-text-blue-1 brui-border-white brui-border-2 enabled:hover:brui-bg-blue-4 enabled:hover:brui-border-blue-4 focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-text-white",secondaryReverse:"brui-inline-block brui-rounded-30 brui-bg-blue-1 brui-text-white brui-border-white brui-border-2 enabled:hover:brui-bg-blue-3 focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-text-white"},Io={regular:"brui-text-15 brui-leading-17 brui-py-7 brui-px-30",small:"brui-text-14 brui-leading-18 brui-py-5 brui-px-14",default:""},Ro=(0,Wt.forwardRef)(function({variant:e="primary",size:t="regular",className:n,...r},a){const i=(0,Wt.useMemo)(()=>({buttonStyle:[Io[t],To[e],n].join(" ").trim()}),[e,t,n]);return Qt().createElement("button",{className:i.buttonStyle,...r,ref:a})}),xo=Ro;rn="(prefers-reduced-motion: reduce)",an=setTimeout,on=function(){},ln=Array.isArray,un=i(l,"function"),sn=i(l,"string"),cn=i(l,"undefined"),mn=Object.keys,pn="data-"+(dn="splide"),fn=Math.min,bn=Math.max,En=Math.floor,_n=Math.ceil,yn=Math.abs,gn={},vn="mounted",Nn="ready",An="move",Cn="moved",hn="click",Tn="active",In="inactive",Rn="visible",xn="hidden",Sn="refresh",On="updated",Mn="resize",Ln="resized",Dn="drag",Pn="dragging",Bn="dragged",kn="scroll",wn="scrolled",Un="destroy",Fn="arrows:mounted",Hn="arrows:updated",Yn="pagination:mounted",jn="pagination:updated",Gn="navigation:mounted",Vn="autoplay:play",zn="autoplay:playing",qn="autoplay:pause",Kn="lazyload:loaded",Wn="ei",er="ttb",tr={width:["height"],left:["top","right"],right:["bottom","left"],x:["y"],X:["Y"],Y:["X"],ArrowLeft:[$n=(Xn="Arrow")+"Up",Zn=Xn+"Right"],ArrowRight:[Jn=Xn+"Down",Qn=Xn+"Left"]},lr=(ar="aria-")+"selected",pr=ar+"live",fr=ar+"busy",br=ar+"atomic",Er=[nr="role",rr="tabindex","disabled",ir=ar+"controls",or=ar+"current",ur=ar+"label",sr=ar+"labelledby",cr=ar+"hidden",mr=ar+"orientation",dr=ar+"roledescription"],gr=dn,vr=(_r=dn+"__")+"track",Nr=_r+"list",hr=(Ar=_r+"slide")+"__container",Mr=_r+"progress__bar",Lr=_r+"toggle",Dr=_r+"sr",Pr=(yr="is-")+"initialized",jr=[Br=yr+"active",Ur=yr+"visible",kr=yr+"prev",wr=yr+"next",Fr=yr+"loading",Hr=yr+"focus-in",Yr=yr+"overflow"],Gr={slide:Ar,clone:Cr=Ar+"--clone",arrows:Tr=_r+"arrows",arrow:Ir=_r+"arrow",prev:Rr=Ir+"--prev",next:xr=Ir+"--next",pagination:Sr=_r+"pagination",page:Or=Sr+"__page",spinner:_r+"spinner"},Vr="touchstart mousedown",zr="touchmove mousemove",qr="touchend touchcancel mouseup click",Kr="slide",Wr="loop",Xr="fade",Qr=pn+"-interval",Zr={passive:!1,capture:!0},$r={Spacebar:" ",Right:Zn,Left:Qn,Up:$n,Down:Jn},Jr="keydown",na="["+(ea=pn+"-lazy")+"], ["+(ta=ea+"-srcset")+"]",ra=[" ","Enter"],aa=Object.freeze({__proto__:null,Media:function(e,t,n){function r(e){e&&c.destroy()}function a(e,t){var n=matchMedia(t);c.bind(n,"change",i),m.push([e,n])}function i(){var t=l.is(7),a=n.direction,i=m.reduce(function(e,t){return h(e,t[1].matches?t[0]:{})},{});T(n),o(i),n.destroy?e.destroy("completely"===n.destroy):t?(r(!0),e.mount()):a!==n.direction&&e.refresh()}function o(t,r,a){h(n,t),r&&h(Object.getPrototypeOf(n),t),!a&&l.is(1)||e.emit(On,n)}var l=e.state,u=n.breakpoints||{},s=n.reducedMotion||{},c=Q(),m=[];return{setup:function(){var e="min"===n.mediaQuery;mn(u).sort(function(t,n){return e?+t-+n:+n-+t}).forEach(function(t){a(u[t],"("+(e?"min":"max")+"-width:"+t+"px)")}),a(s,rn),i()},destroy:r,reduce:function(e){matchMedia(rn).matches&&(e?h(n,s):T(n,mn(s)))},set:o}},Direction:function(e,t,n){return{resolve:function(e,t,r){var a="rtl"!==(r=r||n.direction)||t?r===er?0:-1:1;return tr[e]&&tr[e][a]||e.replace(/width|left|right/i,function(e,t){var n=tr[e.toLowerCase()][a]||e;return t>0?n.charAt(0).toUpperCase()+n.slice(1):n})},orient:function(e){return e*("rtl"===n.direction?1:-1)}}},Elements:function(e,t,n){function a(){var e,t,r;s=l("."+vr),c=N(s,"."+Nr),G(s&&c,"A track/list element is missing."),f(x,v(c,"."+Ar+":not(."+Cr+")")),A({arrows:Tr,pagination:Sr,prev:Rr,next:xr,bar:Mr,toggle:Lr},function(e,t){T[t]=l("."+e)}),C(T,{root:y,track:s,list:c,slides:x}),t=y.id||""+(e=dn)+X(gn[e]=(gn[e]||0)+1),r=n.role,y.id=t,s.id=s.id||t+"-track",c.id=c.id||t+"-list",!L(y,nr)&&"SECTION"!==y.tagName&&r&&R(y,nr,r),R(y,dr,h.carousel),R(c,nr,"presentation"),o()}function i(e){var t=Er.concat("style");r(x),H(y,S),H(s,O),I([s,c],t),I(y,e?t:["style",dr])}function o(){H(y,S),H(s,O),S=u(gr),O=u(vr),E(y,S),E(s,O),R(y,ur,n.label),R(y,sr,n.labelledby)}function l(e){var t=U(y,e);return t&&function(e,t){if(un(e.closest))return e.closest(t);for(var n=e;n&&1===n.nodeType&&!g(n,t);)n=n.parentElement;return n}(t,"."+gr)===y?t:void 0}function u(e){return[e+"--"+n.type,e+"--"+n.direction,n.drag&&e+"--draggable",n.isNavigation&&e+"--nav",e===gr&&Br]}var s,c,m,d=Z(e),p=d.on,_=d.bind,y=e.root,h=n.i18n,T={},x=[],S=[],O=[];return C(T,{setup:a,mount:function(){p(Sn,i),p(Sn,a),p(On,o),_(document,Vr+" keydown",function(e){m="keydown"===e.type},{capture:!0}),_(y,"focusin",function(){b(y,Hr,!!m)})},destroy:i})},Slides:function(e,t,n){function a(){x.forEach(function(e,t){l(e,t,-1)})}function o(){s(function(e){e.destroy()}),r(w)}function l(t,n,r){var a=function(e,t,n,r){function a(){var a=e.splides.map(function(e){var n=e.splide.Components.Slides.getAt(t);return n?n.slide.id:""}).join(" ");R(r,ur,W(v.slideX,(O?n:t)+1)),R(r,ir,a),R(r,nr,C?"button":""),C&&I(r,dr)}function o(){s||l()}function l(){var n,a;s||(n=e.index,(a=u())!==D(r,Br)&&(b(r,Br,a),R(r,or,y&&a||""),d(a?Tn:In,k)),function(){var t,n=function(){if(e.is(Xr))return u();var t=P(f.Elements.track),n=P(r),a=h("left",!0),i=h("right",!0);return En(t[a])<=_n(n[a])&&En(n[i])<=_n(t[i])}(),a=!n&&(!u()||O);e.state.is([4,5])||R(r,cr,a||""),R(F(r,_.focusableNodes||""),rr,a?-1:""),C&&R(r,rr,a?-1:0),n!==D(r,Ur)&&(b(r,Ur,n),d(n?Rn:xn,k)),n||document.activeElement!==r||(t=f.Slides.getAt(e.index))&&M(t.slide)}(),b(r,kr,t===n-1),b(r,wr,t===n+1))}function u(){var r=e.index;return r===t||_.cloneStatus&&r===n}var s,c=Z(e),m=c.on,d=c.emit,p=c.bind,f=e.Components,E=e.root,_=e.options,y=_.isNavigation,g=_.updateOnMove,v=_.i18n,A=_.pagination,C=_.slideFocus,h=f.Direction.resolve,T=L(r,"style"),x=L(r,ur),O=n>-1,B=N(r,"."+hr),k={index:t,slideIndex:n,slide:r,container:B,isClone:O,mount:function(){O||(r.id=E.id+"-slide"+X(t+1),R(r,nr,A?"tabpanel":"group"),R(r,dr,v.slide),R(r,ur,x||W(v.slideLabel,[t+1,e.length]))),p(r,"click",i(d,hn,k)),p(r,"keydown",i(d,"sk",k)),m([Cn,"sh",wn],l),m(Gn,a),g&&m(An,o)},destroy:function(){s=!0,c.destroy(),H(r,jr),I(r,Er),R(r,"style",T),R(r,ur,x||"")},update:l,style:function(e,t,n){S(n&&B||r,e,t)},isWithin:function(n,r){var a=yn(n-t);return O||!_.rewind&&!e.is(Wr)||(a=fn(a,e.length-a)),a<=r}};return k}(e,n,r,t);a.mount(),w.push(a),w.sort(function(e,t){return e.index-t.index})}function u(e){return e?f(function(e){return!e.isClone}):w}function s(e,t){u(t).forEach(e)}function f(e){return w.filter(un(e)?e:function(t){return sn(e)?g(t.slide,e):p(m(e),t.index)})}var v=Z(e),A=v.on,C=v.emit,h=v.bind,T=t.Elements,x=T.slides,O=T.list,w=[];return{mount:function(){a(),A(Sn,o),A(Sn,a)},destroy:o,update:function(){s(function(e){e.update()})},register:l,get:u,getIn:function(e){var r=t.Controller,a=r.toIndex(e),i=r.hasFocus()?1:n.perPage;return f(function(e){return z(e.index,a,a+i-1)})},getAt:function(e){return f(e)[0]},add:function(e,t){d(e,function(e){var r,a,o,l,u;sn(e)&&(e=k(e)),c(e)&&((r=x[t])?y(e,r):_(O,e),E(e,n.classes.slide),a=e,o=i(C,Mn),l=F(a,"img"),(u=l.length)?l.forEach(function(e){h(e,"load error",function(){--u||o()})}):o())}),C(Sn)},remove:function(e){B(f(e).map(function(e){return e.slide})),C(Sn)},forEach:s,filter:f,style:function(e,t,n){s(function(r){r.style(e,t,n)})},getLength:function(e){return e?x.length:w.length},isEnough:function(){return w.length>n.perPage}}},Layout:function(e,t,n){function r(){_=n.direction===er,S(R,"maxWidth",j(n.width)),S(x,T("paddingLeft"),o(!1)),S(x,T("paddingRight"),o(!0)),a(!0)}function a(e){var t,r=P(R);(e||y.width!==r.width||y.height!==r.height)&&(S(x,"height",(t="",_&&(G(t=l(),"height or heightRatio is missing."),t="calc("+t+" - "+o(!1)+" - "+o(!0)+")"),t)),L(T("marginRight"),j(n.gap)),L("width",n.autoWidth?null:j(n.fixedWidth)||(_?"":s())),L("height",j(n.fixedHeight)||(_?n.autoHeight?null:s():l()),!0),y=r,C(Ln),g!==(g=E())&&(b(R,Yr,g),C("overflow",g)))}function o(e){var t=n.padding,r=T(e?"right":"left");return t&&j(t[r]||(u(t)?0:t))||"0px"}function l(){return j(n.height||P(O).width*n.heightRatio)}function s(){var e=j(n.gap);return"calc((100%"+(e&&" + "+e)+")/"+(n.perPage||1)+(e&&" - "+e)+")"}function c(){return P(O)[T("width")]}function m(e,t){var n=M(e||0);return n?P(n.slide)[T("width")]+(t?0:f()):0}function d(e,t){var n,r,a=M(e);return a?(n=P(a.slide)[T("right")],r=P(O)[T("left")],yn(n-r)+(t?0:f())):0}function p(t){return d(e.length-1)-d(0)+m(0,t)}function f(){var e=M(0);return e&&parseFloat(S(e.slide,T("marginRight")))||0}function E(){return e.is(Xr)||p(!0)>c()}var _,y,g,v=Z(e),N=v.on,A=v.bind,C=v.emit,h=t.Slides,T=t.Direction.resolve,I=t.Elements,R=I.root,x=I.track,O=I.list,M=h.getAt,L=h.style;return{mount:function(){var e,t;r(),A(window,"resize load",(e=i(C,Mn),t=$(0,e,null,1),function(){t.isPaused()&&t.start()})),N([On,Sn],r),N(Mn,a)},resize:a,listSize:c,slideSize:m,sliderSize:p,totalSize:d,getPadding:function(e){return parseFloat(S(x,T("padding"+(e?"Right":"Left"))))||0},isOverflow:E}},Clones:function(e,t,n){function a(){m(Sn,i),m([On,Mn],l),(s=u())&&(function(t){var r=p.get().slice(),a=r.length;if(a){for(;r.length<t;)f(r,r);f(r.slice(-t),r.slice(0,t)).forEach(function(i,o){var l=o<t,u=function(t,r){var a=t.cloneNode(!0);return E(a,n.classes.clone),a.id=e.root.id+"-clone"+X(r+1),a}(i.slide,o);l?y(u,r[0].slide):_(d.list,u),f(g,u),p.register(u,o-t+(l?0:a),i.index)})}}(s),t.Layout.resize(!0))}function i(){o(),a()}function o(){B(g),r(g),c.destroy()}function l(){var e=u();s!==e&&(s<e||!e)&&c.emit(Sn)}function u(){var r,a=n.clones;return e.is(Wr)?cn(a)&&(a=(r=n[b("fixedWidth")]&&t.Layout.slideSize(0))&&_n(P(d.track)[b("width")]/r)||n[b("autoWidth")]&&e.length||2*n.perPage):a=0,a}var s,c=Z(e),m=c.on,d=t.Elements,p=t.Slides,b=t.Direction.resolve,g=[];return{mount:a,destroy:o}},Move:function(e,t,n){function r(){t.Controller.isBusy()||(t.Scroll.cancel(),a(e.index),t.Slides.update())}function a(e){i(s(e,!0))}function i(n,r){if(!e.is(Xr)){var a=r?n:function(n){if(e.is(Wr)){var r=u(n),a=r>t.Controller.getEnd();(r<0||a)&&(n=o(n,a))}return n}(n);S(R,"transform","translate"+h("X")+"("+a+"px)"),n!==a&&b("sh")}}function o(e,t){var n=e-m(t),r=A();return e-T(r*(_n(yn(n)/r)||1))*(t?1:-1)}function l(){i(c(),!0),d.cancel()}function u(e){var n,r,a,i,o,l;for(n=t.Slides.get(),r=0,a=1/0,i=0;i<n.length&&(o=n[i].index,(l=yn(s(o,!0)-e))<=a);i++)a=l,r=o;return r}function s(t,r){var a=T(v(t-1)-function(e){var t=n.focus;return"center"===t?(N()-y(e,!0))/2:+t*y(e)||0}(t));return r?function(t){return n.trimSpace&&e.is(Kr)&&(t=q(t,0,T(A(!0)-N()))),t}(a):a}function c(){var e=h("left");return P(R)[e]-P(x)[e]+T(g(!1))}function m(e){return s(e?t.Controller.getEnd():0,!!n.trimSpace)}var d,p=Z(e),f=p.on,b=p.emit,E=e.state.set,_=t.Layout,y=_.slideSize,g=_.getPadding,v=_.totalSize,N=_.listSize,A=_.sliderSize,C=t.Direction,h=C.resolve,T=C.orient,I=t.Elements,R=I.list,x=I.track;return{mount:function(){d=t.Transition,f([vn,Ln,On,Sn],r)},move:function(e,t,n,r){var a,u;e!==t&&(a=e>n,u=T(o(c(),a)),a?u>=0:u<=R[h("scrollWidth")]-P(x)[h("width")])&&(l(),i(o(c(),e>n),!0)),E(4),b(An,t,n,e),d.start(t,function(){E(3),b(Cn,t,n,e),r&&r()})},jump:a,translate:i,shift:o,cancel:l,toIndex:u,toPosition:s,getPosition:c,getLimit:m,exceededLimit:function(e,t){t=cn(t)?c():t;var n=!0!==e&&T(t)<T(m(!1)),r=!1!==e&&T(t)>T(m(!0));return n||r},reposition:r}},Controller:function(e,t,n){function r(){E=x(!0),_=n.perMove,y=n.perPage,b=s();var e=q(P,0,S?b:E-1);e!==P&&(P=e,A.reposition())}function a(){b!==s()&&N(Wn)}function o(e,t){var n=_||(p()?1:y),r=l(P+n*(e?-1:1),P,!(_||p()));return-1===r&&M&&!V(C(),h(!e),1)?e?0:b:t?r:u(r)}function l(t,r,a){if(R()||p()){var i=function(t){if(M&&"move"===n.trimSpace&&t!==P)for(var r=C();r===T(t,!0)&&z(t,0,e.length-1,!n.rewind);)t<P?--t:++t;return t}(t);i!==t&&(r=t,t=i,a=!1),t<0||t>b?t=_||!z(0,t,r,!0)&&!z(b,r,t,!0)?O?a?t<0?-(E%y||y):E:t:n.rewind?t<0?b:0:-1:c(m(t)):a&&t!==r&&(t=c(m(r)+(t<r?-1:1)))}else t=-1;return t}function u(e){return O?(e+E)%E||0:e}function s(){for(var e=E-(p()||O&&_?1:y);S&&e-- >0;)if(T(E-1,!0)!==T(e,!0)){e++;break}return q(e,0,E-1)}function c(e){return q(p()?e:y*e,0,b)}function m(e){return p()?fn(e,b):En((e>=b?E-1:e)/y)}function d(e){e!==P&&(B=P,P=e)}function p(){return!cn(n.focus)||n.isNavigation}function f(){return e.state.is([4,5])&&!!n.waitForTransition}var b,E,_,y,g=Z(e),v=g.on,N=g.emit,A=t.Move,C=A.getPosition,h=A.getLimit,T=A.toPosition,I=t.Slides,R=I.isEnough,x=I.getLength,S=n.omitEnd,O=e.is(Wr),M=e.is(Kr),L=i(o,!1),D=i(o,!0),P=n.start||0,B=P;return{mount:function(){r(),v([On,Sn,Wn],r),v(Ln,a)},go:function(e,t,n){if(!f()){var r=function(e){var t,n,r,a=P;return sn(e)?(n=(t=e.match(/([+\-<>])(\d+)?/)||[])[1],r=t[2],"+"===n||"-"===n?a=l(P+ +(""+n+(+r||1)),P):">"===n?a=r?c(+r):L(!0):"<"===n&&(a=D(!0))):a=O?e:q(e,0,b),a}(e),a=u(r);a>-1&&(t||a!==P)&&(d(a),A.move(r,a,B,n))}},scroll:function(e,n,r,a){t.Scroll.scroll(e,n,r,function(){var e=u(A.toIndex(C()));d(S?fn(e,b):e),a&&a()})},getNext:L,getPrev:D,getAdjacent:o,getEnd:s,setIndex:d,getIndex:function(e){return e?B:P},toIndex:c,toPage:m,toDest:function(e){var t=A.toIndex(e);return M?q(t,0,b):t},hasFocus:p,isBusy:f}},Arrows:function(e,t,n){function r(){var e;!(e=n.arrows)||M&&L||(S=h||x("div",g.arrows),M=u(!0),L=u(!1),c=!0,_(S,[M,L]),!h&&y(S,T)),M&&L&&(C(D,{prev:M,next:L}),O(S,e?"":"none"),E(S,m=Tr+"--"+n.direction),e&&(p([vn,Cn,Sn,wn,Wn],s),f(L,"click",i(l,">")),f(M,"click",i(l,"<")),s(),R([M,L],ir,T.id),b(Fn,M,L))),p(On,a)}function a(){o(),r()}function o(){d.destroy(),H(S,m),c?(B(h?[M,L]:S),M=L=null):I([M,L],Er)}function l(e){A.go(e,!0)}function u(e){return k('<button class="'+g.arrow+" "+(e?g.prev:g.next)+'" type="button"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40" focusable="false"><path d="'+(n.arrowPath||"m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z")+'" />')}function s(){if(M&&L){var t=e.index,n=A.getPrev(),r=A.getNext(),a=n>-1&&t<n?v.last:v.prev,i=r>-1&&t>r?v.first:v.next;M.disabled=n<0,L.disabled=r<0,R(M,ur,a),R(L,ur,i),b(Hn,M,L,n,r)}}var c,m,d=Z(e),p=d.on,f=d.bind,b=d.emit,g=n.classes,v=n.i18n,N=t.Elements,A=t.Controller,h=N.arrows,T=N.track,S=h,M=N.prev,L=N.next,D={};return{arrows:D,mount:r,destroy:o,update:s}},Autoplay:function(e,t,n){function r(){E()&&t.Slides.isEnough()&&(f.start(!n.resetProgress),s=u=A=!1,o(),p(Vn))}function a(e){void 0===e&&(e=!0),A=!!e,o(),E()||(f.pause(),p(qn))}function i(){A||(u||s?a(!1):r())}function o(){v&&(b(v,Br,!A),R(v,ur,n.i18n[A?"play":"pause"]))}function l(e){var r=t.Slides.getAt(e);f.set(r&&+L(r.slide,Qr)||n.interval)}var u,s,c=Z(e),m=c.on,d=c.bind,p=c.emit,f=$(n.interval,e.go.bind(e,">"),function(e){var t=_.bar;t&&S(t,"width",100*e+"%"),p(zn,e)}),E=f.isPaused,_=t.Elements,y=t.Elements,g=y.root,v=y.toggle,N=n.autoplay,A="pause"===N;return{mount:function(){N&&(n.pauseOnHover&&d(g,"mouseenter mouseleave",function(e){u="mouseenter"===e.type,i()}),n.pauseOnFocus&&d(g,"focusin focusout",function(e){s="focusin"===e.type,i()}),v&&d(v,"click",function(){A?r():a(!0)}),m([An,kn,Sn],f.rewind),m(An,l),v&&R(v,ir,_.track.id),A||r(),o())},destroy:f.cancel,play:r,pause:a,isPaused:E}},Cover:function(e,t,n){function r(e){t.Slides.forEach(function(t){var n=N(t.container||t.slide,"img");n&&n.src&&a(e,n,t)})}function a(e,t,n){n.style("background",e?'center/cover no-repeat url("'+t.src+'")':"",!0),O(t,e?"none":"")}var o=Z(e).on;return{mount:function(){n.cover&&(o(Kn,i(a,!0)),o([vn,On,Sn],i(r,!0)))},destroy:i(r,!1)}},Scroll:function(e,t,n){function r(e,n,r,u,m){var d,_,g,A=E();l(),!r||v&&y()||(d=t.Layout.sliderSize(),_=K(e)*d*En(yn(e)/d)||0,e=b.toPosition(t.Controller.toDest(e%d))+_),g=V(A,e,1),N=1,n=g?0:n||bn(yn(e-A)/1.5,800),c=u,s=$(n,a,i(o,A,e,m),1),f(5),p(kn),s.start()}function a(){f(3),c&&c(),p(wn)}function o(e,t,a,i){var o,l,u=E(),s=(e+(t-e)*(o=i,(l=n.easingFunc)?l(o):1-Math.pow(1-o,4))-u)*N;g(u+s),v&&!a&&y()&&(N*=.6,yn(s)<10&&r(_(y(!0)),600,!1,c,!0))}function l(){s&&s.cancel()}function u(){s&&!s.isPaused()&&(l(),a())}var s,c,m=Z(e),d=m.on,p=m.emit,f=e.state.set,b=t.Move,E=b.getPosition,_=b.getLimit,y=b.exceededLimit,g=b.translate,v=e.is(Kr),N=1;return{mount:function(){d(An,l),d([On,Sn],u)},destroy:l,scroll:r,cancel:u}},Drag:function(e,t,n){function r(){var e=n.drag;b(!e),v="free"===e}function a(e){var t,r,a;A=!1,C||(t=f(e),r=e.target,a=n.noDrag,g(r,"."+Or+", ."+Ir)||a&&g(r,a)||!t&&e.button||(D.isBusy()?w(e,!0):(h=t?P:window,N=O.is([4,5]),y=null,x(h,zr,i,Zr),x(h,qr,o,Zr),M.cancel(),L.cancel(),s(e))))}function i(t){if(O.is(6)||(O.set(6),R(Dn)),t.cancelable)if(N){M.translate(E+c(t)/(G&&e.is(Kr)?5:1));var r=m(t)>200,a=G!==(G=j());(r||a)&&s(t),A=!0,R(Pn),w(t)}else(function(e){return yn(c(e))>yn(c(e,!0))})(t)&&(N=function(e){var t=n.dragMinThreshold,r=u(t),a=r&&t.mouse||0,i=(r?t.touch:+t)||10;return yn(c(e))>(f(e)?i:a)}(t),w(t))}function o(r){O.is(6)&&(O.set(3),R(Bn)),N&&(function(r){var a=function(t){if(e.is(Wr)||!G){var n=m(t);if(n&&n<200)return c(t)/n}return 0}(r),i=function(e){return H()+K(e)*fn(yn(e)*(n.flickPower||600),v?1/0:t.Layout.listSize()*(n.flickMaxPages||1))}(a),o=n.rewind&&n.rewindByDrag;B(!1),v?D.scroll(i,0,n.snap):e.is(Xr)?D.go(F(K(a))<0?o?"<":"-":o?">":"+"):e.is(Kr)&&G&&o?D.go(j(!0)?">":"<"):D.go(D.toDest(i),!0),B(!0)}(r),w(r)),S(h,zr,i),S(h,qr,o),N=!1}function l(e){!C&&A&&w(e,!0)}function s(e){y=_,_=e,E=H()}function c(e,t){return p(e,t)-p(d(e),t)}function m(e){return Y(e)-Y(d(e))}function d(e){return _===e&&y||_}function p(e,t){return(f(e)?e.changedTouches[0]:e)["page"+U(t?"Y":"X")]}function f(e){return"undefined"!=typeof TouchEvent&&e instanceof TouchEvent}function b(e){C=e}var E,_,y,v,N,A,C,h,T=Z(e),I=T.on,R=T.emit,x=T.bind,S=T.unbind,O=e.state,M=t.Move,L=t.Scroll,D=t.Controller,P=t.Elements.track,B=t.Media.reduce,k=t.Direction,U=k.resolve,F=k.orient,H=M.getPosition,j=M.exceededLimit,G=!1;return{mount:function(){x(P,zr,on,Zr),x(P,qr,on,Zr),x(P,Vr,a,Zr),x(P,"click",l,{capture:!0}),x(P,"dragstart",w),I([vn,On],r)},disable:b,isDragging:function(){return N}}},Keyboard:function(e,t,n){function r(){var e=n.keyboard;e&&(l="global"===e?window:p,m(l,Jr,o))}function a(){d(l,Jr)}function i(){var e=u;u=!0,an(function(){u=e})}function o(t){if(!u){var n=J(t);n===f(Qn)?e.go("<"):n===f(Zn)&&e.go(">")}}var l,u,s=Z(e),c=s.on,m=s.bind,d=s.unbind,p=e.root,f=t.Direction.resolve;return{mount:function(){r(),c(On,a),c(On,r),c(An,i)},destroy:a,disable:function(e){u=e}}},LazyLoad:function(e,t,n){function a(){r(y),t.Slides.forEach(function(e){F(e.slide,na).forEach(function(t){var r,a,i,o=L(t,ea),l=L(t,ta);o===t.src&&l===t.srcset||(r=n.classes.spinner,i=N(a=t.parentElement,"."+r)||x("span",r,a),y.push([t,e,i]),t.src||O(t,"none"))})}),b?s():(d(_),m(_,o),o())}function o(){(y=y.filter(function(t){var r=n.perPage*((n.preloadPages||1)+1)-1;return!t[1].isWithin(e.index,r)||l(t)})).length||d(_)}function l(e){var t=e[0];E(e[1].slide,Fr),p(t,"load error",i(u,e)),R(t,"src",L(t,ea)),R(t,"srcset",L(t,ta)),I(t,ea),I(t,ta)}function u(e,t){var n=e[0],r=e[1];H(r.slide,Fr),"error"!==t.type&&(B(e[2]),O(n,""),f(Kn,n,r),f(Mn)),b&&s()}function s(){y.length&&l(y.shift())}var c=Z(e),m=c.on,d=c.off,p=c.bind,f=c.emit,b="sequential"===n.lazyLoad,_=[Cn,wn],y=[];return{mount:function(){n.lazyLoad&&(a(),m(Sn,a))},destroy:i(r,y),check:o}},Pagination:function(e,t,n){function o(){d&&(B(S?a(d.children):d),H(d,p),r(L),d=null),f.destroy()}function l(e){h(">"+e,!0)}function u(e,t){var n,r=L.length,a=J(t),i=s(),o=-1;a===T(Zn,!1,i)?o=++e%r:a===T(Qn,!1,i)?o=(--e+r)%r:"Home"===a?o=0:"End"===a&&(o=r-1),(n=L[o])&&(M(n.button),h(">"+o),w(t,!0))}function s(){return n.paginationDirection||n.direction}function c(e){return L[N.toPage(e)]}function m(){var e,t,n=c(C(!0)),r=c(C());n&&(H(e=n.button,Br),I(e,lr),R(e,rr,-1)),r&&(E(t=r.button,Br),R(t,lr,!0),R(t,rr,"")),_(jn,{list:d,items:L},n,r)}var d,p,f=Z(e),b=f.on,_=f.emit,y=f.bind,g=t.Slides,v=t.Elements,N=t.Controller,A=N.hasFocus,C=N.getIndex,h=N.go,T=t.Direction.resolve,S=v.pagination,L=[];return{items:L,mount:function t(){o(),b([On,Sn,Wn],t);var r=n.pagination;S&&O(S,r?"":"none"),r&&(b([An,kn,wn],m),function(){var t,r,a,o,c,m=e.length,f=n.classes,b=n.i18n,_=n.perPage,C=A()?N.getEnd()+1:_n(m/_);for(E(d=S||x("ul",f.pagination,v.track.parentElement),p=Sr+"--"+s()),R(d,nr,"tablist"),R(d,ur,b.select),R(d,mr,s()===er?"vertical":""),t=0;t<C;t++)r=x("li",null,d),a=x("button",{class:f.page,type:"button"},r),o=g.getIn(t).map(function(e){return e.slide.id}),c=!A()&&_>1?b.pageX:b.slideX,y(a,"click",i(l,t)),n.paginationKeyboard&&y(a,"keydown",i(u,t)),R(r,nr,"presentation"),R(a,nr,"tab"),R(a,ir,o.join(" ")),R(a,ur,W(c,t+1)),R(a,rr,-1),L.push({li:r,button:a,page:t})}(),m(),_(Yn,{list:d,items:L},c(e.index)))},destroy:o,getAt:c,update:m}},Sync:function(e,t,n){function a(){var t,n;e.splides.forEach(function(t){t.isParent||(l(e,t.splide),l(t.splide,e))}),m&&((n=(t=Z(e)).on)(hn,s),n("sk",c),n([vn,On],u),f.push(t),t.emit(Gn,e.splides))}function o(){f.forEach(function(e){e.destroy()}),r(f)}function l(e,t){var n=Z(e);n.on(An,function(e,n,r){t.go(t.is(Wr)?r:e)}),f.push(n)}function u(){R(t.Elements.list,mr,n.direction===er?"vertical":"")}function s(t){e.go(t.index)}function c(e,t){p(ra,J(t))&&(s(e),w(t))}var m=n.isNavigation,d=n.slideFocus,f=[];return{setup:i(t.Media.set,{slideFocus:cn(d)?m:d},!0),mount:a,destroy:o,remount:function(){o(),a()}}},Wheel:function(e,t,n){function r(r){if(r.cancelable){var a=r.deltaY,o=a<0,l=Y(r),u=n.wheelMinThreshold||0,s=n.wheelSleep||0;yn(a)>u&&l-i>s&&(e.go(o?"<":">"),i=l),function(r){return!n.releaseWheel||e.state.is(4)||-1!==t.Controller.getAdjacent(r)}(o)&&w(r)}}var a=Z(e).bind,i=0;return{mount:function(){n.wheel&&a(t.Elements.track,"wheel",r,Zr)}}},Live:function(e,t,n){function r(e){R(l,fr,e),e?(_(l,s),c.start()):(B(s),c.cancel())}function a(e){u&&R(l,pr,e?"off":"polite")}var o=Z(e).on,l=t.Elements.track,u=n.live&&!n.isNavigation,s=x("span",Dr),c=$(90,i(r,!1));return{mount:function(){u&&(a(!t.Autoplay.isPaused()),R(l,br,!0),s.textContent="…",o(Vn,i(a,!0)),o(qn,i(a,!1)),o([Cn,wn],i(r,!0)))},disable:a,destroy:function(){I(l,[pr,br,fr]),B(s)}}}}),ia={type:"slide",role:"region",speed:400,perPage:1,cloneStatus:!0,arrows:!0,pagination:!0,paginationKeyboard:!0,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,easing:"cubic-bezier(0.25, 1, 0.5, 1)",drag:!0,direction:"ltr",trimSpace:!0,focusableNodes:"a, button, textarea, input, select, iframe",live:!0,classes:Gr,i18n:{prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay",carousel:"carousel",slide:"slide",select:"Select a slide to show",slideLabel:"%s of %s"},reducedMotion:{speed:0,rewindSpeed:0,autoplay:"pause"}},oa=function(){function e(t,n){var r,a;this.event=Z(),this.Components={},this.state=(r=1,{set:function(e){r=e},is:function(e){return p(m(e),r)}}),this.splides=[],this._o={},this._E={},G(a=sn(t)?U(document,t):t,a+" is invalid."),this.root=a,n=h({label:L(a,ur)||"",labelledby:L(a,sr)||""},ia,e.defaults,n||{});try{h(n,JSON.parse(L(a,pn)))}catch(e){G(!1,"Invalid JSON")}this._o=Object.create(h({},n))}var t,n,i=e.prototype;return i.mount=function(e,t){var n=this,r=this.state,a=this.Components;return G(r.is([1,7]),"Already mounted!"),r.set(1),this._C=a,this._T=t||this._T||(this.is(Xr)?ee:te),this._E=e||this._E,A(C({},aa,this._E,{Transition:this._T}),function(e,t){var r=e(n,a,n._o);a[t]=r,r.setup&&r.setup()}),A(a,function(e){e.mount&&e.mount()}),this.emit(vn),E(this.root,Pr),r.set(3),this.emit(Nn),this},i.sync=function(e){return this.splides.push({splide:e}),e.splides.push({splide:this,isParent:!0}),this.state.is(3)&&(this._C.Sync.remount(),e.Components.Sync.remount()),this},i.go=function(e){return this._C.Controller.go(e),this},i.on=function(e,t){return this.event.on(e,t),this},i.off=function(e){return this.event.off(e),this},i.emit=function(e){var t;return(t=this.event).emit.apply(t,[e].concat(a(arguments,1))),this},i.add=function(e,t){return this._C.Slides.add(e,t),this},i.remove=function(e){return this._C.Slides.remove(e),this},i.is=function(e){return this._o.type===e},i.refresh=function(){return this.emit(Sn),this},i.destroy=function(e){void 0===e&&(e=!0);var t=this.event,n=this.state;return n.is(1)?Z(this).on(Nn,this.destroy.bind(this,e)):(A(this._C,function(t){t.destroy&&t.destroy(e)},!0),t.emit(Un),t.destroy(),e&&r(this.splides),n.set(7)),this},t=e,(n=[{key:"options",get:function(){return this._o},set:function(e){this._C.Media.set(e,!0,!0)}},{key:"length",get:function(){return this._C.Slides.getLength(!0)}},{key:"index",get:function(){return this._C.Controller.getIndex()}}])&&function(e,t){var n,r;for(n=0;n<t.length;n++)(r=t[n]).enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),(la=oa).defaults={},la.STATES={CREATED:1,MOUNTED:2,IDLE:3,MOVING:4,SCROLLING:5,DRAGGING:6,DESTROYED:7},ua=[[vn,"onMounted"],[Nn,"onReady"],[An,"onMove"],[Cn,"onMoved"],[hn,"onClick"],[Tn,"onActive"],[In,"onInactive"],[Rn,"onVisible"],[xn,"onHidden"],[Sn,"onRefresh"],[On,"onUpdated"],[Mn,"onResize"],[Ln,"onResized"],[Dn,"onDrag"],[Pn,"onDragging"],[Bn,"onDragged"],[kn,"onScroll"],[wn,"onScrolled"],[Un,"onDestroy"],[Fn,"onArrowsMounted"],[Hn,"onArrowsUpdated"],[Yn,"onPaginationMounted"],[jn,"onPaginationUpdated"],[Gn,"onNavigationMounted"],[Vn,"onAutoplayPlay"],[zn,"onAutoplayPlaying"],[qn,"onAutoplayPause"],[Kn,"onLazyLoadLoaded"]],sa=({children:e,className:t,...n})=>Qt().createElement("div",{className:ne("splide__track",t),...n},Qt().createElement("ul",{className:"splide__list"},e)),ca=class extends Qt().Component{constructor(){super(...arguments),this.splideRef=Qt().createRef(),this.slides=[]}componentDidMount(){const{options:e,extensions:t,transition:n}=this.props,{current:r}=this.splideRef;r&&(this.splide=new la(r,e),this.bind(this.splide),this.splide.mount(t,n),this.options=ie({},e||{}),this.slides=this.getSlides())}componentWillUnmount(){this.splide&&(this.splide.destroy(),this.splide=void 0),this.options=void 0,this.slides.length=0}componentDidUpdate(){if(!this.splide)return;const{options:e}=this.props;e&&!ae(this.options,e)&&(this.splide.options=e,this.options=ie({},e));const t=this.getSlides();var n,r;n=this.slides,r=t,(n.length!==r.length||n.some((e,t)=>e!==r[t]))&&(this.splide.refresh(),this.slides=t)}sync(e){var t;null==(t=this.splide)||t.sync(e)}go(e){var t;null==(t=this.splide)||t.go(e)}getSlides(){var e;if(this.splide){const t=null==(e=this.splide.Components.Elements)?void 0:e.list.children;return t&&Array.prototype.slice.call(t)||[]}return[]}bind(e){ua.forEach(([t,n])=>{const r=this.props[n];"function"==typeof r&&e.on(t,(...t)=>{r(e,...t)})})}omit(e,t){return t.forEach(t=>{Object.prototype.hasOwnProperty.call(e,t)&&delete e[t]}),e}render(){const{className:e,tag:t="div",hasTrack:n=!0,children:r,...a}=this.props;return Qt().createElement(t,{className:ne("splide",e),ref:this.splideRef,...this.omit(a,["options",...ua.map(e=>e[1])])},n?Qt().createElement(sa,null,r):r)}};const So=(0,Wt.createContext)({trackMarginStyle:{marginLeft:"0px",marginRight:"0px"},trackPaddingStyle:{paddingLeft:"0px",paddingRight:"0px"}}),Oo=({children:e,config:t,id:n,"aria-label":r,"aria-labelledby":a,slideRole:i,slideLabel:o="%s of %s",paginationLabel:l="",paginationRole:u,paginationButtonLabel:s,paginationButtonCurrent:c,hasGradientEffect:m={desktop:!0}})=>{const{mobile:d,tablet:p,desktop:f}=t,b=(0,Wt.useRef)(null),{width:E}=co(),[_,y]=(0,Wt.useState)({}),[g,v]=(0,Wt.useState)({marginLeft:"0px",marginRight:"0px"}),[N,A]=(0,Wt.useState)({paddingLeft:"0px",paddingRight:"0px"}),C=d?.isActive??!0,h=p?.isActive??!0,T=f?.isActive??!0;(0,Wt.useEffect)(()=>{const{splide:e}=b.current||{},t=b.current?.splide?.index,n=b.current?.splide?.length||0,r="after:brui-absolute after:brui-h-full after:!brui-w-[90px] after:brui-top-0 after:-brui-right-16 sm:after:-brui-right-30 md:after:-brui-right-16 brui-carousel-right-gradient before:brui-absolute before:!brui-h-full before:!brui-top-0 before:-brui-left-16 sm:before:-brui-left-30 md:before:-brui-left-16 brui-carousel-left-gradient before:brui-z-1".split(" ");e&&(window.innerWidth<768?(y(0===t?{left:"16px",right:"32px"}:t===n-1?{left:"32px",right:"16px"}:{left:"24px",right:"24px"}),v({marginLeft:"-16px",marginRight:"-16px"}),m?.mobile?r.map(t=>{e.root?.classList.add(t)}):r.map(t=>{e.root?.classList.remove(t)})):window.innerWidth>=768&&window.innerWidth<992?(y(0===t?{left:"32px",right:"42px"}:t===n-2?{left:"42px",right:"32px"}:{left:"32px",right:"32px"}),v({marginLeft:"-32px",marginRight:"-32px"}),m?.tablet?r.map(t=>{e.root?.classList.add(t)}):r.map(t=>{e.root?.classList.remove(t)})):(y({left:"0px",right:"0px"}),v({marginLeft:"0px",marginRight:"0px"}),m?.desktop?r.map(t=>{e.root?.classList.add(t)}):r.map(t=>{e.root?.classList.remove(t)})))},[E]),(0,Wt.useEffect)(()=>{const e=b.current?.splide;return e&&(I(),e.on("move",()=>{I(),setTimeout(()=>{R()},1)})),()=>{e&&e.off("move")}},[E]),(0,Wt.useEffect)(()=>{const e=b.current?.splide;if(e&&void 0!==i&&""!==i&&(()=>{const t=e.Components?.Elements?.slides;t&&t.forEach(e=>{e.setAttribute("role",i)})})(),e){const t=()=>{if(null!=u&&""!==u){const t=e.Components?.Elements?.pagination;t?.setAttribute("role",u)}},n=()=>{t(),R()};e.on("pagination:mounted",n)}return()=>{e&&e.off("pagination:mounted")}},[E,b]);const I=()=>{const e=b.current?.splide?.index,t=b.current?.splide?.length||0;window.innerWidth<768&&C?A(0===e?{paddingLeft:"16px",paddingRight:"32px"}:e===t-1?{paddingLeft:"32px",paddingRight:"16px"}:{paddingLeft:"24px",paddingRight:"24px"}):window.innerWidth>=768&&window.innerWidth<992&&h?A(0===e?{paddingLeft:"32px",paddingRight:"42px"}:e===t-2?{paddingLeft:"42px",paddingRight:"32px"}:{paddingLeft:"32px",paddingRight:"32px"}):window.innerWidth>=992&&T&&A({paddingLeft:"0px",paddingRight:"0px"})},R=()=>{const e=b.current?.splide;if(e){const t=e.Components?.Elements?.pagination,n=t?.querySelectorAll(".splide__pagination__page");n&&n.forEach((e,t)=>{if(e.removeAttribute("role"),e.removeAttribute("tabindex"),e.removeAttribute("aria-controls"),e.removeAttribute("aria-selected"),e.classList.contains("is-active")&&c&&s){const r=s.replace("{current}",`${t+1}`).replace("{total}",`${n.length}`);e.setAttribute("aria-label",`${r} ${c}`)}else s&&e.setAttribute("aria-label",s.replace("{current}",`${t+1}`).replace("{total}",`${n.length}`))})}};return Qt().createElement(So.Provider,{value:{trackMarginStyle:g,trackPaddingStyle:N}},Qt().createElement(ca,{id:`${n}-parent`,ref:b,className:"brui-h-full","aria-label":r,"aria-labelledby":a,hasTrack:!1,options:{i18n:{slideLabel:o,select:l},padding:_,mediaQuery:"min",destroy:!1,focusableNodes:"button, input",paginationKeyboard:!1,classes:{arrows:"splide__arrows",arrow:"splide__arrow",prev:"splide__arrow--prev !brui-h-40 !brui-w-40 !brui-bg-white !brui-border-solid !brui-border-1 !brui-border-gray-8 !brui-text-blue !brui-border-rounded-60 hover:!brui-border-blue-1 !-brui-left-10 focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3 !brui-opacity-100 brui-shadow-6sm bi_brui bi_chevron_left",next:"splide__arrow--next !brui-h-40 !brui-w-40 !brui-bg-white !brui-border-solid !brui-border-1 !brui-border-gray-8 !brui-text-blue !brui-border-rounded-60 hover:!brui-border-blue-1 !-brui-right-10 focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3 !brui-opacity-100 brui-shadow-6sm bi_brui bi_chevron",pagination:"splide__pagination",page:"splide__pagination__page !brui-bg-white !brui-block !brui-border-solid !brui-border-2 !brui-border-gray-10 focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3 !brui-opacity-100"},breakpoints:{320:{perPage:d?.perPage,perMove:d?.perMove,arrows:d?.arrows,gap:d?.gap,destroy:!C},768:{perPage:p?.perPage,perMove:p?.perMove,arrows:p?.arrows,gap:p?.gap,destroy:!h},992:{perPage:f?.perPage,perMove:f?.perMove,arrows:f?.arrows,gap:f?.gap,destroy:!T}}},onMoved:()=>{m&&(()=>{const{splide:e}=b.current||{},t=b.current?.splide?.index||0,n=b.current?.splide?.length||1,r=b.current?.splide?.options.perPage||1;e&&(e.root.classList.add("before:!brui-w-[90px]","after:!brui-w-[90px]"),0===t&&e.root.classList.remove("before:!brui-w-[90px]"),t+r-1>=n-1&&e.root.classList.remove("after:!brui-w-[90px]"))})()}},e))},Mo=()=>Qt().createElement("div",{className:"splide__arrows"}),Lo=({children:e,className:t})=>{const{trackMarginStyle:n,trackPaddingStyle:r}=(0,Wt.useContext)(So),a={...n,...r};return Qt().createElement("div",{className:["splide__track splide__track-custom",t].join(" ").trim(),style:a},Qt().createElement("div",{className:"splide__list"},e))},Do=({children:e})=>Qt().createElement("div",{className:"splide__slide"},e),Po=({className:e})=>Qt().createElement("ul",{className:["splide__pagination",e].join(" ").trim()}),Bo={default:"group-has-[:focus-visible]/inputcheckbox:brui-outline-blue group-has-[:focus-visible]/inputcheckbox:brui-outline group-has-[:focus-visible]/inputcheckbox:brui-outline-2 group-has-[:focus-visible]/inputcheckbox:brui-outline-offset-3",boxedInMobile:"group-has-[:focus-visible]/inputcheckbox:sm:brui-outline-blue group-has-[:focus-visible]/inputcheckbox:sm:brui-outline group-has-[:focus-visible]/inputcheckbox:sm:brui-outline-2 group-has-[:focus-visible]/inputcheckbox:sm:brui-outline-offset-3",alignMiddle:"group-has-[:focus-visible]/inputcheckbox:brui-outline-blue group-has-[:focus-visible]/inputcheckbox:brui-outline group-has-[:focus-visible]/inputcheckbox:brui-outline-2 group-has-[:focus-visible]/inputcheckbox:brui-outline-offset-3"},ko=(0,Wt.forwardRef)(function({id:e,name:t,value:n,hasError:r=!1,variant:a,...i},o){return Qt().createElement("div",{className:"brui-relative brui-group/inputcheckbox"},Qt().createElement("input",{type:"checkbox",id:e,name:t,value:n,className:"brui-absolute brui-size-full brui-opacity-0 enabled:brui-cursor-pointer disabled:brui-cursor-default brui-z-10",...i,ref:o}),Qt().createElement("div",null,Qt().createElement("div",{className:["brui-size-24 brui-rounded-2 brui-border group-has-[:disabled]/inputcheckbox:brui-bg-white group-has-[:disabled]/inputcheckbox:brui-border-gray-5 group-has-[:checked]/inputcheckbox:brui-bg-blue group-has-[:checked]/inputcheckbox:brui-border-blue group-has-[:checked]/label:brui-font-bold group-has-[:disabled]/inputcheckbox:brui-opacity-40",r?"brui-border-red":"brui-border-gray-2",Bo[a]].join(" ").trim()}),Qt().createElement(Eo,{className:"group-has-[:checked]/inputcheckbox:brui-block brui-absolute brui-hidden brui-text-white group-has-[:checked:disabled]/inputcheckbox:brui-text-gray-7 brui-text-12 brui-transform brui-top-1/2 brui-left-1/2 -brui-translate-x-1/2 -brui-translate-y-1/2 group-has-[:disabled]/inputcheckbox:brui-opacity-40",iconClass:"bi_brui",iconName:"bi_arrow_chekcbox"})))}),wo=({elementType:e,children:t,className:n,role:r,...a})=>{const i=e||"span";return Qt().createElement(i,{className:n,role:r,...a},t)},Uo=({id:e,iconName:t="bi_brui",iconClass:n="bi_error_bl_bg_cf",errorMessage:r,className:a="",show:i=!0})=>i?Qt().createElement("div",{className:["brui-flex brui-items-center brui-mt-10",a].join(" ").trim()},Qt().createElement(Eo,{iconClass:n,iconName:t,className:"brui-text-16 brui-text-red brui-mr-10"}),Qt().createElement(wo,{id:e,elementType:"div",className:"brui-box-border brui-text-red brui-text-12 brui-leading-14"},r)):null,Fo=(0,Wt.forwardRef)(function({id:e,name:t,value:n,children:r,variant:a,hasError:i=!1,"aria-describedby":o,errorMessage:l,boldLabelOnCheck:u,...s},c){const m={default:{wrapper:"brui-group/label brui-relative brui-inline-flex brui-w-full brui-items-start brui-content-start brui-border-0",label:"brui-pl-12 brui-text-14 brui-leading-19"},boxedInMobile:{wrapper:i?"brui-group/label brui-relative brui-inline-flex brui-w-full brui-border-2 brui-border-red brui-items-center brui-content-center brui-rounded-6 brui-border-1 sm:brui-border-0 has-[:checked]:sm:brui-border-0 has-[:checked]:brui-border-2 has-[:checked]:brui-border-blue brui-p-15 sm:brui-p-0":"brui-group/label brui-relative brui-inline-flex brui-w-full brui-items-center brui-content-center brui-rounded-6 brui-border-1 sm:brui-border-0 has-[:checked]:sm:brui-border-0 has-[:checked]:brui-border-2 has-[:checked]:brui-border-blue brui-p-15 sm:brui-p-0 brui-border-gray-3 has-[input:focus-visible]:brui-outline has-[input:focus-visible]:brui-outline-2 has-[input:focus-visible]:brui-outline-offset-3 has-[input:focus-visible]:brui-outline-blue has-[input:focus-visible]:sm:brui-outline-0",label:"brui-pl-12 brui-text-14 brui-leading-19"},alignMiddle:{wrapper:"brui-group/label brui-relative brui-inline-flex brui-w-full brui-items-start brui-content-start brui-border-0",label:"brui-pl-12 brui-text-14 brui-leading-19 brui-self-center"}},d=l&&i?`error-${e}`:"",p=(0,Wt.useMemo)(()=>o?[o||"",d].join(" ").trim():d,[o,d]);return Qt().createElement(Qt().Fragment,null,Qt().createElement("div",{className:[m[a].wrapper].join(" ").trim()},Qt().createElement(ko,{id:e,name:t,value:n,ref:c,hasError:i,"aria-describedby":p||"",variant:a,...s}),Qt().createElement("label",{htmlFor:e,className:["brui-cursor-pointer group-has-[:disabled]/label:brui-cursor-default",m[a].label,u?"group-has-[:checked]/label:brui-font-bold":""].join(" ").trim()},r)),i&&Qt().createElement(Uo,{id:d,iconClass:"vi_warning_c",iconName:"vi_vrui",errorMessage:l||""}))}),Ho=Fo,Yo={topRight:"brui-right-24 brui-top-24",topLeft:"sm:brui-top-30 sm:brui-left-30 brui-top-40 brui-left-15",topCenter:"brui-top-30 brui-left-1/2 -brui-translate-x-1/2",default:"brui-right-24 brui-top-24"},jo=(0,Wt.forwardRef)(function({checkboxPlacement:e="default",...t},n){return Qt().createElement("div",{className:"brui-group/inputcheckbox brui-absolute brui-right-0 brui-top-0 rui-leading-0 brui-w-full brui-h-full"},Qt().createElement("div",{className:["brui-shadow-4sm group-has-[:checked]/inputcheckbox:brui-shadow-none group-has-[:disabled]/inputcheckbox:brui-shadow-none brui-absolute brui-w-full brui-h-full group-has-[:disabled]/inputcheckbox:brui-border-gray-2 group-has-[:disabled]/inputcheckbox:brui-border-1 group-has-[:checked]/inputcheckbox:brui-border-2 brui-border group-has-[:focus-visible]/inputcheckbox:brui-outline-blue group-has-[:focus-visible]/inputcheckbox:brui-outline group-has-[:focus-visible]/inputcheckbox:brui-outline-2 group-has-[:focus-visible]/inputcheckbox:brui-outline-offset-3 brui-rounded-20 transition-all",t.disabled&&(t.checked||t.defaultChecked)?"":"group-has-[:checked]/inputcheckbox:brui-border-blue"].join(" ").trim()}),Qt().createElement("input",{type:"checkbox",ref:n,...t,className:"brui-absolute brui-left-0 brui-top-0 brui-w-full brui-h-full brui-z-10 brui-opacity-0"}),Qt().createElement("div",{className:["brui-absolute",Yo[e]].join(" ").trim()},Qt().createElement("div",{className:["brui-w-24 brui-h-24 brui-rounded-2 brui-border  ",t.disabled&&(t.checked||t.defaultChecked)?"brui-border-gray-7 group-has-[:disabled]/inputcheckbox:brui-bg-white":"brui-border-gray-2 group-has-[:checked]/inputcheckbox:brui-bg-blue group-has-[:checked]/inputcheckbox:brui-border-blue"].join(" ").trim()}),Qt().createElement(Eo,{className:["group-has-[:checked]/inputcheckbox:brui-block brui-absolute brui-hidden brui-text-12 brui-transform brui-top-1/2 brui-left-1/2 -brui-translate-x-1/2 -brui-translate-y-1/2",t.disabled&&(t.checked||t.defaultChecked)?"brui-text-gray-1":"brui-text-white"].join(" ").trim(),iconClass:"bi_brui",iconName:"bi_arrow_chekcbox"})))}),Go={topRight:"brui-py-32 brui-px-24",topLeft:"brui-p-16 sm:brui-p-32",topCenter:"brui-py-30 brui-px-15 sm:brui-px-30",default:"brui-py-32 brui-px-24"},Vo=(0,Wt.forwardRef)(function({children:e,className:t,"aria-labelledby":n,"aria-describedby":r,name:a,checkboxPlacement:i="topRight",defaultPadding:o,...l},u){return Qt().createElement("div",{className:["brui-group/checkboxcard brui-rounded-20 brui-relative",o?Go[i]:"",t,l.disabled&&(l.checked||l.defaultChecked)?"brui-bg-gray-3":""].join(" ").trim()},Qt().createElement(jo,{"aria-labelledby":n,"aria-describedby":r,name:a,ref:u,checkboxPlacement:i,...l}),e)}),zo=Vo,qo=({children:e,className:t,...n})=>Qt().createElement("div",{className:["brui-mb-24",t].join(" ").trim(),...n},e),Ko=({children:e,className:t,...n})=>Qt().createElement("div",{className:["brui-mt-auto",t].join(" ").trim(),...n},e),Wo=({children:e,className:t,...n})=>Qt().createElement("div",{className:["brui-px-16 sm:brui-px-30 md:brui-px-16 lg:brui-container",t].join(" ").trim(),...n},e),Xo=({direction:e="horizontal",width:t=2,className:n,style:r,...a})=>Qt().createElement("div",{className:["brui-bg-gray-1","horizontal"===e?"brui-w-full":"brui-flex",n].join(" ").trim(),style:{..."horizontal"===e?{height:t}:{width:t},...r||{}},...a}),Qo=(0,Wt.createContext)({visibility:!1}),Zo=({children:e})=>{const[t,n]=(0,Wt.useState)(!1),r=(0,Wt.useRef)(null),a=fo(0),i=()=>{if(r.current){const e=r.current.getBoundingClientRect();n(e.top>=0&&e.bottom<=window.innerHeight)}};return(0,Wt.useEffect)(()=>{i()},[a]),(0,Wt.useEffect)(()=>(window.addEventListener("scroll",i),()=>window.removeEventListener("scroll",i)),[]),Qt().createElement(Qo.Provider,{value:{visibility:!t}},Qt().createElement("div",{ref:r},e))},$o=({children:e,className:t,...n})=>{const{visibility:r}=(0,Wt.useContext)(Qo);return Qt().createElement("div",{className:["brui-fixed brui-left-0 brui-right-0 brui-bottom-0 brui-z-50 "+(r?"":"brui-hidden"),t].join(" ").trim(),...n},e)},Jo=({children:e,className:t,...n})=>Qt().createElement("div",{className:t,...n},e),el=({children:e,...t})=>Qt().createElement("li",{...t},e),tl={simple:{divClass:"brui-mb-32 sm:brui-mb-0 brui-text-center sm:brui-text-left",navClass:"brui-mb-16 sm:brui-mb-8 brui-text-12 brui-leading-14"},default:{divClass:"brui-text-center sm:brui-text-left",navClass:"brui-text-12 brui-leading-14"}},nl=({ariaLabel:e,children:t,copyRight:n,variant:r})=>Qt().createElement("div",{className:tl[r].divClass},Qt().createElement("nav",{className:tl[r].navClass,"aria-label":e},Qt().createElement("ul",{className:"sm:-brui-mx-8 sm:brui-inline-flex"},Qt().Children.map(t,(e,t)=>Qt().createElement(el,{className:"brui-mb-5 last-of-type:brui-mb-0 sm:brui-mb-0 sm:after:brui-inline-block sm:after:brui-align-middle sm:after:brui-h-12 sm:after:brui-w-px sm:after:brui-bg-gray-4 sm:after:last-of-type:brui-w-0",key:t},e)))),n&&Qt().createElement(wo,{elementType:"div",className:"brui-text-gray-4 brui-text-12 brui-leading-14"},n)),rl={default:"brui-flex-col"},al=({variant:e="default",className:t,children:n,...r})=>Qt().createElement("div",{className:[rl[e],"brui-flex brui-box-border brui-w-full",t].join(" ").trim(),...r},n),il=(0,Wt.createContext)(void 0),ol=({children:e,hasError:t,errorMessage:n,className:r})=>{const a=(0,Wt.useId)();return Qt().createElement(il.Provider,{value:{formGroupHasError:t,formGroupErrorMessage:n,inputErrorId:a}},Qt().createElement("div",{className:r},e),t&&void 0!==n&&""!==n&&Qt().createElement(Uo,{id:a,iconClass:"bi_error_bl_bg_cf",iconName:"bi_brui",errorMessage:n||""}))},ll={xs:"brui-text-18 brui-leading-20",sm:"brui-text-20 brui-leading-22 md:brui-text-22 md:brui-leading-28",md:"brui-text-22 brui-leading-24 sm:brui-text-24 sm:brui-leading-26 md:brui-text-24 lg:brui-text-24 md:brui-leading-[31px]",lg:"brui-text-26 brui-leading-28 -brui-tracking-0.3 sm:brui-text-32 sm:brui-leading-36 sm:-brui-tracking-0.6 md:brui-text-32 md:brui-leading-38",xl:"brui-text-30 brui-leading-36 -brui-tracking-0.4 sm:brui-text-34 sm:brui-leading-40 sm:-brui-tracking-0.6 md:brui-text-40 md:brui-leading-46",default:""},ul=({level:e,variant:t,children:n,className:r,...a})=>Qt().createElement(Qt().Fragment,null,Qt().createElement(e,{className:[ll[t],r,"brui-font-bellslim-black"].join(" "),...a},n)),sl=({title:e,subtitle:t,status:n,hideSubtitle:r,children:a,className:i,editButton:o,variant:l="default",disableSrOnlyText:u,autoScrollActiveStep:s=!0,...c})=>{const m="active"===n?{containerStyles:{default:"brui-flex brui-flex-col brui-pt-32 brui-pb-48",leftAlign:"brui-flex brui-flex-col brui-pt-40 brui-pb-24 sm:brui-py-48",leftAlignNoStep:"brui-flex brui-flex-col sm:brui-pt-45 brui-pt-45"},headingTitle:{default:["brui-text-center brui-text-darkblue",i].join(" ").trim(),leftAlign:"",leftAlignNoStep:""},headingSubtitle:{default:"brui-text-14 brui-text-blue",leftAlign:"brui-text-14 brui-text-blue",leftAlignNoStep:"brui-text-14 brui-text-blue"},headingContainer:{default:"brui-flex brui-justify-center",leftAlign:"brui-flex brui-justify-start",leftAlignNoStep:"brui-flex brui-justify-start"}}:"inactive"===n?{containerStyles:{default:"brui-flex brui-flex-col brui-pt-32 brui-pb-48",leftAlign:"brui-flex brui-flex-col brui-py-24 sm:brui-py-48",leftAlignNoStep:"brui-flex brui-flex-col brui-py-45",leftAlignHeading:"brui-flex brui-flex-col"},headingTitle:{default:["brui-text-center brui-text-gray-2",i].join(" ").trim(),leftAlign:"brui-text-gray-7 !brui-text-22 !brui-leading-24",leftAlignNoStep:"brui-text-gray-2 sm:brui-text-32 sm:brui-text-26",leftAlignHeading:"brui-text-gray-2 sm:brui-text-24 sm:brui-text-24 md:brui-text-24 lg:brui-text-24"},headingSubtitle:{default:"brui-text-14 brui-text-gray-2",leftAlign:"brui-text-14 brui-text-gray-7",leftAlignNoStep:"brui-text-14 brui-text-gray-2",leftAlignHeading:"brui-text-14 brui-text-gray-2"},headingContainer:{default:"brui-flex brui-justify-center",leftAlign:"",leftAlignNoStep:""}}:"complete"===n?{containerStyles:{default:"brui-flex brui-flex-col brui-pt-32 brui-pb-48",leftAlign:"brui-flex brui-flex-col brui-py-24 sm:brui-pt-48",leftAlignNoStep:"brui-flex brui-flex-col brui-pt-45 sm:brui-pt-45"},headingTitle:{default:["brui-text-darkblue !brui-text-20 !brui-leading-22 md:!brui-text-22 md:!brui-leading-28",i].join(" ").trim(),leftAlign:"brui-text-gray-2 !brui-text-22 !brui-leading-24",leftAlignNoStep:["brui-text-gray-2 sm:!brui-text-24 !brui-text-26 !brui-leading-28 sm:!brui-leading-26 !brui-tracking-[-0.4px]",i].join(" ").trim()},headingSubtitle:{default:"brui-text-14 brui-text-gray-4",leftAlign:"brui-text-14 brui-text-gray-2",leftAlignNoStep:"brui-text-14 brui-text-gray-2"},headingContainer:{default:"brui-flex brui-justify-between brui-gap-x-10 md:brui-mx-80",leftAlign:"brui-flex brui-justify-between brui-gap-x-10",leftAlignNoStep:"brui-flex brui-justify-between brui-gap-x-10"}}:{containerStyles:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""},headingTitle:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""},headingSubtitle:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""},headingContainer:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""}},d=(0,Wt.useRef)(null);return(0,Wt.useEffect)(()=>{"active"===n&&s&&d.current?.scrollIntoView({behavior:"smooth"})},[n]),Qt().createElement("div",{ref:d,className:m.containerStyles[l]},Qt().createElement("div",{className:m.headingContainer[l]},Qt().createElement(ul,{level:"h2",variant:"lg",className:m.headingTitle[l],...c},Qt().createElement("span",{className:"brui-flex brui-flex-col"},r?null:Qt().createElement(wo,{elementType:"span",className:m.headingSubtitle[l]},t," "),e,u?null:Qt().createElement(Ao,null," ",{active:"(Current Step)",inactive:"(Disabled: Please click the next step button above to proceed)",complete:"(Complete Step)"}[n]))),"complete"===n?o:null),"active"===n||"complete"===n?a:null)},cl={solidBlue:"brui-bg-blue-1 brui-text-white brui-border-blue-1 brui-border-2 brui-border-solid brui-rounded-4 hover:brui-bg-blue hover:brui-border-blue focus:brui-border-blue focus:brui-bg-blue focus:brui-outline focus:brui-outline-blue-2 focus:brui-outline-2 focus:brui-outline-offset-2",underline:"",boldLink:"brui-text-blue focus:brui-outline-blue-2 brui-font-bold hover:brui-underline focus:brui-underline brui-inline-block hover:brui-text-blue-1 focus:brui-text-blue-1 !brui-p-5",outlinedBlue:"",solidRed:"brui-font-sans brui-rounded-4 brui-bg-red brui-text-white brui-border-red brui-border-2 brui-border-solid hover:brui-bg-red-1 hover:brui-border-red-1 focus:brui-bg-red-1 focus:brui-border-red-1 focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 brui-inline-block",outlinedBlack:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-darkblue  brui-border-darkblue brui-border-2 brui-border-solid hover:brui-bg-transparent-1 focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 brui-inline-block",solidWhite:"brui-font-sans brui-rounded-4 brui-bg-white brui-text-darkblue brui-border-white brui-border-2 brui-border-solid hover:brui-bg-pink hover:brui-border-pink focus:brui-bg-pink focus:brui-border-pink focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 brui-inline-block",outlinedWhite:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-white  brui-border-white brui-border-2 brui-border-solid hover:brui-bg-transparent-1 focus:brui-bg-transparent-1 focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 brui-inline-block",textRed:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-red brui-underline brui-underline-offset-2 hover:brui-text-red-1 hover:brui-no-underline focus:brui-text-red-1 focus:brui-no-underline focus:brui-outline-blue-2 focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0",textBlue:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-blue brui-underline brui-underline-offset-2 hover:brui-text-blue-1 hover:brui-no-underline focus:brui-text-blue-1 focus:brui-no-underline focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0",textWhite:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-white brui-underline brui-underline-offset-2 hover:brui-text-pink hover:brui-no-underline focus:brui-text-pink focus:brui-no-underline focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0",default:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-blue brui-underline brui-underline-offset-2 hover:brui-text-blue-1 hover:brui-no-underline focus:brui-text-blue-1 focus:brui-no-underline focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0"},ml={regular:"brui-text-16 brui-py-8 brui-px-30 brui-leading-17",small:"brui-text-14 brui-py-5 brui-px-14 brui-leading-17",default:""},dl=(0,Wt.forwardRef)(function({variant:e="default",size:t="regular",className:n,href:r,...a},i){const o={linkStyle:[ml[t],cl[e],n].join(" ").trim()};return Qt().createElement("a",{className:o.linkStyle,href:r,ref:i,...a})}),pl=dl,fl=({icon:e,text:t,className:n,position:r,href:a,...i})=>{const o=(0,Wt.useMemo)(()=>"right"===r?Qt().createElement(Qt().Fragment,null,Qt().createElement(wo,{elementType:"span",className:"group-hover:brui-underline brui-mr-8"},t),e):Qt().createElement(Qt().Fragment,null,e,Qt().createElement(wo,{elementType:"span",className:"group-hover:brui-underline brui-ml-8"},t)),[r,e,t]);return Qt().createElement(pl,{className:["brui-group brui-no-underline",n,"right"===r?"brui-inline-block":"brui-inline-flex brui-items-center"].join(" ").trim(),href:a,...i},o)},bl={default:"brui-bg-white"},El={defaultInput:"brui-font-normal brui-text-gray-9 brui-rounded brui-border-solid brui-border-2 brui-box-border placeholder:brui-text-gray brui-text-14 brui-leading-18 brui-h-44 brui-w-full brui-py-13 brui-px-10 hover:brui-border-gray focus:brui-border-gray focus:brui-outline-2 focus:brui-outline-blue-2 focus:brui-outline-offset-4 disabled:brui-border-gray-2 disabled:brui-text-gray-2"},_l=(0,Wt.forwardRef)(function({id:e,variant:t="default",errorMessage:n,className:r,"aria-describedby":a,iconClass:i="bi_error_bl_bg_cf",iconName:o="bi_brui",isError:l,errorMessageClassName:u="",...s},c){const m=n&&l?`error-${e}`:"",d=(0,Wt.useMemo)(()=>a?[a||"",m].join(" ").trim():m,[a,m]);return Qt().createElement("div",{className:"brui-flex brui-flex-col"},Qt().createElement("div",{className:"brui-flex brui-items-center"},Qt().createElement("input",{className:[El.defaultInput,bl[t],l?"invalid:brui-border-red brui-border-red":"brui-border-gray-7",r].join(" ").trim(),type:"text","aria-describedby":d||"",ref:c,id:e,...s})),l&&n&&Qt().createElement(Uo,{id:m,iconClass:i,iconName:o,errorMessage:n||"",className:u}))}),yl={defaultLabel:"disabled:brui-text-gray-2 brui-font-semibold brui-text-14 brui-leading-18"},gl=({children:e,required:t,className:n,isError:r,htmlFor:a,overrideClassNames:i,...o})=>{const l=Qt().createElement("span",{className:["brui-mr-8 brui-text-black"].join(" ").trim(),"aria-hidden":"true"},"*");return Qt().createElement(Qt().Fragment,null,Qt().createElement("label",{htmlFor:a,className:i||[yl.defaultLabel,r?"brui-text-red":"brui-text-darkblue",n].join(" ").trim(),...o},t&&l,e))};!function(e){e.ENTER="Enter",e.ESCAPE="Escape",e.SPACE="Space"}(ma||(ma={})),da="data-focus-lock",pa="data-focus-lock-disabled",fa="undefined"!=typeof window?Wt.useLayoutEffect:Wt.useEffect,ba=new WeakMap,Ea={width:"1px",height:"0px",padding:0,overflow:"hidden",position:"fixed",top:"1px",left:"1px"},_a=function(){return _a=Object.assign||function(e){var t,n,r,a;for(n=1,r=arguments.length;n<r;n++)for(a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},_a.apply(this,arguments)},Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError,ya=ce({},function(e){return{target:e.target,currentTarget:e.currentTarget}}),ga=ce(),va=ce(),Na=function(e){void 0===e&&(e={});var t=se(null);return t.options=_a({async:!0,ssr:!1},e),t}({async:!0,ssr:"undefined"!=typeof document}),Aa=(0,Wt.createContext)(void 0),Ca=[],ha=(0,Wt.forwardRef)(function(e,t){var n,r,a,i,o,l=(0,Wt.useState)(),u=l[0],s=l[1],c=(0,Wt.useRef)(),m=(0,Wt.useRef)(!1),d=(0,Wt.useRef)(null),p=(0,Wt.useState)({})[1],f=e.children,b=e.disabled,E=void 0!==b&&b,_=e.noFocusGuards,y=void 0!==_&&_,g=e.persistentFocus,v=void 0!==g&&g,N=e.crossFrame,A=void 0===N||N,C=e.autoFocus,h=void 0===C||C,T=(e.allowTextSelection,e.group),I=e.className,R=e.whiteList,x=e.hasPositiveIndices,S=e.shards,O=void 0===S?Ca:S,M=e.as,L=void 0===M?"div":M,D=e.lockProps,P=void 0===D?{}:D,B=e.sideCar,k=e.returnFocus,w=void 0!==k&&k,U=e.focusOptions,F=e.onActivation,H=e.onDeactivation,Y=(0,Wt.useState)({})[0],j=(0,Wt.useCallback)(function(e){var t,n,r=e.captureFocusRestore;d.current||(n=null==(t=document)?void 0:t.activeElement,d.current=n,n!==document.body&&(d.current=r(n))),c.current&&F&&F(c.current),m.current=!0,p()},[F]),G=(0,Wt.useCallback)(function(){m.current=!1,H&&H(c.current),p()},[H]),V=(0,Wt.useCallback)(function(e){var t,n,r,a=d.current;a&&(t=("function"==typeof a?a():a)||document.body,(n="function"==typeof w?w(t):w)&&(r="object"==typeof n?n:void 0,d.current=null,e?Promise.resolve().then(function(){return t.focus(r)}):t.focus(r)))},[w]),z=(0,Wt.useCallback)(function(e){m.current&&ya.useMedium(e)},[]),q=ga.useMedium,K=(0,Wt.useCallback)(function(e){c.current!==e&&(c.current=e,s(e))},[]),W=oe(((n={})[pa]=E&&"disabled",n[da]=T,n),P),X=!0!==y,Q=X&&"tail"!==y,Z=(r=[t,K],a=function(e){return r.forEach(function(t){return le(t,e)})},(i=(0,Wt.useState)(function(){return{value:null,callback:a,facade:{get current(){return i.value},set current(e){var t=i.value;t!==e&&(i.value=e,i.callback(e,t))}}}})[0]).callback=a,o=i.facade,fa(function(){var e,t,n,a=ba.get(o);a&&(e=new Set(a),t=new Set(r),n=o.current,e.forEach(function(e){t.has(e)||le(e,null)}),t.forEach(function(t){e.has(t)||le(t,n)})),ba.set(o,r)},[r]),o),$=(0,Wt.useMemo)(function(){return{observed:c,shards:O,enabled:!E,active:m.current}},[E,m.current,O,u]);return Qt().createElement(Wt.Fragment,null,X&&[Qt().createElement("div",{key:"guard-first","data-focus-guard":!0,tabIndex:E?-1:0,style:Ea}),x?Qt().createElement("div",{key:"guard-nearest","data-focus-guard":!0,tabIndex:E?-1:1,style:Ea}):null],!E&&Qt().createElement(B,{id:Y,sideCar:Na,observed:u,disabled:E,persistentFocus:v,crossFrame:A,autoFocus:h,whiteList:R,shards:O,onActivation:j,onDeactivation:G,returnFocus:V,focusOptions:U,noFocusGuards:y}),Qt().createElement(L,oe({ref:Z},W,{className:I,onBlur:q,onFocus:z}),Qt().createElement(Aa.Provider,{value:$},f)),Q&&Qt().createElement("div",{"data-focus-guard":!0,tabIndex:E?-1:0,style:Ea}))}),ha.propTypes={};const vl=ha;Ta=function(e){for(var t=Array(e.length),n=0;n<e.length;++n)t[n]=e[n];return t},Ia=function(e){return Array.isArray(e)?e:[e]},Ra=function(e){return Array.isArray(e)?e[0]:e},xa=function(e){return e.parentNode&&e.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE?e.parentNode.host:e.parentNode},Sa=function(e){return e===document||e&&e.nodeType===Node.DOCUMENT_NODE},Oa=function(e,t){var n,r=e.get(t);return void 0!==r?r:(n=function(e,t){return!e||Sa(e)||!function(e){if(e.nodeType!==Node.ELEMENT_NODE)return!1;var t=window.getComputedStyle(e,null);return!(!t||!t.getPropertyValue||"none"!==t.getPropertyValue("display")&&"hidden"!==t.getPropertyValue("visibility"))}(e)&&!function(e){return e.hasAttribute("inert")}(e)&&t(xa(e))}(t,Oa.bind(void 0,e)),e.set(t,n),n)},Ma=function(e,t){var n,r=e.get(t);return void 0!==r?r:(n=function(e,t){return!(e&&!Sa(e))||!!Ba(e)&&t(xa(e))}(t,Ma.bind(void 0,e)),e.set(t,n),n)},La=function(e){return e.dataset},Da=function(e){return"INPUT"===e.tagName},Pa=function(e){return Da(e)&&"radio"===e.type},Ba=function(e){var t=e.getAttribute("data-no-autofocus");return![!0,"true",""].includes(t)},ka=function(e){var t;return Boolean(e&&(null===(t=La(e))||void 0===t?void 0:t.focusGuard))},wa=function(e){return!ka(e)},Ua=function(e){return Boolean(e)},Fa=function(e,t){var n=Math.max(0,e.tabIndex),r=Math.max(0,t.tabIndex),a=n-r,i=e.index-t.index;if(a){if(!n)return 1;if(!r)return-1}return a||i},Ha=function(e,t,n){return Ta(e).map(function(e,t){var r=function(e){return e.tabIndex<0&&!e.hasAttribute("tabindex")?0:e.tabIndex}(e);return{node:e,index:t,tabIndex:n&&-1===r?(e.dataset||{}).focusGuard?0:-1:r}}).filter(function(e){return!t||e.tabIndex>=0}).sort(Fa)},Ya=["button:enabled","select:enabled","textarea:enabled","input:enabled","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]","[tabindex]","[contenteditable]","[autofocus]"].join(","),ja="".concat(Ya,", [data-focus-guard]"),Ga=function(e,t){return Ta((e.shadowRoot||e).children).reduce(function(e,n){return e.concat(n.matches(t?ja:Ya)?[n]:[],Ga(n))},[])},Va=function(e,t){return e.reduce(function(e,n){var r,a=Ga(n,t),i=(r=[]).concat.apply(r,a.map(function(e){return function(e,t){var n;return e instanceof HTMLIFrameElement&&(null===(n=e.contentDocument)||void 0===n?void 0:n.body)?Va([e.contentDocument.body],t):[e]}(e,t)}));return e.concat(i,n.parentNode?Ta(n.parentNode.querySelectorAll(Ya)).filter(function(e){return e===n}):[])},[])},za=function(e,t){return Ta(e).filter(function(e){return Oa(t,e)}).filter(function(e){return function(e){return!((Da(e)||function(e){return"BUTTON"===e.tagName}(e))&&("hidden"===e.type||e.disabled))}(e)})},qa=function(e,t){return void 0===t&&(t=new Map),Ta(e).filter(function(e){return Ma(t,e)})},Ka=function(e,t,n){return Ha(za(Va(e,n),t),!0,n)},Wa=function(e,t){return Ha(za(Va(e),t),!1)},Xa=function(e,t){return e.shadowRoot?Xa(e.shadowRoot,t):!(void 0===Object.getPrototypeOf(e).contains||!Object.getPrototypeOf(e).contains.call(e,t))||Ta(e.children).some(function(e){var n,r;return e instanceof HTMLIFrameElement?!!(r=null===(n=e.contentDocument)||void 0===n?void 0:n.body)&&Xa(r,t):Xa(e,t)})},Qa=function(e){if(void 0===e&&(e=document),e&&e.activeElement){var t=e.activeElement;return t.shadowRoot?Qa(t.shadowRoot):t instanceof HTMLIFrameElement&&function(){try{return t.contentWindow.document}catch(e){return}}()?Qa(t.contentWindow.document):t}},Za=function(e){return e.parentNode?Za(e.parentNode):e},$a=function(e){return Ia(e).filter(Boolean).reduce(function(e,t){var n=t.getAttribute(da);return e.push.apply(e,n?function(e){var t,n,r,a,i;for(t=new Set,n=e.length,r=0;r<n;r+=1)for(a=r+1;a<n;a+=1)((i=e[r].compareDocumentPosition(e[a]))&Node.DOCUMENT_POSITION_CONTAINED_BY)>0&&t.add(a),(i&Node.DOCUMENT_POSITION_CONTAINS)>0&&t.add(r);return e.filter(function(e,n){return!t.has(n)})}(Ta(Za(t).querySelectorAll("[".concat(da,'="').concat(n,'"]:not([').concat(pa,'="disabled"])')))):[t]),e},[])},Ja=function(e,t){return void 0===t&&(t=Qa(Ra(e).ownerDocument)),!(!t||t.dataset&&t.dataset.focusGuard)&&$a(e).some(function(e){return Xa(e,t)||function(e,t){return Boolean(Ta(e.querySelectorAll("iframe")).some(function(e){return function(e,t){return e===t}(e,t)}))}(e,t)})},ei=function(e,t){e&&("focus"in e&&e.focus(t),"contentWindow"in e&&e.contentWindow&&e.contentWindow.focus())},ti=function(e,t){return Pa(e)&&e.name?function(e,t){return t.filter(Pa).filter(function(t){return t.name===e.name}).filter(function(e){return e.checked})[0]||e}(e,t):e},ni=function(e){return e[0]&&e.length>1?ti(e[0],e):e[0]},ri=function(e,t){return e.indexOf(ti(t,e))},ai="NEW_FOCUS",ii=function(e,t,n){var r,a=e.map(function(e){return e.node}),i=qa(a.filter((r=n,function(e){var t,n=null===(t=La(e))||void 0===t?void 0:t.autofocus;return e.autofocus||void 0!==n&&"false"!==n||r.indexOf(e)>=0})));return i&&i.length?ni(i):ni(qa(t))},oi=function(e,t){return void 0===t&&(t=[]),t.push(e),e.parentNode&&oi(e.parentNode.host||e.parentNode,t),t},li=function(e,t){var n,r,a,i;for(n=oi(e),r=oi(t),a=0;a<n.length;a+=1)if(i=n[a],r.indexOf(i)>=0)return i;return!1},ui=function(e,t,n){var r=Ia(e),a=Ia(t),i=r[0],o=!1;return a.filter(Boolean).forEach(function(e){o=li(o||e,e)||o,n.filter(Boolean).forEach(function(e){var t=li(i,e);t&&(o=!o||Xa(t,o)?t:li(t,o))})}),o},si=function(e,t){return e.reduce(function(e,n){return e.concat(function(e,t){return za((n=e.querySelectorAll("[".concat("data-autofocus-inside","]")),Ta(n).map(function(e){return Va([e])}).reduce(function(e,t){return e.concat(t)},[])),t);var n}(n,t))},[])},ci=function(e,t){var n,r,a,i,o,l,u,s,c,m=Qa(Ia(e).length>0?document:Ra(e).ownerDocument),d=$a(e).filter(wa),p=ui(m||e,e,d),f=new Map,b=Wa(d,f),E=b.filter(function(e){var t=e.node;return wa(t)});if(E[0])return i=Wa([p],f).map(function(e){return e.node}),n=i,r=E,a=new Map,r.forEach(function(e){return a.set(e.node,e)}),o=n.map(function(e){return a.get(e)}).filter(Ua),l=o.map(function(e){return e.node}),u=o.filter(function(e){return e.tabIndex>=0}).map(function(e){return e.node}),s=function(e,t,n,r,a){var i,o,l,u,s,c,m,d,p,f,b,E,_,y,g,v,N,A=e.length,C=e[0],h=e[A-1],T=ka(r);if(!(r&&e.indexOf(r)>=0))return i=void 0!==r?n.indexOf(r):-1,o=a?n.indexOf(a):i,l=a?e.indexOf(a):-1,-1===i?-1!==l?l:ai:-1===l?ai:(c=i-o,m=n.indexOf(C),d=n.indexOf(h),u=n,s=new Set,u.forEach(function(e){return s.add(ti(e,u))}),p=u.filter(function(e){return s.has(e)}),f=void 0!==r?p.indexOf(r):-1,b=a?p.indexOf(a):f,E=p.filter(function(e){return e.tabIndex>=0}),_=void 0!==r?E.indexOf(r):-1,y=a?E.indexOf(a):_,g=_>=0&&y>=0?y-_:b-f,!c&&l>=0||0===t.length?l:(v=ri(e,t[0]),N=ri(e,t[t.length-1]),i<=m&&T&&Math.abs(c)>1?N:i>=d&&T&&Math.abs(c)>1?v:c&&Math.abs(g)>1?l:i<=m?N:i>d?v:c?Math.abs(c)>1?l:(A+l+c)%A:void 0))}(l,u,i,m,t),s===ai?(c=ii(b,u,si(d,f))||ii(b,l,si(d,f)))?{node:c}:void 0:void 0===s?s:o[s]},mi=0,di=!1,pi=function(e,t,n){void 0===n&&(n={});var r=ci(e,t);if(!di&&r){if(mi>2)return di=!0,void setTimeout(function(){di=!1},1);mi++,ei(r.node,n.focusOptions),mi--}},fi=function(e){var t=function(e){if(!e)return null;for(var t=[],n=e;n&&n!==document.body;)t.push({current:pe(n),parent:pe(n.parentElement),left:pe(n.previousElementSibling),right:pe(n.nextElementSibling)}),n=n.parentElement;return{element:pe(e),stack:t,ownerDocument:e.ownerDocument}}(e);return function(){return function(e){var t,n,r,a,i,o,l,u,s,c,m,d,p,f,b,E,_,y,g,v,N;if(e)for(o=e.stack,l=e.ownerDocument,u=new Map,s=0,c=o;s<c.length;s++)if((d=null===(t=(m=c[s]).parent)||void 0===t?void 0:t.call(m))&&l.contains(d)){for(p=null===(n=m.left)||void 0===n?void 0:n.call(m),f=m.current(),b=d.contains(f)?f:void 0,E=null===(r=m.right)||void 0===r?void 0:r.call(m),_=Ka([d],u),y=null!==(i=null!==(a=null!=b?b:null==p?void 0:p.nextElementSibling)&&void 0!==a?a:E)&&void 0!==i?i:p;y;){for(g=0,v=_;g<v.length;g++)if(N=v[g],null==y?void 0:y.contains(N.node))return N.node;y=y.nextElementSibling}if(_.length)return _[0].node}}(t)}},bi=function(e,t,n){var r,a,i;void 0===t&&(t={}),r=function(e){return Object.assign({scope:document.body,cycle:!0,onlyTabbable:!0},e)}(t),a=function(e,t,n){var r,a,i;return e&&t?(r=Ia(t)).every(function(t){return!Xa(t,e)})?{}:(i=(a=n?Ka(r,new Map):Wa(r,new Map)).findIndex(function(t){return t.node===e}),-1!==i?{prev:a[i-1],next:a[i+1],first:a[0],last:a[a.length-1]}:void 0):{}}(e,r.scope,r.onlyTabbable),a&&(i=n(a,r.cycle))&&ei(i.node,r.focusOptions)},Ei=function(e,t,n){var r,a,i,o,l=(a=e,i=null===(r=t.onlyTabbable)||void 0===r||r,{first:(o=i?Ka(Ia(a),new Map):Wa(Ia(a),new Map))[0],last:o[o.length-1]})[n];l&&ei(l.node,t.focusOptions)},_i=function(e){return e&&"current"in e?e.current:e},yi=function(){return document&&document.activeElement===document.body},gi=null,vi=null,Ni=function(){return null},Ai=null,Ci=!1,hi=!1,Ti=function(){return!0},Ii=function e(t,n,r){return n&&(n.host===t&&(!n.activeElement||r.contains(n.activeElement))||n.parentNode&&e(t,n.parentNode,r))},Ri=function(e){return Wa(e,new Map)},xi=function(){var e,t,n,r,a,i,o,l,u,s,c,m,d,p,f,b,E,_,y,g,v,N,A=!1;return gi&&(u=(l=gi).observed,s=l.persistentFocus,c=l.autoFocus,m=l.shards,d=l.crossFrame,p=l.focusOptions,f=l.noFocusGuards,b=u||Ai&&Ai.portaledElement,!yi()||!vi||vi===document.body||document.body.contains(vi)&&Ri([(o=vi).parentNode]).some(function(e){return e.node===o})||(E=Ni())&&E.focus(),_=document&&document.activeElement,b&&(y=[b].concat(m.map(_i).filter(Boolean)),_&&!function(e){return(gi.whiteList||Ti)(e)}(_)||(s||function(){if(!(d?Boolean(Ci):"meanwhile"===Ci)||!f||!vi||hi)return!1;var e=Ri(y),t=e.findIndex(function(e){return e.node===vi});return 0===t||t===e.length-1}()||!(yi()||function(e){void 0===e&&(e=document);var t=Qa(e);return!!t&&Ta(e.querySelectorAll("[".concat("data-no-focus-lock","]"))).some(function(e){return Xa(e,t)})}())||!vi&&c)&&(b&&!(Ja(y)||_&&function(e,t){return t.some(function(t){return Ii(e,t,t)})}(_,y)||(i=_,Ai&&Ai.portaledElement===i))&&(document&&!vi&&_&&!c?(_.blur&&_.blur(),document.body.focus()):(A=pi(y,vi,{focusOptions:p}),Ai={})),(vi=document&&document.activeElement)!==document.body&&(Ni=fi(vi)),Ci=!1),document&&_!==document.activeElement&&document.querySelector("[data-focus-auto-guard]")&&(g=document&&document.activeElement,t=$a(e=y).filter(wa),n=ui(e,e,t),r=Ha(Va([n],!0),!0,!0),a=Va(t,!1),v=r.map(function(e){var t=e.node;return{node:t,index:e.index,lockItem:a.indexOf(t)>=0,guard:ka(t)}}),N=v.map(function(e){return e.node}).indexOf(g),N>-1&&(v.filter(function(e){var t=e.guard,n=e.node;return t&&n.dataset.focusAutoGuard}).forEach(function(e){return e.node.removeAttribute("tabIndex")}),be(N,v.length,1,v),be(N,-1,-1,v))))),A},Si=function(e){xi()&&e&&(e.stopPropagation(),e.preventDefault())},Oi=function(){return fe(xi)},Mi=function(){hi=!0},Li=function(){hi=!1,Ci="just",fe(function(){Ci="meanwhile"})},Di={moveFocusInside:pi,focusInside:Ja,focusNextElement:function(e,t){void 0===t&&(t={}),bi(e,t,function(e,t){var n=e.next,r=e.first;return n||t&&r})},focusPrevElement:function(e,t){void 0===t&&(t={}),bi(e,t,function(e,t){var n=e.prev,r=e.last;return n||t&&r})},focusFirstElement:function(e,t){void 0===t&&(t={}),Ei(e,t,"first")},focusLastElement:function(e,t){void 0===t&&(t={}),Ei(e,t,"last")},captureFocusRestore:fi},ya.assignSyncMedium(function(e){var t=e.target,n=e.currentTarget;n.contains(t)||(Ai={observerNode:n,portaledElement:t})}),ga.assignMedium(Oi),va.assignMedium(function(e){return e(Di)});const Nl=(Pi=function(e){return e.filter(function(e){return!e.disabled})},Bi=function(e){var t,n,r=e.slice(-1)[0];r&&!gi&&(document.addEventListener("focusin",Si),document.addEventListener("focusout",Oi),window.addEventListener("focus",Mi),window.addEventListener("blur",Li)),n=(t=gi)&&r&&r.id===t.id,gi=r,t&&!n&&(t.onDeactivation(),e.filter(function(e){return e.id===t.id}).length||t.returnFocus(!r)),r?(vi=null,n&&t.observed===r.observed||r.onActivation(Di),xi(),fe(xi)):(document.removeEventListener("focusin",Si),document.removeEventListener("focusout",Oi),window.removeEventListener("focus",Mi),window.removeEventListener("blur",Li),vi=null)},function(e){function t(){n=Pi(o.map(function(e){return e.props})),Bi(n)}var n,r,a,i,o=[],l=function(r){function a(){return r.apply(this,arguments)||this}var i,l,u=r;return(i=a).prototype=Object.create(u.prototype),i.prototype.constructor=i,me(i,u),a.peek=function(){return n},(l=a.prototype).componentDidMount=function(){o.push(this),t()},l.componentDidUpdate=function(){t()},l.componentWillUnmount=function(){var e=o.indexOf(this);o.splice(e,1),t()},l.render=function(){return Qt().createElement(e,this.props)},a}(Wt.PureComponent);return r=l,a="displayName",i="SideEffect("+function(e){return e.displayName||e.name||"Component"}(e)+")",(a=function(e){var t=function(e){var t,n;if("object"!=de(e)||!e)return e;if(void 0!==(t=e[Symbol.toPrimitive])){if("object"!=de(n=t.call(e,"string")))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==de(t)?t:t+""}(a))in r?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i,l})(function(){return null});ki=(0,Wt.forwardRef)(function(e,t){return Qt().createElement(vl,oe({sideCar:Nl,ref:t},e))}),(wi=vl.propTypes||{}).sideCar,function(e,t){var n,r;if(null==e)return{};for(r in n={},e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}}(wi,["sideCar"]),ki.propTypes={};const Al=ki;Ui=n(514);const Cl=({container:e=document.body,children:t})=>(0,Ui.createPortal)(t,e),hl=(0,Wt.createContext)({variant:"default",ariaLabelledby:""}),Tl=({isNestedModal:e=!1,variant:t="default","aria-labelledby":n,"aria-describedby":r,onOverlayClick:a,onEscapeKeyPressed:i,children:o,className:l,focusLockOptions:u={returnFocus:!0,lockProps:{tabIndex:-1}},hasBackdrop:s=!0,modalWidth:c={mobile:"100%",tablet:"645px",desktop:"645px"},...m})=>{const d=n||(0,Wt.useId)(),{width:p}=co(100),f=(0,Wt.useRef)(null);((e,t)=>{(0,Wt.useEffect)(()=>{if(t)return;const n=document.body.style.overflow;let r;document.body.style.overflow="hidden";const a=e.current;return a&&(r=nn(a)),()=>{document.body.style.overflow=n,r&&r()}},[t])})(f,e),so(e=>{e.stopPropagation(),e.key===ma.ESCAPE&&i?.(e)});const b=(0,Wt.useMemo)(()=>p<768?c.mobile:p>=768&&p<992?c.tablet:c.desktop,[p,c]);return Qt().createElement(hl.Provider,{value:{variant:t,ariaLabelledby:d,ariaDescribedBy:r}},Qt().createElement(Cl,null,Qt().createElement(Al,{as:"div",...u||{}},Qt().createElement("div",{className:["brui-bg-black-1 brui-transition-opacity brui-overflow-y-auto","brui-h-dvh brui-w-screen","brui-flex brui-items-start","brui-fixed brui-inset-0 brui-m-auto brui-z-[9998]",s?"brui-bg-opacity-50":"brui-bg-opacity-0",l||"brui-justify-center"].join(" ").trim(),onClick:a,role:"dialog","aria-modal":!0,"aria-labelledby":d,tabIndex:-1,...m},Qt().createElement("div",{className:["brui-relative brui-z-10","brui-w-full","brui-flex"].join(" ").trim(),ref:f,role:"document",style:{maxWidth:b}},o)))))},Il={default:"brui-modal-body brui-flex brui-flex-col brui-items-start brui-bg-white brui-gap-16"},Rl=({isDefaultPadding:e=!0,className:t,children:n,...r})=>{const{variant:a}=(0,Wt.useContext)(hl),i=(0,Wt.useMemo)(()=>({modalBodyStyle:[Il[a],e&&"brui-py-30 brui-px-15 sm:brui-px-30",t].join(" ").trim()}),[a]);return Qt().createElement("div",{className:i.modalBodyStyle,...r},n)},xl={default:["brui-min-w-full brui-inline-block brui-bg-white brui-text-left brui-overflow-hidden brui-shadow-xl brui-transform brui-transition-all","brui-flex brui-flex-col","brui-relative brui-opacity-0 sm:brui-align-middle"].join(" ").trim()},Sl={top:"brui-self-start",bottom:"brui-self-end",center:"brui-self-center",left:"",right:""},Ol={top:"brui-rounded-b-10 -brui-translate-y-[50px]",bottom:"brui-rounded-t-10 brui-translate-y-[50px]",center:"brui-rounded-10 -brui-translate-y-[50px]",centerRadius10:"brui-rounded-10 -brui-translate-y-[50px]",left:"brui-rounded-r-10 brui-h-full",right:"brui-rounded-l-10 brui-h-full"},Ml=({className:e,children:t,verticalAlignment:n={mobile:"bottom",tablet:"center",desktop:"center"},useDefaultRadius:r=!0})=>{const{variant:a}=(0,Wt.useContext)(hl),{width:i}=co(100),{verticalAlignmentBreakpoint:o,radiusBreakpoint:l}=((e,t,n,r)=>{const a=(0,Wt.useMemo)(()=>{let a,i;return e<768?(a=t[r.mobile],i=n[r.mobile]):e>=768&&e<992?(a=t[r.tablet],i=n[r.tablet]):(a=t[r.desktop],i=n[r.desktop]),{verticalAlignmentBreakpoint:a,radiusBreakpoint:i}},[e,t,n,r]);return a})(i,Sl,Ol,n),[u,s]=(0,Wt.useState)(!1),c=(0,Wt.useMemo)(()=>({modalContentStyle:[xl[a],u?"brui-transition-all brui-duration-300 brui-ease-out brui-delay-50 brui-opacity-100 brui-transform-none":"",o,r?l:"",e].join(" ").trim()}),[a,e,u]);return(0,Wt.useEffect)(()=>{s(!0)},[]),Qt().createElement("div",{className:"brui-flex brui-justify-center brui-w-full "+("center"===n.mobile?"brui-min-h-dvh brui-px-16":"brui-min-h-dvh")},Qt().createElement("div",{onClick:e=>e.stopPropagation(),className:c.modalContentStyle},t))},Ll={variant:{gray:"brui-bg-gray-5",transparent:"",lightGray:"brui-bg-gray-3"}},Dl=({variant:e="gray",children:t,className:n,isDefaultPadding:r=!0,...a})=>Qt().createElement("div",{className:["brui-flex sm:brui-gap-32","brui-justify-center sm:brui-justify-start brui-flex-col sm:brui-flex-row",r&&"brui-ps-24 brui-pe-16 brui-py-16 brui-gap-16 sm:brui-px-32 sm:brui-py-24",Ll.variant[e],n].join(" ").trim(),...a},t),Pl={variant:{grayBar:"brui-bg-gray-5",transparent:"",none:"",lightGrayBar:"brui-bg-gray-3"}},Bl=({variant:e="transparent",headerIcon:t,title:n,rightButtonIcon:r,onRightButtonClicked:a,rightButtonLabel:i="Close Modal",className:o,children:l,isDefaultPadding:u=!0})=>{const{ariaLabelledby:s}=(0,Wt.useContext)(hl);return Qt().createElement("div",{className:["brui-modal-header brui-flex brui-flex-col",Pl.variant[e],u&&"brui-py-25 brui-px-15 sm:brui-px-30",o].join(" ").trim()},Qt().createElement("div",{className:"brui-flex brui-justify-between brui-gap-16"},Qt().createElement("div",{className:["brui-flex brui-items-start  sm:brui-items-center brui-justify-center sm:brui-justify-start brui-gap-20 brui-flex-col sm:brui-flex-row"].join(" ").trim()},t||null,Qt().createElement(ul,{className:"brui-text-22 brui-leading-24 sm:brui-text-24 sm:brui-leading-26 brui-text-black brui-tracking-[-0.4px]",id:s||"modal=dialog-title",level:"h2",variant:"default"},n)),r?Qt().createElement("div",{className:"brui-pl-30"},Qt().createElement("button",{type:"button","aria-label":i,onClick:()=>a?.(),className:"brui-flex brui-rounded-2 brui-text-blue hover:brui-text-blue-1 focus:!brui-outline-blue focus:!brui-outline focus:brui-outline-2 focus:brui-outline-offset-3",id:`${s}-close-button`||"modal-dialog-close-button"},"default"===r?Qt().createElement(Eo,{className:"brui-text-20",iconClass:"bi_brui",iconName:"bi_close"}):null,Qt().createElement(Ao,null,i))):null),l)},kl={ordinaryPrice:{divClassName:null,spanClassName:null,forwardSlashClassName:null},defaultPrice:{divClassName:"brui-font-bold brui-text-18 brui-text-blue",spanClassName:null,forwardSlashClassName:null},bigPrice:{divClassName:"brui-font-bellslim-heavy brui-text-28 brui-leading-25 sm:brui-text-40 sm:brui-leading-40 -brui-tracking-1 brui-whitespace-nowrap",spanClassName:"brui-text-16 brui-leading-16 sm:brui-text-20 sm:brui-leading-30 -brui-tracking-0.45 brui-relative brui-top-0 sm:-brui-top-3 brui-align-top",forwardSlashClassName:"brui-text-[11px] brui-leading-16 sm:brui-text-[13px] sm:brui-leading-20 -brui-tracking-0.45 brui-relative brui-top-0 sm:brui-top-3 brui-align-top"},smallPrice:{divClassName:"brui-font-bellslim-heavy brui-text-28 brui-leading-25 -brui-tracking-1 brui-whitespace-nowrap",spanClassName:"brui-text-16 brui-leading-16 -brui-tracking-0.45 brui-relative brui-align-top",forwardSlashClassName:"brui-text-[11px] brui-leading-16 -brui-tracking-0.45 brui-relative brui-align-top"}},wl={strikeText:{en:"previous price",fr:"précédent Coût"},decimalPointText:{en:".",fr:","},perMonth:{visibleText:{en:"MO.",fr:"MOIS"},screenReaderText:{en:" per month",fr:" par mois"}},perDay:{visibleText:{en:"DAY",fr:"JOUR"},screenReaderText:{en:" per day",fr:" par jour"}}},Ul={CR:{en:"Credit",fr:"Crédit"},"-":{en:"Negative",fr:"Négatif"}},Fl=({price:e,variant:t="defaultPrice",strike:n,reverse:r,language:a="en",suffixText:i,className:o,negativeIndicator:l="CR",showZeroDecimalPart:u,srText:s})=>{if(!e&&0!==e)return null;const c=e<0,m=Math.abs(e),d=m.toFixed(2).toString().split("."),p=d[0];let f=d[1]||"";f=parseFloat(p)===m?"00":f,i&&"00"===f&&!u&&(f="");const b=(0,Wt.useMemo)(()=>{let e="",o="",u="";const c="fr"===a,m=n?"brui-line-through":"";let d,b,E;return s?u=`${s} `:null!==l&&(u=`${Ul[l][a]} `),"bigPrice"!==t&&"smallPrice"!==t||(e=c?"":"brui-mr-px",(f||i)&&(o=c?"":"brui-ml-[2px]")),d=r?"brui-text-white":n?"brui-text-gray":"ordinaryPrice"===t?"":"brui-text-darkblue",c?(b=Qt().createElement(Qt().Fragment,null,Qt().createElement("span",{className:m},p),f?Qt().createElement("span",{className:[kl[t].spanClassName,o,m].join(" ").trim()},wl.decimalPointText.fr+f):null,Qt().createElement("span",{className:[kl[t].spanClassName,e].join(" ").trim()}," $")),E=`${p}${f?wl.decimalPointText.fr+f:""} $`):(b=Qt().createElement(Qt().Fragment,null,Qt().createElement("span",{className:[kl[t].spanClassName,e].join(" ").trim()},"$"),Qt().createElement("span",{className:m},p),f?Qt().createElement("span",{className:[kl[t].spanClassName,o,m].join(" ").trim()},wl.decimalPointText.en+f):null),E=`$${p}${f?wl.decimalPointText.en+f:""}`),{dollarMargin:e,leftMargin:o,strikeClass:m,priceTextColor:d,priceMarkup:b,priceScreenReaderText:E,negativeText:u}},[p,t,f,i,r,n,a,s]);return Qt().createElement(Qt().Fragment,null,Qt().createElement("div",{className:[kl[t].divClassName,b.priceTextColor,o].join(" ").trim(),"aria-hidden":"true"},c&&`${l?.toString()} `,b.priceMarkup,i?Qt().createElement(Qt().Fragment,null,Qt().createElement("span",{className:[kl[t].forwardSlashClassName,b.leftMargin].join(" ").trim()},"/"),Qt().createElement("span",{className:[kl[t].spanClassName,b.leftMargin].join(" ").trim()},wl[i].visibleText[a])):null),n&&Qt().createElement("span",{className:"brui-sr-only"},wl.strikeText[a]," "),Qt().createElement("span",{className:"brui-sr-only"},c&&b.negativeText,b.priceScreenReaderText,i?wl[i].screenReaderText[a]:null))},Hl={default:"group-has-[:focus-visible]/inputradiobutton:brui-outline-blue group-has-[:focus-visible]/inputradiobutton:brui-outline group-has-[:focus-visible]/inputradiobutton:brui-outline-2 group-has-[:focus-visible]/inputradiobutton:brui-outline-offset-3",boxedInMobile:"group-has-[:focus-visible]/inputradiobutton:sm:brui-outline-blue group-has-[:focus-visible]/inputradiobutton:sm:brui-outline group-has-[:focus-visible]/inputradiobutton:sm:brui-outline-2 group-has-[:focus-visible]/inputradiobutton:sm:brui-outline-offset-3"},Yl=(0,Wt.forwardRef)(function({id:e,name:t,value:n,hasError:r=!1,variant:a,...i},o){return Qt().createElement("div",{className:"brui-relative brui-group/inputradiobutton"},Qt().createElement("input",{type:"radio",id:e,name:t,value:n,className:"brui-absolute brui-size-full brui-opacity-0 enabled:brui-cursor-pointer disabled:brui-cursor-default brui-z-10",...i,ref:o}),Qt().createElement("div",{className:"brui-relative"},Qt().createElement("div",{className:["brui-size-24 brui-rounded-12 brui-border group-has-[:disabled]/inputradiobutton:brui-opacity-40 group-has-[:checked]/inputradiobutton:brui-bg-blue-1  group-has-[:checked]/inputradiobutton:brui-border-blue-1",r?"brui-border-red":"brui-border-gray-2",Hl[a]].join(" ").trim()}),Qt().createElement("div",{className:["brui-scale-0 group-has-[:checked]/inputradiobutton:brui-scale-100 brui-absolute brui-w-12 brui-h-12 brui-bg-white brui-rounded-6 brui-top-1/2 brui-left-1/2 brui-transform -brui-translate-x-1/2 -brui-translate-y-1/2 brui-transition-transform group-has-[:disabled]/inputradiobutton:brui-opacity-40",r?"brui-bg-red":"brui-bg-blue"].join(" ").trim()})))}),jl=(0,Wt.forwardRef)(function({id:e,name:t,value:n,children:r,variant:a,hasError:i=!1,RadioButtonWrapperClass:o,...l},u){const s={default:{wrapper:"brui-group/label brui-relative brui-inline-flex brui-w-full brui-border-0",label:"brui-pl-12 enabled:brui-text-14 enabled:brui-leading-19 brui-cursor-pointer brui-self-center group-has-[:disabled]/label:brui-text-gray-7 group-has-[:disabled]/label:brui-cursor-default group-has-[:checked]/label:brui-font-bold group-has-[:checked:disabled]/label:brui-font-normal"},boxedInMobile:{wrapper:"brui-group/label brui-relative brui-inline-flex brui-w-full brui-rounded-4 brui-border-1 sm:brui-border-0 has-[:checked]:sm:brui-border-0 has-[:checked]:brui-border-2 brui-p-15 sm:brui-p-0 has-[input:focus-visible]:brui-outline has-[input:focus-visible]:brui-outline-2 has-[input:focus-visible]:brui-outline-offset-3 has-[input:focus-visible]:brui-outline-blue-2 has-[input:focus-visible]:sm:brui-outline-0 has-[:checked:disabled]:brui-border-[#BABEC2] brui-shadow-4sm sm:brui-shadow-none"+(i?" brui-border-red has-[:checked]:brui-border-red":" brui-border-[#d7d7d7] has-[:checked]:brui-border-blue"),label:"brui-pl-12 brui-text-14 brui-leading-19 enabled:brui-cursor-pointer group-has-[:checked]/label:brui-font-bold group-has-[:checked:disabled]/label:brui-font-normal brui-self-center group-has-[:disabled]/label:brui-text-gray-7 group-has-[:disabled]/label:brui-cursor-default group-has-[:checked:disabled]/label:brui-font-normal"}};return Qt().createElement(Qt().Fragment,null,Qt().createElement("div",{className:[s[a].wrapper,o].join(" ").trim()},Qt().createElement(Yl,{id:e,name:t,value:n,ref:u,hasError:i,variant:a,...l}),Qt().createElement("label",{htmlFor:e,className:[s[a].label].join(" ").trim()},r)))}),Gl=jl,Vl={topRight:"brui-right-24 brui-top-24",topLeft:"brui-top-16 brui-left-16 sm:brui-top-32 sm:brui-left-32",leftCenter:"brui-left-12 brui-transform -brui-translate-y-1/2 brui-top-1/2"},zl=(0,Wt.forwardRef)(function({radioPlacement:e="topRight",radioPlacementMobile:t,borderRadiusClassName:n,cursorPointer:r=!1,...a},i){const{width:o}=co(),l=(0,Wt.useMemo)(()=>t&&o<768?t:e,[o]);return Qt().createElement("div",{className:"brui-group/inputradio brui-absolute brui-right-0 brui-top-0 brui-leading-0 brui-w-full brui-h-full"},Qt().createElement("div",{className:["brui-absolute brui-w-full brui-h-full group-has-[:checked]/inputradio:brui-border-2 group-has-[:checked]/inputradio:brui-border-blue group-has-[:focus-visible]/inputradio:brui-outline-blue-2 group-has-[:focus-visible]/inputradio:brui-outline group-has-[:focus-visible]/inputradio:brui-outline-2 group-has-[:focus-visible]/inputradio:brui-outline-offset-3 transition-all brui-shadow-4sm group-has-[:checked]/inputradio:brui-shadow-none",n||"brui-rounded-20"].join(" ").trim()}),Qt().createElement("input",{type:"radio",className:"brui-absolute brui-left-0 brui-top-0 brui-w-full brui-h-full brui-z-10 brui-opacity-0 "+(r?"brui-cursor-pointer":""),ref:i,...a}),Qt().createElement("div",{className:["brui-absolute",Vl[l]].join(" ").trim()},Qt().createElement("div",{className:"brui-w-24 brui-h-24 brui-rounded-12 brui-border brui-border-gray-7 group-has-[:checked]/inputradio:brui-bg-blue-1 group-has-[:checked]/inputradio:brui-border-blue-1"}),Qt().createElement("div",{className:"brui-scale-0 group-has-[:checked]/inputradio:brui-scale-100 brui-absolute brui-w-12 brui-h-12 brui-bg-white brui-rounded-6 brui-top-1/2 brui-left-1/2 brui-transform -brui-translate-x-1/2 -brui-translate-y-1/2 brui-transition-transform"})))}),ql={topRight:"brui-py-32 brui-px-24",topLeft:"brui-p-16 sm:brui-p-32",leftCenter:"brui-p-12 brui-pl-48"},Kl=(0,Wt.forwardRef)(function({children:e,className:t,"aria-labelledby":n,"aria-describedby":r,name:a,radioPlacement:i="topRight",radioPlacementMobile:o,borderRadiusClassName:l,defaultPadding:u=!0,disabled:s,...c},m){return Qt().createElement("div",{className:["brui-group brui-border brui-relative",u&&ql[i],l||"brui-rounded-20",t,s&&"brui-bg-gray-5 brui-border-gray-3 brui-opacity-60"].join(" ").trim()},Qt().createElement(zl,{"aria-labelledby":n,"aria-describedby":r,name:a,ref:m,radioPlacement:i,radioPlacementMobile:o,borderRadiusClassName:l,disabled:s,...c}),e)}),Wl=Kl,Xl=({children:e,className:t,...n})=>Qt().createElement("div",{className:["brui-hidden group-has-[:checked]:brui-block",t].join(" ").trim(),...n},e),Ql=({children:e,className:t,...n})=>Qt().createElement("div",{className:["brui-mb-24",t].join(" ").trim(),...n},e),Zl=({children:e,className:t,...n})=>Qt().createElement("div",{className:["brui-mt-auto",t].join(" ").trim(),...n},e),$l=(0,Wt.createContext)({sameHeightGroups:[],updateSameHeightGroups:()=>{},deleteItem:()=>{}}),Jl=({children:e})=>{const[t,n]=(0,Wt.useState)([]),r=(0,Wt.useCallback)((e,t)=>{n(n=>n.some(t=>t.groupIndex===e)?n.map(n=>n.groupIndex===e?n.items.some(e=>e.index===t.index)?{...n,items:n.items.map(e=>e.index===t.index?t:e)}:{...n,items:[...n.items,t]}:n):[...n,{groupIndex:e,items:[t]}])},[]);return Qt().createElement($l.Provider,{value:{sameHeightGroups:t,updateSameHeightGroups:r,deleteItem:(e,t)=>{n(n=>n.map(n=>n.groupIndex===e?{...n,items:n.items.filter(e=>e.index!==t)}:n))}}},e)},eu=({children:e,index:t,groupIndex:n})=>{const r=(0,Wt.useRef)(null),[a,i]=(0,Wt.useState)(null),{width:o}=co(100),l=fo(),[u,s]=(0,Wt.useState)(o<768),c=(0,Wt.useContext)($l);if(!c)return null;const{sameHeightGroups:m,updateSameHeightGroups:d,deleteItem:p}=c;return(0,Wt.useEffect)(()=>{const e=m.find(e=>e.groupIndex===n),t=e?.items.map(e=>e.height);s(o<768),i(t?Math.max(...t):null)},[o,l,m]),(0,Wt.useEffect)(()=>{if(r.current){const e=r.current.offsetHeight;m&&d(n,{index:t,height:e})}return()=>{p(n,t)}},[l]),Qt().createElement("div",{ref:r,style:{minHeight:u?0:a?`${a}px`:"auto"},"data-id":t},e)},tu=(0,Wt.createContext)(null),nu=()=>{const e=(0,Wt.useContext)(tu);if(null===e)throw new Error("Select components must be wrapped in <Select />");return e},ru=tu,au=new Set(["inline","contents"]),iu=new Set(["table","td","th"]),ou=[":popover-open",":modal"],lu=["transform","translate","scale","rotate","perspective"],uu=["transform","translate","scale","rotate","perspective","filter"],su=["paint","layout","strict","content"],cu=new Set(["html","body","#document"]);Fi=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"].join(","),Hi="undefined"==typeof Element,Yi=Hi?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,ji=!Hi&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},Gi=function e(t,n){var r,a;return void 0===n&&(n=!0),""===(a=null==t||null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert"))||"true"===a||n&&t&&e(t.parentNode)},Vi=function e(t,n,r){var a,i,o,l,u,s,c,m;for(a=[],i=Array.from(t);i.length;)o=i.shift(),Gi(o,!1)||("SLOT"===o.tagName?(u=e((l=o.assignedElements()).length?l:o.children,!0,r),r.flatten?a.push.apply(a,u):a.push({scopeParent:o,candidates:u})):(Yi.call(o,Fi)&&r.filter(o)&&(n||!t.includes(o))&&a.push(o),s=o.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(o),c=!Gi(s,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(o)),s&&c?(m=e(!0===s?o.children:s.children,!0,r),r.flatten?a.push.apply(a,m):a.push({scopeParent:o,candidates:m})):i.unshift.apply(i,o.children)));return a},zi=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},qi=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!zi(e)?0:e.tabIndex},Ki=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},Wi=function(e){return"INPUT"===e.tagName},Xi=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},Qi=function(e,t){return!(t.disabled||Gi(t)||function(e){return Wi(e)&&"hidden"===e.type}(t)||function(e,t){var n,r,a,i,o=t.displayCheck,l=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;if(n=Yi.call(e,"details>summary:first-of-type")?e.parentElement:e,Yi.call(n,"details:not([open]) *"))return!0;if(o&&"full"!==o&&"legacy-full"!==o){if("non-zero-area"===o)return Xi(e)}else{if("function"==typeof l){for(r=e;e;){if(a=e.parentElement,i=ji(e),a&&!a.shadowRoot&&!0===l(a))return Xi(e);e=e.assignedSlot?e.assignedSlot:a||i===e.ownerDocument?a:i.host}e=r}if(function(e){var t,n,r,a,i,o,l,u=e&&ji(e),s=null===(t=u)||void 0===t?void 0:t.host,c=!1;if(u&&u!==e)for(c=!!(null!==(n=s)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(s)||null!=e&&null!==(a=e.ownerDocument)&&void 0!==a&&a.contains(e));!c&&s;)c=!(null===(o=s=null===(i=u=ji(s))||void 0===i?void 0:i.host)||void 0===o||null===(l=o.ownerDocument)||void 0===l||!l.contains(s));return c}(e))return!e.getClientRects().length;if("legacy-full"!==o)return!0}return!1}(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some(function(e){return"SUMMARY"===e.tagName})}(t)||function(e){var t,n,r;if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(n=0;n<t.children.length;n++)if("LEGEND"===(r=t.children.item(n)).tagName)return!!Yi.call(t,"fieldset[disabled] *")||!r.contains(e);return!0}t=t.parentElement}return!1}(t))},Zi=function(e,t){return!(function(e){return function(e){return Wi(e)&&"radio"===e.type}(e)&&!function(e){var t,n,r,a;if(!e.name)return!0;if(n=e.form||ji(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')},"undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return!1}return a=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form),!a||a===e}(e)}(t)||qi(t)<0||!Qi(e,t))},$i=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},Ji=function e(t){var n=[],r=[];return t.forEach(function(t,a){var i=!!t.scopeParent,o=i?t.scopeParent:t,l=function(e,t){var n=qi(e);return n<0&&t&&!zi(e)?0:n}(o,i),u=i?e(t.candidates):o;0===l?i?n.push.apply(n,u):n.push(o):r.push({documentOrder:a,tabIndex:l,item:t,isScope:i,content:u})}),r.sort(Ki).reduce(function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e},[]).concat(n)},eo=function(e,t){var n;return n=(t=t||{}).getShadowRoot?Vi([e],t.includeContainer,{filter:Zi.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:$i}):function(e,t,n){if(Gi(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(Fi));return t&&Yi.call(e,Fi)&&r.unshift(e),r.filter(n)}(e,t.includeContainer,Zi.bind(null,t)),Ji(n)};const mu=Math.min,du=Math.max,pu=Math.round,fu=Math.floor,bu=e=>({x:e,y:e}),Eu={left:"right",right:"left",bottom:"top",top:"bottom"},_u={start:"end",end:"start"},yu=new Set(["top","bottom"]),gu=["left","right"],vu=["right","left"],Nu=["top","bottom"],Au=["bottom","top"],Cu=new Set(["left","top"]),hu=bu(0),Tu=new Set(["absolute","fixed"]),Iu={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:a}=e;const i="fixed"===a,o=ge(r),l=!!t&&Ie(t.floating);if(r===o||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=bu(1);const c=bu(0),m=Ae(r);if((m||!m&&!i)&&(("body"!==_e(r)||he(o))&&(u=Me(r)),Ae(r))){const e=ct(r);s=ut(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}const d=!o||m||i?bu(0):dt(o,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+c.x+d.x,y:n.y*s.y-u.scrollTop*s.y+c.y+d.y}},getDocumentElement:ge,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:a}=e;const i=[..."clippingAncestors"===n?Ie(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=Pe(e,[],!1).filter(e=>Ne(e)&&"body"!==_e(e)),a=null;const i="fixed"===Oe(e).position;let o=i?Le(e):e;for(;Ne(o)&&!Se(o);){const t=Oe(o),n=Re(o);n||"fixed"!==t.position||(a=null),(i?!n&&!a:!n&&"static"===t.position&&a&&Tu.has(a.position)||he(o)&&!n&&ft(e,o))?r=r.filter(e=>e!==o):a=t,o=Le(o)}return t.set(e,r),r}(t,this._c):[].concat(n),r],o=i[0],l=i.reduce((e,n)=>{const r=pt(t,n,a);return e.top=du(r.top,e.top),e.right=mu(r.right,e.right),e.bottom=mu(r.bottom,e.bottom),e.left=du(r.left,e.left),e},pt(t,o,a));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:yt,getElementRects:async function(e){const t=this.getOffsetParent||yt,n=this.getDimensions,r=await n(e.floating);return{reference:bt(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=ot(e);return{width:t,height:n}},getScale:ut,isElement:Ne,isRTL:function(e){return"rtl"===Oe(e).direction}},Ru=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:a,y:i,placement:o,middlewareData:l}=t,u=await async function(e,t){const{placement:n,platform:r,elements:a}=e,i=await(null==r.isRTL?void 0:r.isRTL(a.floating)),o=We(n),l=Xe(n),u="y"===$e(n),s=Cu.has(o)?-1:1,c=i&&u?-1:1,m=Ke(t,e);let{mainAxis:d,crossAxis:p,alignmentAxis:f}="number"==typeof m?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return l&&"number"==typeof f&&(p="end"===l?-1*f:f),u?{x:p*c,y:d*s}:{x:d*s,y:p*c}}(t,e);return o===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:a+u.x,y:i+u.y,data:{...u,placement:o}}}}},xu=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:a}=t,{mainAxis:i=!0,crossAxis:o=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=Ke(e,t),s={x:n,y:r},c=await it(t,u),m=$e(We(a)),d=Qe(m);let p=s[d],f=s[m];if(i){const e="y"===d?"bottom":"right";p=qe(p+c["y"===d?"top":"left"],p,p-c[e])}if(o){const e="y"===m?"bottom":"right";f=qe(f+c["y"===m?"top":"left"],f,f-c[e])}const b=l.fn({...t,[d]:p,[m]:f});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[d]:i,[m]:o}}}}}},Su=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,a,i,o;const{placement:l,middlewareData:u,rects:s,initialPlacement:c,platform:m,elements:d}=t,{mainAxis:p=!0,crossAxis:f=!0,fallbackPlacements:b,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:_="none",flipAlignment:y=!0,...g}=Ke(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};const v=We(l),N=$e(c),A=We(c)===c,C=await(null==m.isRTL?void 0:m.isRTL(d.floating)),h=b||(A||!y?[tt(c)]:function(e){const t=tt(e);return[et(e),t,et(t)]}(c)),T="none"!==_;!b&&T&&h.push(...function(e,t,n,r){const a=Xe(e);let i=function(e,t,n){switch(e){case"top":case"bottom":return n?t?vu:gu:t?gu:vu;case"left":case"right":return t?Nu:Au;default:return[]}}(We(e),"start"===n,r);return a&&(i=i.map(e=>e+"-"+a),t&&(i=i.concat(i.map(et)))),i}(c,y,_,C));const I=[c,...h],R=await it(t,g),x=[];let S=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&x.push(R[v]),f){const e=function(e,t,n){void 0===n&&(n=!1);const r=Xe(e),a=Je(e),i=Ze(a);let o="x"===a?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(o=tt(o)),[o,tt(o)]}(l,s,C);x.push(R[e[0]],R[e[1]])}if(S=[...S,{placement:l,overflows:x}],!x.every(e=>e<=0)){const e=((null==(a=u.flip)?void 0:a.index)||0)+1,t=I[e];if(t&&("alignment"!==f||N===$e(t)||S.every(e=>e.overflows[0]>0&&$e(e.placement)===N)))return{data:{index:e,overflows:S},reset:{placement:t}};let n=null==(i=S.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(E){case"bestFit":{const e=null==(o=S.filter(e=>{if(T){const t=$e(e.placement);return t===N||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:o[0];e&&(n=e);break}case"initialPlacement":n=c}if(l!==n)return{reset:{placement:n}}}return{}}}},Ou=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:a,rects:i,platform:o,elements:l,middlewareData:u}=t,{element:s,padding:c=0}=Ke(e,t)||{};if(null==s)return{};const m=nt(c),d={x:n,y:r},p=Je(a),f=Ze(p),b=await o.getDimensions(s),E="y"===p,_=E?"top":"left",y=E?"bottom":"right",g=E?"clientHeight":"clientWidth",v=i.reference[f]+i.reference[p]-d[p]-i.floating[f],N=d[p]-i.reference[p],A=await(null==o.getOffsetParent?void 0:o.getOffsetParent(s));let C=A?A[g]:0;C&&await(null==o.isElement?void 0:o.isElement(A))||(C=l.floating[g]||i.floating[f]);const h=v/2-N/2,T=C/2-b[f]/2-1,I=mu(m[_],T),R=mu(m[y],T),x=I,S=C-b[f]-R,O=C/2-b[f]/2+h,M=qe(x,O,S),L=!u.arrow&&null!=Xe(a)&&O!==M&&i.reference[f]/2-(O<x?I:R)-b[f]/2<0,D=L?O<x?O-x:O-S:0;return{[p]:d[p]+D,data:{[p]:M,centerOffset:O-M-D,...L&&{alignmentOffset:D}},reset:L}}});to="undefined"!=typeof document?Wt.useLayoutEffect:function(){};const Mu=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(a=n,{}.hasOwnProperty.call(a,"current"))?null!=n.current?Ou({element:n.current,padding:r}).fn(t):{}:n?Ou({element:n,padding:r}).fn(t):{};var a}}),Lu=(e,t)=>({...xu(e),options:[e,t]}),Du=(e,t)=>({...Su(e),options:[e,t]}),Pu=(e,t)=>({...Mu(e),options:[e,t]}),Bu={...Xt},ku=Bu.useInsertionEffect||(e=>e());no="undefined"!=typeof document?Wt.useLayoutEffect:Wt.useEffect;let wu=!1,Uu=0;const Fu=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Uu++,Hu=Bu.useId||function(){const[e,t]=Wt.useState(()=>wu?Fu():void 0);return no(()=>{null==e&&t(Fu())},[]),Wt.useEffect(()=>{wu=!0},[]),e},Yu=Wt.forwardRef(function(e,t){const{context:{placement:n,elements:{floating:r},middlewareData:{arrow:a,shift:i}},width:o=14,height:l=7,tipRadius:u=0,strokeWidth:s=0,staticOffset:c,stroke:m,d,style:{transform:p,...f}={},...b}=e,E=Hu(),[_,y]=Wt.useState(!1);if(no(()=>{r&&"rtl"===Oe(r).direction&&y(!0)},[r]),!r)return null;const[g,v]=n.split("-"),N="top"===g||"bottom"===g;let A=c;(N&&null!=i&&i.x||!N&&null!=i&&i.y)&&(A=null);const C=2*s,h=C/2,T=o/2*(u/-8+1),I=l/2*u/4,R=!!d,x=A&&"end"===v?"bottom":"top";let S=A&&"end"===v?"right":"left";A&&_&&(S="end"===v?"left":"right");const O=null!=(null==a?void 0:a.x)?A||a.x:"",M=null!=(null==a?void 0:a.y)?A||a.y:"",L=d||"M0,0 H"+o+" L"+(o-T)+","+(l-I)+" Q"+o/2+","+l+" "+T+","+(l-I)+" Z",D={top:R?"rotate(180deg)":"",left:R?"rotate(90deg)":"rotate(-90deg)",bottom:R?"":"rotate(180deg)",right:R?"rotate(-90deg)":"rotate(90deg)"}[g];return Wt.createElement("svg",Rt({},b,{"aria-hidden":!0,ref:t,width:R?o:o+C,height:o,viewBox:"0 0 "+o+" "+(l>o?l:o),style:{position:"absolute",pointerEvents:"none",[S]:O,[x]:M,[g]:N||R?"100%":"calc(100% - "+C/2+"px)",transform:[D,p].filter(e=>!!e).join(" "),...f}}),C>0&&Wt.createElement("path",{clipPath:"url(#"+E+")",fill:"none",stroke:m,strokeWidth:C+(d?0:1),d:L}),Wt.createElement("path",{stroke:C&&!d?b.fill:"none",d:L}),Wt.createElement("clipPath",{id:E},Wt.createElement("rect",{x:-h,y:h*(R?-1:1),width:o+C,height:o})))}),ju=Wt.createContext(null),Gu=Wt.createContext(null),Vu=()=>{var e;return(null==(e=Wt.useContext(ju))?void 0:e.id)||null},zu=()=>Wt.useContext(Gu),qu=xt("safe-polygon");let Ku=0,Wu=new WeakMap,Xu=new WeakSet,Qu={},Zu=0;const $u=e=>e&&(e.host||$u(e.parentNode)),Ju=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"}),es={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0},ts=Wt.forwardRef(function(e,t){const[n,r]=Wt.useState();no(()=>(/apple/i.test(navigator.vendor)&&r("button"),document.addEventListener("keydown",kt),()=>{document.removeEventListener("keydown",kt)}),[]);const a={ref:t,tabIndex:0,role:n,"aria-hidden":!n||void 0,[xt("focus-guard")]:"",style:es};return Wt.createElement("span",Rt({},e,a))}),ns=Wt.createContext(null),rs="data-floating-ui-focusable";let as=[];const is=Wt.forwardRef(function(e,t){return Wt.createElement("button",Rt({},e,{type:"button",ref:t,tabIndex:-1,style:es}))}),os={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},ls={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},us=e=>{var t,n;return{escapeKey:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePress:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}},ss="active",cs="selected",ms=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]),ds=(0,Wt.forwardRef)(function({options:e,name:t,onChange:n,id:r,...a},i){const{selectedOptionValue:o,selectRef:l}=nu(),u=Tt([i,l]);return Qt().createElement("select",{id:`hidden-${r}`,ref:u,name:t,value:o,className:"brui-sr-only","aria-hidden":"true",tabIndex:-1,onChange:n,...a},Qt().createElement("option",{value:""}),e.map(e=>Qt().createElement("option",{key:e.id,value:e.value})))}),ps="brui-text-left brui-font-normal brui-text-darkblue focus:brui-text-darkblue brui-text-14 brui-leading-18 disabled:hover:brui-bg-gray-5 hover:brui-bg-gray-6 brui-rounded disabled:brui-border-gray-3 brui-h-44 brui-w-full brui-py-13 brui-px-10 brui-border-2 brui-flex brui-items-center brui-justify-between focus-visible:brui-outline-1 focus-visible:brui-outline-blue-2 focus-visible:brui-outline-offset-4",fs=({"aria-controls":e,id:t,"aria-labelledby":n,"aria-describedby":r,className:a,hasError:i,"aria-required":o})=>{const{selectedOptionValue:l,buttonRef:u,options:s,isOpen:c,selectedOptionDisplayName:m,navigatorIndex:d,setIsNavigating:p,setSelectedOptionValue:f,setSelectedOptionDisplayName:b,setIsOpen:E,setNavigatorIndex:_,selectRef:y}=nu(),g=e=>{const t=document.getElementById(`${e}`);t&&t.scrollIntoView({behavior:"auto",block:"nearest"})},[v]=(0,Wt.useState)("");return(0,Wt.useEffect)(()=>{if(v){const e=s.findIndex(e=>e.displayName.toString().toLowerCase().startsWith(v.toLowerCase()));e>-1&&(_(e),p(!0),g(s[e].id))}},[v]),Qt().createElement("button",{id:t,ref:u,type:"button",onClick:()=>{E(!c)},onKeyDown:e=>{let t;if("ArrowDown"===e.key||"ArrowUp"===e.key)if(e.preventDefault(),E(!0),p(!0),"ArrowDown"===e.key){t=null===d?0:d+1;const e=s.length-1,n=t>e;_(t>e?e:t),g(s[n?e:t].id)}else"ArrowUp"===e.key&&(t=null===d?0:d-1,_(t<0?0:t),g(s[t<0?0:t].id));else if("Enter"===e.key&&c&&null!==d)l!==s[d].value&&(f(s[d].value),b(s[d].displayName),setTimeout(()=>{if(y.current){const e=new Event("change",{bubbles:!0});y.current.dispatchEvent(e)}},0)),p(!1);else if("Escape"===e.key){E(!1),p(!1);const e=s.findIndex(e=>e.value===l);_(e)}},onBlur:()=>{E(!1)},"aria-controls":e,"aria-haspopup":"listbox","aria-expanded":c,"aria-labelledby":n,"aria-describedby":r,"aria-activedescendant":c&&s[d]?s[d].id:void 0,role:"combobox",className:[ps,a,i?"brui-border-red focus:brui-border-red":"brui-border-gray-7 focus:brui-text-gray-9"].join(" ").trim(),"aria-required":o},m,Qt().createElement(Eo,{className:["brui-text-15 brui-ml-auto brui-text-blue"].join(" ").trim(),iconClass:"bi_brui",iconName:c?"bi_small_select_trigger_half_open":"bi_small_select_trigger_half_f"}))},bs="brui-bg-white brui-text-14 brui-leading-18 brui-rounded-b-4 brui-shadow-2sm brui-w-full brui-z-[1001] brui-overflow-y-auto brui-border-gray-4 brui-max-h-[230px]",Es=({id:e,children:t,dropDownHeight:n})=>{const{isOpen:r}=nu(),{width:a}=co();return Qt().createElement("div",{id:e,className:"brui-absolute brui-w-full brui-top-[44px] brui-z-[1001]"},Qt().createElement("ul",{role:"listbox",className:[bs,r?"brui-absolute":"brui-hidden"].join(" ").trim(),style:n?po(a,n):void 0},t))},_s=(0,Wt.forwardRef)(function({defaultValue:e,placeHolder:t,children:n,"aria-labelledby":r,"aria-describedby":a,name:i,id:o,onChange:l,className:u,hasError:s,errorMessage:c,"aria-required":m,disableDropdownIcon:d,dropDownHeight:p,...f},b){const{formGroupHasError:E,formGroupErrorMessage:_,inputErrorId:y}=(0,Wt.useContext)(il)||{},g=void 0!==_?_:c,[v,N]=(0,Wt.useState)([]),[A,C]=(0,Wt.useState)(null),[h,T]=(0,Wt.useState)(e),[I,R]=(0,Wt.useState)(t),[x,S]=(0,Wt.useState)(!1),[O,M]=(0,Wt.useState)(!1),L=(0,Wt.useRef)(null),D=(0,Wt.useRef)(null),P=`dropdown-${(0,Wt.useId)()}`;(0,Wt.useEffect)(()=>{const t=v.findIndex(t=>t.value===e);e&&v.length>0&&t>-1&&(T(v[t].value),C(t),R(v[t].displayName))},[v,e]);const B=(0,Wt.useMemo)(()=>a||y?y?[a||"",y].join(" ").trim():[a||"",`${o}-error`].join(" ").trim():"",[a,y,o]);return Qt().createElement(ru.Provider,{value:{selectedOptionValue:h,selectedOptionDisplayName:I,isOpen:x,buttonRef:L,selectRef:D,isNavigating:O,options:v,navigatorIndex:A,setSelectedOptionValue:T,setSelectedOptionDisplayName:R,setIsOpen:S,setIsNavigating:M,setNavigatorIndex:C,initializeOptions:e=>{N(t=>[...t,e])}}},Qt().createElement("div",{className:"brui-relative"},Qt().createElement(fs,{"aria-controls":P,id:o,"aria-labelledby":r,"aria-describedby":B,className:u,hasError:E||s,"aria-required":m,disableDropdownIcon:d}),Qt().createElement(Es,{id:P,dropDownHeight:p||void 0},n),Qt().createElement(ds,{options:v,id:o,name:i,onChange:l,ref:b,...f}),void 0===E&&s&&g&&Qt().createElement(Uo,{id:y||`${o}-error`,iconClass:"bi_error_bl_bg_cf",iconName:"bi_brui",errorMessage:g||""})))}),ys=_s,gs=({displayName:e,value:t,id:n,...r})=>{const{selectedOptionValue:a,buttonRef:i,selectRef:o,isNavigating:l,options:u,navigatorIndex:s,setSelectedOptionValue:c,setSelectedOptionDisplayName:m,setIsOpen:d,setNavigatorIndex:p,initializeOptions:f}=nu();(0,Wt.useEffect)(()=>{u.findIndex(e=>e.value===t)<0&&f({value:t,id:n,displayName:e})},[]);const b=["brui-px-18 brui-py-10 hover:brui-bg-gray-1",a===t&&!l||l&&u[s].id===n?"brui-bg-gray-1":""].join(" ").trim();return Qt().createElement("li",{value:t,id:n,onMouseDown:()=>{((e,t)=>{if(a!==e){c(e),m(t);const n=u.findIndex(t=>t.value===e);p(n),setTimeout(()=>{if(o.current){const e=new Event("change",{bubbles:!0});o.current.dispatchEvent(e)}},0)}d(!1),setTimeout(()=>{i.current?.focus()},1)})(t,e)},role:"option",className:b,...r},e)},vs=({elementType:e,leftButton:t,heading:n,rightButton:r,centerMobile:a})=>{if(e&&"div"!==(i=e)&&"header"!==i)throw new Error(`Invalid elementType: ${e}. Must be "div" or "header".`);var i;const o=e||"header",l=(0,Wt.useMemo)(()=>a?"brui-text-center max-sm:brui-absolute max-sm:brui-left-1/2 max-sm:brui-top-1/2 max-sm:brui-transform max-sm:-brui-translate-x-1/2 max-sm:-brui-translate-y-1/2":"brui-ps-16 sm:brui-ps-0",[a]);return Qt().createElement(o,{className:"brui-bg-blue"},Qt().createElement(Wo,null,Qt().createElement("div",{className:"brui-h-[54px] sm:brui-h-[75px] brui-flex brui-flex-row brui-items-center brui-text-white brui-relative brui-py-5"},Qt().createElement("div",{className:"sm:brui-basis-1/3"},t),Qt().createElement("div",{className:["sm:brui-basis-1/3 sm:brui-text-center",l].join(" ").trim()},n),Qt().createElement("div",{className:"sm:brui-basis-1/3 brui-hidden sm:brui-block brui-text-right"},r))))},Ns=({elementType:e,children:t,className:n,...r})=>{if(e&&"div"!==(a=e)&&"footer"!==a)throw new Error(`Invalid elementType: ${e}. Must be "div" or "footer".`);var a;const i=e||"footer";return Qt().createElement(i,{...r},Qt().createElement(Wo,null,Qt().createElement("div",{className:n},t)))},As=({text:e,variant:t,className:n,children:r,...a})=>{const i=(0,Wt.useMemo)(()=>({tagStyle:["brui-font-semibold brui-text-12 brui-leading-14 brui-rounded-6 brui-py-4 brui-px-8",{solidRedTag:"brui-bg-red brui-text-white",solidBlueTag:"brui-bg-blue brui-text-white",solidWhiteTag:"brui-bg-white brui-text-red",solidGreyTag:"brui-bg-gray-4 brui-text-white",solidBlackTag:"brui-bg-darkblue brui-text-white",solidGradientDarkBlueTag:"brui-bg-gradient-to-l brui-from-darkblue brui-from-0% brui-to-blue brui-to-100% brui-text-white",outlinedBlackTag:"brui-text-black brui-bg-transparent brui-border-1 brui-border-solid brui-border-black",outlinedWhiteTag:"brui-text-white brui-bg-transparent brui-border-1 brui-border-solid brui-border-white",outlinedRedTag:"brui-text-red brui-bg-transparent brui-border-1 brui-border-solid brui-border-red"}[t],n].join(" ").trim()}),[t]);return Qt().createElement("span",{className:i.tagStyle,...a},e," ",r)},Cs=(0,Wt.createContext)({activeTabId:"",updateActiveTabID:()=>{},mode:"manual",isFocusableTabPanel:!1}),hs=({mode:e,children:t,isFocusableTabPanel:n,...r})=>{const[a,i]=(0,Wt.useState)("");return Qt().createElement(Cs.Provider,{value:{activeTabId:a,updateActiveTabID:e=>{i(e)},mode:e,isFocusableTabPanel:n},...r},t)},Ts="brui-inline-flex brui-w-full hover:after:brui-content-[''] hover:after:brui-w-full hover:after:brui-h-full hover:after:brui-absolute hover:after:-brui-bottom-0 hover:after:brui-border-solid hover:after:brui-border-b-4 hover:after:brui-border-blue focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3 focus:!brui-rounded-6 brui-text-blue",Is=({id:e,active:t,activeByDefault:n,useDefaultFontStyle:r=!0,className:a,onClick:i,children:o,...l})=>{const{mode:u,activeTabId:s,updateActiveTabID:c}=(0,Wt.useContext)(Cs);(0,Wt.useEffect)(()=>{n&&!s&&c(e)},[n,s,c,e]),(0,Wt.useEffect)(()=>{t&&c(e)},[t]);const m=s===e;return Qt().createElement("div",{className:"brui-inline-flex brui-relative brui-mr-24 last:brui-mr-5 brui-py-20 brui-shrink-0"},Qt().createElement("button",{id:e,className:[a,Ts,m&&"active focus:after:brui-content-[''] focus:after:brui-w-full focus:after:brui-h-full focus:after:brui-absolute focus:after:-brui-bottom-0 focus:after:brui-border-solid focus:after:brui-border-b-4 focus:after:brui-border-blue after:brui-w-full after:brui-h-full after:brui-absolute after:-brui-bottom-0 after:brui-border-solid after:brui-border-b-4 after:brui-border-blue !brui-text-black",r,r&&"brui-text-18 brui-leading-22"].join(" "),onClick:t=>{c(e),i&&i(t)},onKeyDown:e=>{if("ArrowRight"===e.key||"ArrowLeft"===e.key){e.preventDefault();const t=e.currentTarget.parentElement;if(!t)return;const n="ArrowRight"===e.key?t.nextElementSibling:t.previousElementSibling;if(!n)return;const r=Array.from(n.children).filter(e=>"BUTTON"===e.tagName),a=r.find(e=>-1===e.tabIndex);a&&(a.focus(),"automatic"===u&&a.click())}},role:"tab","aria-selected":m?"true":"false",tabIndex:m?0:-1,...l},o))},Rs=({className:e,"aria-labelledby":t,children:n})=>{const r=(0,Wt.useRef)(null);return Qt().createElement("div",{className:"brui-relative sm:-brui-mx-5"},Qt().createElement("div",{ref:r,className:"brui-tablist-overflow brui-px-5 brui-relative brui-w-full brui-overflow-x-auto brui-scroll-smooth brui-scrollbar-width-none brui-whitespace-nowrap"},Qt().createElement("div",{className:e,role:"tablist","aria-labelledby":t},n)))},xs=({id:e,tabId:t,children:n,"aria-labelledby":r,activeByDefault:a})=>{const{activeTabId:i,updateActiveTabID:o,isFocusableTabPanel:l}=(0,Wt.useContext)(Cs);(0,Wt.useEffect)(()=>{a&&!i&&o(t)},[a,i,o,t]);const u=i===t,s=l?0:void 0;return Qt().createElement("div",{id:e,role:"tabpanel",className:"focus-visible:brui-outline-blue focus-visible:brui-outline focus-visible:brui-outline-2 focus-visible:brui-outline-offset-3 focus-visible:brui-rounded-6 "+(u?"brui-block":"brui-hidden"),"aria-labelledby":r,"data-tabid":t,tabIndex:s},n)},Ss=(0,Wt.createContext)(null),Os=({placement:e="bottom",children:t})=>{const n=function(e){const[t,n]=(0,Wt.useState)(!1),[r,a]=(0,Wt.useState)(!1),i=(0,Wt.useRef)(null),{width:o}=co(),[l,u]=(0,Wt.useState)(o<768),s=function(e){void 0===e&&(e={});const{nodeId:t}=e,n=function(e){const{open:t=!1,onOpenChange:n,elements:r}=e,a=Hu(),i=Wt.useRef({}),[o]=Wt.useState(()=>function(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach(e=>e(n))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter(e=>e!==n))||[])}}}()),l=null!=Vu(),[u,s]=Wt.useState(r.reference),c=It((e,t,r)=>{i.current.openEvent=e?t:void 0,o.emit("openchange",{open:e,event:t,reason:r,nested:l}),null==n||n(e,t,r)}),m=Wt.useMemo(()=>({setPositionReference:s}),[]),d=Wt.useMemo(()=>({reference:u||r.reference||null,floating:r.floating||null,domReference:r.reference}),[u,r.reference,r.floating]);return Wt.useMemo(()=>({dataRef:i,open:t,onOpenChange:c,elements:d,events:o,floatingId:a,refs:m}),[t,c,d,o,a,m])}({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,a=r.elements,[i,o]=Wt.useState(null),[l,u]=Wt.useState(null),s=(null==a?void 0:a.domReference)||i,c=Wt.useRef(null),m=zu();no(()=>{s&&(c.current=s)},[s]);const d=function(e){void 0===e&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:a,elements:{reference:i,floating:o}={},transform:l=!0,whileElementsMounted:u,open:s}=e,[c,m]=Wt.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[d,p]=Wt.useState(r);Nt(d,r)||p(r);const[f,b]=Wt.useState(null),[E,_]=Wt.useState(null),y=Wt.useCallback(e=>{e!==A.current&&(A.current=e,b(e))},[]),g=Wt.useCallback(e=>{e!==C.current&&(C.current=e,_(e))},[]),v=i||f,N=o||E,A=Wt.useRef(null),C=Wt.useRef(null),h=Wt.useRef(c),T=null!=u,I=ht(u),R=ht(a),x=ht(s),S=Wt.useCallback(()=>{if(!A.current||!C.current)return;const e={placement:t,strategy:n,middleware:d};R.current&&(e.platform=R.current),((e,t,n)=>{const r=new Map,a={platform:Iu,...n},i={...a.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:a="absolute",middleware:i=[],platform:o}=n,l=i.filter(Boolean),u=await(null==o.isRTL?void 0:o.isRTL(t));let s=await o.getElementRects({reference:e,floating:t,strategy:a}),{x:c,y:m}=at(s,r,u),d=r,p={},f=0;for(let b=0;b<l.length;b++){const{name:n,fn:i}=l[b],{x:E,y:_,data:y,reset:g}=await i({x:c,y:m,initialPlacement:r,placement:d,strategy:a,middlewareData:p,rects:s,platform:o,elements:{reference:e,floating:t}});c=null!=E?E:c,m=null!=_?_:m,p={...p,[n]:{...p[n],...y}},g&&f<=50&&(f++,"object"==typeof g&&(g.placement&&(d=g.placement),g.rects&&(s=!0===g.rects?await o.getElementRects({reference:e,floating:t,strategy:a}):g.rects),({x:c,y:m}=at(s,d,u))),b=-1)}return{x:c,y:m,placement:d,strategy:a,middlewareData:p}})(e,t,{...a,platform:i})})(A.current,C.current,e).then(e=>{const t={...e,isPositioned:!1!==x.current};O.current&&!Nt(h.current,t)&&(h.current=t,Ui.flushSync(()=>{m(t)}))})},[d,t,n,R,x]);to(()=>{!1===s&&h.current.isPositioned&&(h.current.isPositioned=!1,m(e=>({...e,isPositioned:!1})))},[s]);const O=Wt.useRef(!1);to(()=>(O.current=!0,()=>{O.current=!1}),[]),to(()=>{if(v&&(A.current=v),N&&(C.current=N),v&&N){if(I.current)return I.current(v,N,S);S()}},[v,N,S,I,T]);const M=Wt.useMemo(()=>({reference:A,floating:C,setReference:y,setFloating:g}),[y,g]),L=Wt.useMemo(()=>({reference:v,floating:N}),[v,N]),D=Wt.useMemo(()=>{const e={position:n,left:0,top:0};if(!L.floating)return e;const t=Ct(L.floating,c.x),r=Ct(L.floating,c.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...At(L.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,L.floating,c.x,c.y]);return Wt.useMemo(()=>({...c,update:S,refs:M,elements:L,floatingStyles:D}),[c,S,M,L,D])}({...e,elements:{...a,...l&&{reference:l}}}),p=Wt.useCallback(e=>{const t=Ne(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;u(t),d.refs.setReference(t)},[d.refs]),f=Wt.useCallback(e=>{(Ne(e)||null===e)&&(c.current=e,o(e)),(Ne(d.refs.reference.current)||null===d.refs.reference.current||null!==e&&!Ne(e))&&d.refs.setReference(e)},[d.refs]),b=Wt.useMemo(()=>({...d.refs,setReference:f,setPositionReference:p,domReference:c}),[d.refs,f,p]),E=Wt.useMemo(()=>({...d.elements,domReference:s}),[d.elements,s]),_=Wt.useMemo(()=>({...d,...r,refs:b,elements:E,nodeId:t}),[d,b,E,t,r]);return no(()=>{r.dataRef.current.floatingContext=_;const e=null==m?void 0:m.nodesRef.current.find(e=>e.id===t);e&&(e.context=_)}),Wt.useMemo(()=>({...d,context:_,refs:b,elements:E}),[d,b,E,_])}({placement:e,open:t,onOpenChange:n,whileElementsMounted:vt,middleware:[{...Ru(24),options:[24,void 0]},Du({crossAxis:e.includes("-"),fallbackAxisSideDirection:"end",padding:5}),Lu({padding:5}),Pu({element:i,padding:8})]}),c=s.context,m=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:a,elements:{domReference:i}}=e,{enabled:o=!0,event:l="click",toggle:u=!0,ignoreMouse:s=!1,keyboardHandlers:c=!0,stickIfOpen:m=!0}=t,d=Wt.useRef(),p=Wt.useRef(!1),f=Wt.useMemo(()=>({onPointerDown(e){d.current=e.pointerType},onMouseDown(e){const t=d.current;0===e.button&&"click"!==l&&(He(t,!0)&&s||(!n||!u||a.current.openEvent&&m&&"mousedown"!==a.current.openEvent.type?(e.preventDefault(),r(!0,e.nativeEvent,"click")):r(!1,e.nativeEvent,"click")))},onClick(e){const t=d.current;"mousedown"===l&&d.current?d.current=void 0:He(t,!0)&&s||(!n||!u||a.current.openEvent&&m&&"click"!==a.current.openEvent.type?r(!0,e.nativeEvent,"click"):r(!1,e.nativeEvent,"click"))},onKeyDown(e){d.current=void 0,e.defaultPrevented||!c||Ht(e)||(" "!==e.key||Yt(i)||(e.preventDefault(),p.current=!0),"Enter"===e.key&&r(!n||!u,e.nativeEvent,"click"))},onKeyUp(e){e.defaultPrevented||!c||Ht(e)||Yt(i)||" "===e.key&&p.current&&(p.current=!1,r(!n||!u,e.nativeEvent,"click"))}}),[a,i,l,s,c,r,n,m,u]);return Wt.useMemo(()=>o?{reference:f}:{},[o,f])}(c),d=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:a,events:i,elements:o}=e,{enabled:l=!0,delay:u=0,handleClose:s=null,mouseOnly:c=!1,restMs:m=0,move:d=!0}=t,p=zu(),f=Vu(),b=St(s),E=St(u),_=St(n),y=Wt.useRef(),g=Wt.useRef(-1),v=Wt.useRef(),N=Wt.useRef(-1),A=Wt.useRef(!0),C=Wt.useRef(!1),h=Wt.useRef(()=>{}),T=Wt.useRef(!1),I=Wt.useCallback(()=>{var e;const t=null==(e=a.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t},[a]);Wt.useEffect(()=>{function e(e){let{open:t}=e;t||(clearTimeout(g.current),clearTimeout(N.current),A.current=!0,T.current=!1)}if(l)return i.on("openchange",e),()=>{i.off("openchange",e)}},[l,i]),Wt.useEffect(()=>{function e(e){I()&&r(!1,e,"hover")}if(!l)return;if(!b.current)return;if(!n)return;const t=Ye(o.floating).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}},[o.floating,n,r,l,b,I]);const R=Wt.useCallback(function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n="hover");const a=Ot(E.current,"close",y.current);a&&!v.current?(clearTimeout(g.current),g.current=window.setTimeout(()=>r(!1,e,n),a)):t&&(clearTimeout(g.current),r(!1,e,n))},[E,r]),x=It(()=>{h.current(),v.current=void 0}),S=It(()=>{if(C.current){const e=Ye(o.floating).body;e.style.pointerEvents="",e.removeAttribute(qu),C.current=!1}}),O=It(()=>!!a.current.openEvent&&["click","mousedown"].includes(a.current.openEvent.type));Wt.useEffect(()=>{function e(e){if(clearTimeout(g.current),A.current=!1,c&&!He(y.current)||m>0&&!Ot(E.current,"open"))return;const t=Ot(E.current,"open",y.current);t?g.current=window.setTimeout(()=>{_.current||r(!0,e,"hover")},t):n||r(!0,e,"hover")}function t(e){if(O())return;h.current();const t=Ye(o.floating);if(clearTimeout(N.current),T.current=!1,b.current&&a.current.floatingContext){n||clearTimeout(g.current),v.current=b.current({...a.current.floatingContext,tree:p,x:e.clientX,y:e.clientY,onClose(){S(),x(),O()||R(e,!0,"safe-polygon")}});const r=v.current;return t.addEventListener("mousemove",r),void(h.current=()=>{t.removeEventListener("mousemove",r)})}("touch"!==y.current||!we(o.floating,e.relatedTarget))&&R(e)}function i(e){O()||a.current.floatingContext&&(null==b.current||b.current({...a.current.floatingContext,tree:p,x:e.clientX,y:e.clientY,onClose(){S(),x(),O()||R(e)}})(e))}if(l&&Ne(o.domReference)){var u;const r=o.domReference;return n&&r.addEventListener("mouseleave",i),null==(u=o.floating)||u.addEventListener("mouseleave",i),d&&r.addEventListener("mousemove",e,{once:!0}),r.addEventListener("mouseenter",e),r.addEventListener("mouseleave",t),()=>{var a;n&&r.removeEventListener("mouseleave",i),null==(a=o.floating)||a.removeEventListener("mouseleave",i),d&&r.removeEventListener("mousemove",e),r.removeEventListener("mouseenter",e),r.removeEventListener("mouseleave",t)}}},[o,l,e,c,m,d,R,x,S,r,n,_,p,E,b,a,O]),no(()=>{var e,t;if(l&&n&&null!=(e=b.current)&&e.__options.blockPointerEvents&&I()){C.current=!0;const e=o.floating;if(Ne(o.domReference)&&e){const n=Ye(o.floating).body;n.setAttribute(qu,"");const r=o.domReference,a=null==p||null==(t=p.nodesRef.current.find(e=>e.id===f))||null==(t=t.context)?void 0:t.elements.floating;return a&&(a.style.pointerEvents=""),n.style.pointerEvents="none",r.style.pointerEvents="auto",e.style.pointerEvents="auto",()=>{n.style.pointerEvents="",r.style.pointerEvents="",e.style.pointerEvents=""}}}},[l,n,f,o,p,b,I]),no(()=>{n||(y.current=void 0,T.current=!1,x(),S())},[n,x,S]),Wt.useEffect(()=>()=>{x(),clearTimeout(g.current),clearTimeout(N.current),S()},[l,o.domReference,x,S]);const M=Wt.useMemo(()=>{function e(e){y.current=e.pointerType}return{onPointerDown:e,onPointerEnter:e,onMouseMove(e){function t(){A.current||_.current||r(!0,a,"hover")}const{nativeEvent:a}=e;c&&!He(y.current)||n||0===m||T.current&&e.movementX**2+e.movementY**2<2||(clearTimeout(N.current),"touch"===y.current?t():(T.current=!0,N.current=window.setTimeout(t,m)))}}},[c,r,n,_,m]),L=Wt.useMemo(()=>({onMouseEnter(){clearTimeout(g.current)},onMouseLeave(e){O()||R(e.nativeEvent,!1)}}),[R,O]);return Wt.useMemo(()=>l?{reference:M,floating:L}:{},[l,M,L])}(c,{handleClose:Vt({buffer:5})}),p=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,elements:a,dataRef:i}=e,{enabled:o=!0,escapeKey:l=!0,outsidePress:u=!0,outsidePressEvent:s="pointerdown",referencePress:c=!1,referencePressEvent:m="pointerdown",ancestorScroll:d=!1,bubbles:p,capture:f}=t,b=zu(),E=It("function"==typeof u?u:()=>!1),_="function"==typeof u?E:u,y=Wt.useRef(!1),g=Wt.useRef(!1),{escapeKey:v,outsidePress:N}=us(p),{escapeKey:A,outsidePress:C}=us(f),h=Wt.useRef(!1),T=It(e=>{var t;if(!n||!o||!l||"Escape"!==e.key)return;if(h.current)return;const a=null==(t=i.current.floatingContext)?void 0:t.nodeId,u=b?Lt(b.nodesRef.current,a):[];if(!v&&(e.stopPropagation(),u.length>0)){let e=!0;if(u.forEach(t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__escapeKeyBubbles||(e=!1)}),!e)return}r(!1,function(e){return"nativeEvent"in e}(e)?e.nativeEvent:e,"escape-key")}),I=It(e=>{var t;const n=()=>{var t;T(e),null==(t=Ge(e))||t.removeEventListener("keydown",n)};null==(t=Ge(e))||t.addEventListener("keydown",n)}),R=It(e=>{var t;const n=y.current;y.current=!1;const o=g.current;if(g.current=!1,"click"===s&&o)return;if(n)return;if("function"==typeof _&&!_(e))return;const l=Ge(e),u="["+xt("inert")+"]",c=Ye(a.floating).querySelectorAll(u);let m=Ne(l)?l:null;for(;m&&!Se(m);){const e=Le(m);if(Se(e)||!Ne(e))break;m=e}if(c.length&&Ne(l)&&!l.matches("html,body")&&!we(l,a.floating)&&Array.from(c).every(e=>!we(m,e)))return;if(Ae(l)&&O){const t=l.clientWidth>0&&l.scrollWidth>l.clientWidth,n=l.clientHeight>0&&l.scrollHeight>l.clientHeight;let r=n&&e.offsetX>l.clientWidth;if(n&&"rtl"===Oe(l).direction&&(r=e.offsetX<=l.offsetWidth-l.clientWidth),r||t&&e.offsetY>l.clientHeight)return}const d=null==(t=i.current.floatingContext)?void 0:t.nodeId,p=b&&Lt(b.nodesRef.current,d).some(t=>{var n;return je(e,null==(n=t.context)?void 0:n.elements.floating)});if(je(e,a.floating)||je(e,a.domReference)||p)return;const f=b?Lt(b.nodesRef.current,d):[];if(f.length>0){let e=!0;if(f.forEach(t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__outsidePressBubbles||(e=!1)}),!e)return}r(!1,e,"outside-press")}),x=It(e=>{var t;const n=()=>{var t;R(e),null==(t=Ge(e))||t.removeEventListener(s,n)};null==(t=Ge(e))||t.addEventListener(s,n)});Wt.useEffect(()=>{function e(e){r(!1,e,"ancestor-scroll")}function t(){window.clearTimeout(c),h.current=!0}function u(){c=window.setTimeout(()=>{h.current=!1},xe()?5:0)}if(!n||!o)return;i.current.__escapeKeyBubbles=v,i.current.__outsidePressBubbles=N;let c=-1;const m=Ye(a.floating);l&&(m.addEventListener("keydown",A?I:T,A),m.addEventListener("compositionstart",t),m.addEventListener("compositionend",u)),_&&m.addEventListener(s,C?x:R,C);let p=[];return d&&(Ne(a.domReference)&&(p=Pe(a.domReference)),Ne(a.floating)&&(p=p.concat(Pe(a.floating))),!Ne(a.reference)&&a.reference&&a.reference.contextElement&&(p=p.concat(Pe(a.reference.contextElement)))),p=p.filter(e=>{var t;return e!==(null==(t=m.defaultView)?void 0:t.visualViewport)}),p.forEach(t=>{t.addEventListener("scroll",e,{passive:!0})}),()=>{l&&(m.removeEventListener("keydown",A?I:T,A),m.removeEventListener("compositionstart",t),m.removeEventListener("compositionend",u)),_&&m.removeEventListener(s,C?x:R,C),p.forEach(t=>{t.removeEventListener("scroll",e)}),window.clearTimeout(c)}},[i,a,l,_,s,n,r,d,o,v,N,T,A,I,R,C,x]),Wt.useEffect(()=>{y.current=!1},[_,s]);const S=Wt.useMemo(()=>({onKeyDown:T,[os[m]]:e=>{c&&r(!1,e.nativeEvent,"reference-press")}}),[T,r,c,m]),O=Wt.useMemo(()=>({onKeyDown:T,onMouseDown(){g.current=!0},onMouseUp(){g.current=!0},[ls[s]]:()=>{y.current=!0}}),[T,s]);return Wt.useMemo(()=>o?{reference:S,floating:O}:{},[o,S,O])}(c),f=function(e,t){var n;void 0===t&&(t={});const{open:r,floatingId:a}=e,{enabled:i=!0,role:o="dialog"}=t,l=null!=(n=ms.get(o))?n:o,u=Hu(),s=null!=Vu(),c=Wt.useMemo(()=>"tooltip"===l||"label"===o?{["aria-"+("label"===o?"labelledby":"describedby")]:r?a:void 0}:{"aria-expanded":r?"true":"false","aria-haspopup":"alertdialog"===l?"dialog":l,"aria-controls":r?a:void 0,..."listbox"===l&&{role:"combobox"},..."menu"===l&&{id:u},..."menu"===l&&s&&{role:"menuitem"},..."select"===o&&{"aria-autocomplete":"none"},..."combobox"===o&&{"aria-autocomplete":"list"}},[l,a,s,r,u,o]),m=Wt.useMemo(()=>{const e={id:a,...l&&{role:l}};return"tooltip"===l||"label"===o?e:{...e,..."menu"===l&&{"aria-labelledby":u}}},[l,a,u,o]),d=Wt.useCallback(e=>{let{active:t,selected:n}=e;const r={role:"option",...t&&{id:a+"-option"}};switch(o){case"select":return{...r,"aria-selected":t&&n};case"combobox":return{...r,...t&&{"aria-selected":!0}}}return{}},[a,o]);return Wt.useMemo(()=>i?{reference:c,floating:m,item:d}:{},[i,c,m,d])}(c),b=function(e){void 0===e&&(e=[]);const t=e.map(e=>null==e?void 0:e.reference),n=e.map(e=>null==e?void 0:e.floating),r=e.map(e=>null==e?void 0:e.item),a=Wt.useCallback(t=>jt(t,e,"reference"),t),i=Wt.useCallback(t=>jt(t,e,"floating"),n),o=Wt.useCallback(t=>jt(t,e,"item"),r);return Wt.useMemo(()=>({getReferenceProps:a,getFloatingProps:i,getItemProps:o}),[a,i,o])}([m,p,f,d]),E=e=>{a(e)};return(0,Wt.useEffect)(()=>{const e=o<768;u(e),n((r||t)&&!e),E((r||t)&&e)},[o]),{popoverIsOpen:t,setPopoverIsOpen:n,arrowRef:i,modalIsOpen:r,toggleModal:E,isMobile:l,...b,...s}}(e);return Qt().createElement(Ss.Provider,{value:n},t)},Ms=()=>{const e=(0,Wt.useContext)(Ss);if(null==e)throw new Error("Popover components must be wrapped in <Popover />");return e},Ls=(0,Wt.forwardRef)(function({children:e,ariaLabel:t="More info",className:n,disabled:r,...a},i){const o=Ms(),l=Tt([o.refs.setReference,i]),u=(0,Wt.useMemo)(()=>({popover:{ref:l,"data-state":o.popoverIsOpen?"open":"closed",...o.getReferenceProps(),"aria-haspopup":!1},modal:{ref:i,onClick:()=>o.toggleModal(!0)}}),[o,l,i]);return Qt().createElement(Qt().Fragment,null,Qt().createElement(xo,{type:"button",disabled:r,"aria-disabled":r,...a,variant:"icon",size:"default","aria-label":t,className:["disabled:brui-pointer-events-none",n].join(" ").trim(),...o.isMobile?u.modal:u.popover},e))}),Ds=Ls,Ps=(0,Wt.forwardRef)(function({children:e,className:t,ariaLabelledby:n="MoreInfo"},r){const{context:a,...i}=Ms(),o=Tt([i.refs.setFloating,r]),l=(0,Wt.useMemo)(()=>"right"===a.placement?"-brui-mr-1":"left"===a.placement?"-brui-ml-1":"",[a]);return Qt().createElement(Qt().Fragment,null,a.open&&Qt().createElement(Ft,{context:a,modal:!1,disabled:!0},Qt().createElement("div",{ref:o,style:i.floatingStyles,...i.getFloatingProps(),role:"none",className:["brui-border brui-border-gray-8 brui-p-25 brui-w-[290px] brui-hidden sm:brui-block brui-shadow-2sm brui-bg-white brui-z-[1000]",t].join(" ").trim()},Qt().createElement(Yu,{ref:i.arrowRef,context:a,width:30,height:15,fill:"white",stroke:"#e1e1e1",strokeWidth:1,tipRadius:0,className:l}),e)),i.modalIsOpen&&Qt().createElement(Tl,{"aria-labelledby":n,onEscapeKeyPressed:()=>i.toggleModal(!1),onOverlayClick:()=>i.toggleModal(!1)},Qt().createElement(Ml,{verticalAlignment:{mobile:"center",tablet:"center",desktop:"center"},useDefaultRadius:!1,className:"brui-rounded-10"},Qt().createElement(Rl,null,Qt().createElement(xo,{variant:"icon",size:"default",className:"brui-absolute brui-top-15 brui-right-15  brui-leading-0",onClick:()=>i.toggleModal(!1)},Qt().createElement(Eo,{className:"brui-text-20 brui-text-blue-1",iconClass:"bi_brui",iconName:"bi_close"}),Qt().createElement(Ao,null,"Close modal")),Qt().createElement("div",{className:"brui-w-full brui-pt-15 -brui-mb-5"},e)))))}),Bs=Ps,ks={Accordion:oo,AccordionContent:bo,AccordionIcon:_o,AccordionItem:uo,AccordionToggleTitle:yo,AccordionTrigger:go,Alert:ho,Button:xo,Card:No,Carousel:Oo,CarouselArrows:Mo,CarouselContent:Lo,CarouselItem:Do,CarouselPagination:Po,Checkbox:Ho,CheckboxCard:zo,CheckboxCardBody:qo,CheckboxCardInput:jo,CheckboxCardPrice:Ko,Container:Wo,Divider:Xo,DockBar:Zo,Fixed:$o,FormControl:al,FormGroup:ol,FooterLegal:nl,Heading:ul,HeadingStep:sl,Icon:Eo,IconLink:fl,InputError:Uo,InputText:_l,Label:gl,Link:pl,ListItem:el,Modal:Tl,ModalBody:Rl,ModalContent:Ml,ModalFooter:Dl,ModalHeader:Bl,Portal:Cl,Price:Fl,RadioButton:Gl,RadioCard:Wl,DynamicRadioContent:Xl,RadioCardBody:Ql,RadioCardInput:zl,RadioCardPrice:Zl,SameHeightGroup:Jl,SameHeightItem:eu,Select:ys,SelectContext:ru,SelectCustom:fs,SelectDropdown:Es,SelectNativeHidden:ds,SelectOption:gs,SimpleHeader:vs,SimpleFooter:Ns,SrOnly:Ao,Static:Jo,Tag:As,Tab:Is,Tabs:hs,TabList:Rs,TabPanel:xs,Text:wo,useHeightResizeObserver:mo,useKeyboardListener:so,useResponsiveHeight:po,useWindowResize:co,useBodyHeightObserver:fo,Popover:Os,PopoverContent:Bs,PopoverTrigger:Ds};return Kt})(),e.exports=r(n(2),n(15))},function(e){"use strict";e.exports=m},function(e,t,n){"use strict";var r,a,i,o,l;for(i in n.r(t),n.d(t,{LightBoxNoname:function(){return o.LightBoxNoname},LightBoxNonameComponent:function(){return o.LightBoxNonameComponent}}),a={},r=n(17))"default"!==i&&(a[i]=function(e){return r[e]}.bind(0,i));for(i in n.d(t,a),o=n(18),a={},l=n(19))["default","LightBoxNoname","LightBoxNonameComponent"].indexOf(i)<0&&(a[i]=function(e){return l[e]}.bind(0,i));n.d(t,a)},function(){},function(e,t,n){"use strict";var r,a,i,o,l,u,s,c,m,d;n.r(t),n.d(t,{LightBoxNoname:function(){return d},LightBoxNonameComponent:function(){return c}}),r=n(1),a=n(2),i=n.n(a),o=n(14),l=n(13),u=n(3),s=n(8),c=function(e){var t=e.intl,n=e.setOmnitureOnBoxNameLightBox,l=(0,r.__read)((0,a.useState)(!1),2),u=l[0],s=l[1],c=function(e){s(e)};return i().createElement("div",null,i().createElement(o.Button,{variant:"textBlue",onClick:function(e){e.preventDefault(),c(!0),setTimeout(function(){n()},1e3)},size:"small",className:"payment-text-14 payment-leading-18"},t.formatMessage({id:"MODAL_NO_NAME"})),u&&i().createElement(o.Modal,{id:"no-name-on-card","aria-labelledby":"no-name-on-card-title",onEscapeKeyPressed:function(){return c(!1)},onOverlayClick:function(){return c(!1)}},i().createElement(o.ModalContent,{useDefaultRadius:!1,className:"payment-rounded-10"},i().createElement(o.ModalHeader,{variant:"lightGrayBar",rightButtonIcon:"default",title:t.formatMessage({id:"MODAL_NO_NAME_TITLE"}),isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-25",onRightButtonClicked:function(){return c(!1)},rightButtonLabel:t.formatMessage({id:"CTA_CLOSE"})}),i().createElement(o.ModalBody,{id:"no-name-on-card-body",isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-30"},i().createElement("div",{className:"payment-text-gray payment-text-14 payment-leading-18"},t.formatMessage({id:"MODAL_NO_NAME_DESC"}))))))},m=function(e){return{setOmnitureOnBoxNameLightBox:function(t){return e((0,s.OmnitureOnBoxNameLightBox)({data:t}))}}},d=(0,u.connect)(null,m)((0,l.injectIntl)(c))},function(){},function(){},function(e,t,n){"use strict";var r,a,i,o;for(i in n.r(t),a={},r=n(22))"default"!==i&&(a[i]=function(e){return r[e]}.bind(0,i));for(i in n.d(t,a),a={},o=n(23))"default"!==i&&(a[i]=function(e){return o[e]}.bind(0,i));n.d(t,a)},function(){},function(){}],d={};return e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,{a:n}),n},e.d=function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},e.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},e.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t={},function(){"use strict";function n(e,t){var n=t.filter(function(t){return t.Ban===e});return n&&n[0].TransactionId}function r(e,t,n){if(e==A.PaymentItemAccountType.Mobility)return t?n.MyBill:n.Mobility;switch(e){case A.PaymentItemAccountType.OneBill:return n.OneBill;case A.PaymentItemAccountType.TV:return n.TV;case A.PaymentItemAccountType.Internet:return n.Internet;case A.PaymentItemAccountType.HomePhone:return n.HomePhone;case A.PaymentItemAccountType.MobilityAndOneBill:return n.MobilityAndOneBill;case A.PaymentItemAccountType.SingleBan:return n.SingleBan;default:return"Unknown"}}function a(e){switch(e){case A.CreditCardType.VI:return"VI";case A.CreditCardType.MC:return"MC";case A.CreditCardType.AX:return"AX";default:return""}}function i(e){switch(e){case A.CreditCardType.VI:return"VISA";case A.CreditCardType.MC:return"Mastercard";case A.CreditCardType.AX:return"American Express";case A.CreditCardType.DC:return"Diners Club";default:return"Unknown"}}var o,l,u,s,c,m,d,p,f,b,E,_,y,g,v,N,A,C,h,T,I,R,x,S,O,M,L,D,P,B,k,w,U,F,H,Y,j,G,V,z,q,K,W,X,Q,Z,$,J,ee,te,ne,re,ae,ie,oe,le,ue,se,ce,me,de,pe,fe,be,Ee,_e,ye,ge,ve,Ne,Ae,Ce,he,Te,Ie,Re,xe,Se,Oe,Me,Le,De,Pe,Be,ke,we,Ue,Fe,He,Ye,je,Ge,Ve,ze,qe,Ke,We,Xe,Qe,Ze,$e,Je,et,tt,nt,rt,at,it,ot,lt,ut,st,ct,mt,dt,pt,ft,bt,Et,_t,yt,gt,vt,Nt,At,Ct,ht,Tt,It,Rt,xt,St,Ot,Mt,Lt,Dt,Pt,Bt,kt,wt,Ut,Ft,Ht,Yt,jt,Gt,Vt,zt,qt,Kt,Wt,Xt,Qt,Zt,$t,Jt,en,tn,nn,rn,an,on,ln,un,sn,cn,mn,dn,pn,fn,bn,En,_n,yn,gn,vn,Nn,An,Cn,hn,Tn,In,Rn,xn,Sn,On,Mn,Ln,Dn,Pn,Bn,kn;e.r(t),e.d(t,{default:function(){return Bn}}),o=e(1),l=e(2),u=e.n(l),s=e(3),c=e(4),m=e(5),d=e(6),p=c.CommonFeatures.BaseLocalization,f=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return(0,o.__extends)(n,t),Object.defineProperty(n.prototype,"defaultMessages",{get:function(){return e(7)},enumerable:!1,configurable:!0}),(0,o.__decorate)([c.Injectable],n)}(p),b=c.CommonFeatures.BaseConfig,E=c.CommonFeatures.configProperty,_=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,o.__extends)(t,e),(0,o.__decorate)([E("en"),(0,o.__metadata)("design:type",String)],t.prototype,"language",void 0),(0,o.__decorate)([E(c.LoggerSeverityLevel.All),(0,o.__metadata)("design:type",Number)],t.prototype,"logLevel",void 0),(0,o.__decorate)([E("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderForm/PreAuthorizePayment"),(0,o.__metadata)("design:type",String)],t.prototype,"createPaymentURL",void 0),(0,o.__decorate)([E("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderMultiForm/PreAuthorizePayment"),(0,o.__metadata)("design:type",String)],t.prototype,"createMultiPaymentURL",void 0),(0,o.__decorate)([E("B"),(0,o.__metadata)("design:type",String)],t.prototype,"brand",void 0),(0,o.__decorate)([E("BELLCAEXT"),(0,o.__metadata)("design:type",String)],t.prototype,"channel",void 0),(0,o.__decorate)([E("ON"),(0,o.__metadata)("design:type",String)],t.prototype,"province",void 0),(0,o.__decorate)([E("b14bc3rcGo"),(0,o.__metadata)("design:type",String)],t.prototype,"userID",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",String)],t.prototype,"CSRFToken",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",Array)],t.prototype,"getPaymentItem",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",String)],t.prototype,"pagetitle",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",Object)],t.prototype,"DTSTokenization",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",String)],t.prototype,"paymentApiUrl",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",Array)],t.prototype,"getBankList",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",Array)],t.prototype,"transactionIdArray",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",String)],t.prototype,"RedirectUrl",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",String)],t.prototype,"BankInfoUrl",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",String)],t.prototype,"currentUrl",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",String)],t.prototype,"selectedUpdatePaymentMethod",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",String)],t.prototype,"isCheckedBan",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",Array)],t.prototype,"creditCardAutopayOffers",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",Array)],t.prototype,"debitCardAutopayOffers",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",Array)],t.prototype,"removedSubscriberOffers",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",String)],t.prototype,"CancelApiUrl",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",String)],t.prototype,"IsInteracEnabled",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",String)],t.prototype,"IsAutopayCreditEnabled",void 0),(0,o.__decorate)([E(""),(0,o.__metadata)("design:type",String)],t.prototype,"userProfileProvince",void 0),(0,o.__decorate)([c.Injectable],t)}(b),y=_,g=e(8),function(e){e[e.IDLE=0]="IDLE",e[e.PENDING=1]="PENDING",e[e.COMPLETED=2]="COMPLETED",e[e.FAILED=3]="FAILED"}(v||(v={})),N=e(11),A=e(9),C=function(e,t){return e},h=function(e,t){return t.payload},T=function(e,t){var n=t.payload;return e&&n?(0,o.__assign)((0,o.__assign)({},e),{CreditCardNumber:n.CreditCardNumber}):e},I=function(e,t){var n=t.payload;return e&&n?(0,o.__assign)((0,o.__assign)({},e),{CardholderName:n.CardholderName}):e},R=function(e,t){var n=t.payload;return e&&n?(0,o.__assign)((0,o.__assign)({},e),{SecurityCode:n.SecurityCode}):e},x=function(e,t){var n=t.payload;return e&&n?(0,o.__assign)((0,o.__assign)({},e),{ExpireMonth:n.ExpireMonth,ExpireYear:n.ExpireYear}):e},S=function(e,t){switch(t.type){case A.CreditCardDetailsAction.SET_CREDIT_CARD_VALIDATION:return e.errors.map(function(e){return!!t.payload.errors.some(function(t){return t.field===e.field})}).filter(function(e){return!0===e}).length>0?(e.errors.map(function(e){return t.payload.errors.find(function(t){return t.field===e.field})?(0,o.__assign)((0,o.__assign)({},e),t.payload.errors):e}),e):(0,o.__assign)((0,o.__assign)({},e),{errors:(0,o.__spreadArray)((0,o.__spreadArray)([],(0,o.__read)(e.errors),!1),(0,o.__read)(t.payload.errors),!1)});case A.CreditCardDetailsAction.RESET_CREDIT_CARD_VALIDATION:return(0,o.__assign)((0,o.__assign)({},e),{errors:[]});default:return e}},O=function(){return function(e,t){var n=t.payload;return(0,o.__assign)((0,o.__assign)({},e),n)}},M=function(e){return function(t,n){return n.payload,e||v.IDLE}},L=function(){return function(e,t){var n=t.payload;return(0,o.__assign)((0,o.__assign)({},e),n)}},D=function(e){return function(t,n){return n.payload,e||v.IDLE}},P=function(){return function(e,t){var n=t.payload;return(0,o.__assign)((0,o.__assign)({},e),n)}},B=function(e){return function(t,n){return n.payload,e||v.IDLE}},k=function(){return function(e,t){var n=t.payload;return(0,o.__assign)((0,o.__assign)({},e),n)}},w=function(){return function(e,t){var n=t.payload;return(0,o.__assign)((0,o.__assign)({},e),n)}},U=function(e,t){return t.payload},F=function(){return function(e,t){var n=t.payload;return(0,o.__assign)((0,o.__assign)({},e),n)}},H=function(e){return function(t,n){return n.payload,e||v.IDLE}},Y=function(){return function(e,t){var n=t.payload;return(0,o.__assign)((0,o.__assign)({},e),n)}},j=function(e){return function(t,n){return n.payload,e||v.IDLE}},G=function(){return function(e,t){var n=t.payload;return(0,o.__assign)((0,o.__assign)({},e),n)}},V=function(e){return function(t,n){return n.payload,e||v.IDLE}},z=function(){return function(e,t){var n=t.payload;return(0,o.__assign)((0,o.__assign)({},e),n)}},q=function(){return function(e,t){var n=t.payload;return(0,o.__assign)((0,o.__assign)({},e),n)}},K=function(e){return function(t,n){return n.payload,e||v.IDLE}},W=function(e){return function(t,n){return n.payload,e||v.IDLE}},X=e(12),Q=c.CommonFeatures.BaseClient,Z=function(e){function t(t,n){var r=e.call(this,t)||this;return r.config=n,r}return(0,o.__extends)(t,e),Object.defineProperty(t.prototype,"options",{get:function(){return{cache:!1,credentials:"include",headers:{"Content-Type":"application/json",brand:this.config.brand,channel:this.config.channel,Province:this.config.province,userID:this.config.userID,"accept-language":this.config.language,"X-CSRF-TOKEN":this.config.CSRFToken}}},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"optionsOneBill",{get:function(){return{cache:!1,credentials:"include",headers:{"Content-Type":"application/json",brand:this.config.brand,channel:this.config.channel,Province:this.config.province,userID:this.config.userID,"accept-language":this.config.language,"X-CSRF-TOKEN":this.config.CSRFToken,PM:!0}}},enumerable:!1,configurable:!0}),t.prototype.createMultiOrderFormData=function(e,t,n,r){var a=t?this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(null)):this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(r)),i=this.getBanSpecificTransactionId(e),l=a+"?TransactionId=".concat(i,"&province=").concat(this.config.province),u=t?(0,o.__assign)({},this.optionsOneBill):(0,o.__assign)({},this.options);return this.post(l,{AccountInputValues:n},u)},t.prototype.validateMultiOrderForm=function(e,t,n,r,a,i,l){var u=t?this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(null)):this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(i)),s=this.getBanSpecificTransactionId(e),c=u+"/ValidatePayment?TransactionId=".concat(s,"&province=").concat(this.config.province),m=t?(0,o.__assign)({},this.optionsOneBill):(0,o.__assign)({},this.options);return this.post(c,a?{AccountInputValues:r,ValidatePADPACInput:{SelectedPaymentMethod:n.SelectedPaymentMethod,BankName:n.BankName,AccountNumber:n.AccountNumber,HolderName:n.HolderName,BankCode:n.BankCode,TransitCode:n.TransitCode}}:{AccountInputValues:r,ValidatePADPACInput:{SelectedPaymentMethod:n.SelectedPaymentMethod,CardholderName:n.CardholderName,CreditCardToken:l,CreditCardType:n.CreditCardType,ExpiryYear:n.ExpiryYear,ExpiryMonth:n.ExpiryMonth,SecurityCode:n.SecurityCode}},m)},t.prototype.submitMultiOrderForm=function(e,t,n,r,a,i,l){var u=t?this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(null)):this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(l)),s=this.getBanSpecificTransactionId(e),c="".concat(u,"/Submit?TransactionId=").concat(s,"&province=").concat(this.config.province),m=t?(0,o.__assign)({},this.optionsOneBill):(0,o.__assign)({},this.options);return function(e,t,n,r,a,i,o){var l,u,s,c,m=t?r:n,d=t?a:i,p=t?"D":"C";if(o&&Array.isArray(o))for(u=function(e){var t,n=o[e];return m?n.incentiveDiscountDetails=[]:(t=(null!==(l=null==d?void 0:d.filter(function(e){return e.Ban===n.accountNumber}))&&void 0!==l?l:[]).flatMap(function(e){var t;return null!==(t=e.AutopayEligibleSubscribers)&&void 0!==t?t:[]}).map(function(e){var t,n,r;return{mdn:null===(t=e.subscriberTelephoneNumber)||void 0===t?void 0:t.replace(/\D/g,""),autopayOffers:null!==(r=null===(n=e.autopayOffers)||void 0===n?void 0:n.map(function(e){var t,n,r;return{newDiscountAmount:null!==(t=e.currentdiscountAmount)&&void 0!==t?t:0,currentDiscountAmount:null!==(n=e.discountAmount)&&void 0!==n?n:0,offerImpact:null!==(r=e.offerImpact)&&void 0!==r?r:""}}))&&void 0!==r?r:[]}}),n.incentiveDiscountDetails=[{autopayEligibleSubscribers:t,selectedPaymentMethod:p}]),{value:n}},s=0;s<o.length;s++)if("object"==typeof(c=u(s)))return c.value}(0,n,r,a,this.config.debitCardAutopayOffers,this.config.creditCardAutopayOffers,i),this.post(c,{AccountInputValues:i},m)},t.prototype.getPassKeyRepsonse=function(e){var t=this.config.paymentApiUrl+"".concat(e.payload.ban,"/").concat(e.payload.sub,"/payment/CreditCard/PassKey"),n=(0,o.__assign)({},this.options);return this.get(t,null,n)},t.prototype.getBanSpecificTransactionId=function(e){var t=this.config.transactionIdArray.filter(function(t){return t.Ban===e});return t&&t[0].TransactionId},t.prototype.getRedirectUrl=function(){var e,t,n,r,a=this.config.RedirectUrl,i=(null===(t=null===(e=this.config)||void 0===e?void 0:e.currentUrl)||void 0===t?void 0:t.substring(0,(null===(r=null===(n=this.config)||void 0===n?void 0:n.currentUrl)||void 0===r?void 0:r.lastIndexOf("/"))+1))+"GetFieldModifyViewManage",l=(0,o.__assign)({},this.options);return this.post(a,{OneTimeCode:"",RedirectUrl:i},l)},t.prototype.getInteracBankInfo=function(e){var t,n,r,a,i=this.config.BankInfoUrl,l=(0,o.__assign)({},this.options),u=(null===(n=null===(t=this.config)||void 0===t?void 0:t.currentUrl)||void 0===n?void 0:n.substring(0,(null===(a=null===(r=this.config)||void 0===r?void 0:r.currentUrl)||void 0===a?void 0:a.lastIndexOf("/"))+1))+"GetFieldModifyViewManage";return this.post(i,{RedirectUrl:u,OneTimeCode:e},l)},t.prototype.cancelPreauth=function(e){var t=this.config.CancelApiUrl,n=(0,o.__assign)({},this.options);return this.post(t,e,n)},(0,o.__decorate)([c.Injectable,(0,o.__metadata)("design:paramtypes",[c.AjaxServices,y])],t)}(Q),$=function(){function e(e,t){this.client=e,this.config=t}return e.prototype.combineEpics=function(){return(0,N.combineEpics)(this.createMultiPaymentEpic,this.validateMultiOrderPaymentEpic,this.submitMultiOrderPaymentEpic,this.tokenizeAndPropagateFormValues,this.fetchPassKey,this.getRedirectUrl,this.getInteracBankInfo,this.cancelPreauthPaymentsEpic)},Object.defineProperty(e.prototype,"tokenizeAndPropagateFormValues",{get:function(){var e=this;return function(t,n){return t.ofType(g.tokenizeAndPropagateFormValues.toString()).mergeMap(function(t){return(r=e.config.DTSTokenization,a=n.getState().passKey,i=new DTSTokenizationPlugin,o=r.consumerId,l=r.applicationId,u=r.systemTransactionID,s=r.userID,c=r.timeout,i.setUserID(s),i.setSystemTransactionID(u),i.setApplicationID(l),i.setConsumerID(o),i.setPassKey(a),i.setPanElementID("card-number"),i.setTimeout(c),X.Observable.create(function(e){i.setSuccessHandler(function(t,n){return e.next(t),e.complete()}),i.setErrorHandler(function(t){return e.error(t),e.complete()}),i.tokenize()})).mergeMap(function(e){return[(0,g.getPassKey)({ban:t.payload.BillName,sub:t.payload.subscriberId}),(0,g.cardTokenizationSuccess)(e.token)]}).catch(function(e){return[(0,g.cardTokenizationError)("string"==typeof e&&e.length>0?e:"TOKENIZATIONERROR")]});var r,a,i,o,l,u,s,c}).catch(function(e){return[(0,g.cardTokenizationError)("TOKENIZATIONERROR")]})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fetchPassKey",{get:function(){var e=this;return function(t,n){return t.ofType(g.getPassKey.toString()).mergeMap(function(t){return e.client.getPassKeyRepsonse(t).map(function(e){var t;return(0,g.setPassKey)(null===(t=null==e?void 0:e.data)||void 0===t?void 0:t.PassKey)})}).catch(function(e){return[(0,g.cardTokenizationError)("TOKENIZATIONERROR")]})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"createMultiPaymentEpic",{get:function(){var e=this;return function(t,n){return t.ofType(g.createMultiPaymentAction.toString()).mergeMap(function(t){var n=t.payload;return e.client.createMultiOrderFormData(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.details,null==n?void 0:n.sub).map(function(e){var t=e.data;return(0,g.createMultiPaymentCompleted)(t)}).catch(function(e){return X.Observable.of((0,o.__assign)((0,o.__assign)({},(0,g.createMultiPaymentFailed)(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"validateMultiOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.ofType(g.validateMultiOrderPaymentAction.toString()).mergeMap(function(t){var r=t.payload;return e.client.validateMultiOrderForm(null==r?void 0:r.ban,null==r?void 0:r.type,null==r?void 0:r.details,null==r?void 0:r.accountInputValue,null==r?void 0:r.isBankPaymentSelected,null==r?void 0:r.sub,n.getState().cardTokenizationSuccess).map(function(e){var t=e.data;return(0,g.validateMultiOrderPaymentActionCompleted)(t)}).catch(function(e){return X.Observable.of((0,o.__assign)((0,o.__assign)({},(0,g.validateMultiOrderPaymentActionFailed)(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"submitMultiOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.ofType(g.submitMultiOrderPaymentAction.toString()).mergeMap(function(t){var n=t.payload;return e.client.submitMultiOrderForm(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.isbankSelected,null==n?void 0:n.sorryCredit,null==n?void 0:n.sorryDebit,null==n?void 0:n.details,null==n?void 0:n.sub).map(function(e){var t=e.data;return t.length>0&&t.find(function(e){return"Confirmation"===e.OrderFormStatus})?(0,g.submitMultiOrderPaymentActionCompleted)(t):(0,g.submitMultiOrderPaymentActionFailed)({error:!0})}).catch(function(e){return X.Observable.of((0,o.__assign)((0,o.__assign)({},(0,g.submitMultiOrderPaymentActionFailed)(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"getRedirectUrl",{get:function(){var e=this;return function(t,n){return t.ofType(g.getRedirectUrl.toString()).mergeMap(function(t){return e.client.getRedirectUrl().map(function(e){var t=e.data;return(0,g.redirectUrlSuccess)(t)}).catch(function(e){return X.Observable.of((0,o.__assign)((0,o.__assign)({},(0,g.redirectUrlFailure)(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"getInteracBankInfo",{get:function(){var e=this;return function(t,n){return t.ofType(g.getInteracBankInfo.toString()).mergeMap(function(t){var r=t.payload;return n.dispatch((0,g.setIsLoading)(!0)),e.client.getInteracBankInfo(null==r?void 0:r.code).map(function(e){var t=e.data;return n.dispatch((0,g.setIsLoading)(!1)),(0,g.interacBankInfoSuccess)(t)}).catch(function(e){return n.dispatch((0,g.setIsLoading)(!1)),X.Observable.of((0,o.__assign)((0,o.__assign)({},(0,g.interacBankInfoFailure)(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cancelPreauthPaymentsEpic",{get:function(){var e=this;return function(t,n){return t.ofType(g.cancelPreauthAction.toString()).mergeMap(function(t){var r=t.payload;return n.dispatch((0,g.setIsLoading)(!0)),e.client.cancelPreauth(null==r?void 0:r.bans).map(function(e){var t,r=e.data;return r.length>0&&(t=r.filter(function(e){return e.success}))&&t.length>0?(n.dispatch((0,g.setIsLoading)(!1)),(0,g.cancelPreauthSuccessAction)(r)):(n.dispatch((0,g.setIsLoading)(!1)),(0,g.cancelPreauthFailureAction)({error:!0}))}).catch(function(e){return n.dispatch((0,g.setIsLoading)(!1)),X.Observable.of((0,o.__assign)((0,o.__assign)({},(0,g.cancelPreauthFailureAction)(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),(0,o.__decorate)([c.Injectable,(0,o.__metadata)("design:paramtypes",[Z,y])],e)}(),J=c.CommonFeatures.BaseStore,ee=(0,c.CommonFeatures.actionsToComputedPropertyName)(g),te=ee.setConfig,ne=ee.getConfig,re=ee.onCreditCardNumberChange,ae=ee.onCardHolderNameChange,ie=ee.onCreditCardExpiryDateChange,oe=ee.onSecurityCodeChange,le=ee.setValidationErrors,ue=ee.resetValidationErrors,se=ee.createPaymentAction,ce=ee.createPaymentCompleted,me=ee.createPaymentFailed,de=ee.validateOrderPaymentAction,pe=ee.validateOrderPaymentActionCompleted,fe=ee.validateOrderPaymentActionFailed,be=ee.submitOrderPaymentAction,Ee=ee.submitOrderPaymentActionCompleted,_e=ee.submitOrderPaymentActionFailed,ye=ee.createMultiPaymentAction,ge=ee.createMultiPaymentCompleted,ve=ee.createMultiPaymentFailed,Ne=ee.validateMultiOrderPaymentAction,Ae=ee.validateMultiOrderPaymentActionCompleted,Ce=ee.validateMultiOrderPaymentActionFailed,he=ee.submitMultiOrderPaymentAction,Te=ee.submitMultiOrderPaymentActionCompleted,Ie=ee.submitMultiOrderPaymentActionFailed,Re=ee.setPassKey,xe=ee.cardTokenizationError,Se=ee.cardTokenizationSuccess,Oe=ee.tokenizeAndPropagateFormValues,Me=ee.redirectUrlSuccess,Le=ee.interacBankInfoSuccess,De=ee.setIsLoading,Pe=ee.interacBankInfoFailure,Be=ee.cancelPreauthAction,ke=ee.cancelPreauthSuccessAction,we=ee.cancelPreauthFailureAction,Ue=function(e){function t(t,n,r,a){var i=e.call(this,t)||this;return i.config=n,i.localization=r,i.epics=a,i}return(0,o.__extends)(t,e),Object.defineProperty(t.prototype,"reducer",{get:function(){var e,t,n,r,a,i,o,l,u,s,c,p,f,b,E,_,y,g,N,X,Q,Z,$,J,ee;return(0,m.combineReducers)({localization:this.localization.createReducer(),config:(0,d.handleActions)((e={},e[te]=h,e[ne]=C,e),this.config),creditCardDetails:(0,d.handleActions)((t={},t[re]=T,t[ae]=I,t[oe]=R,t[ie]=x,t),A.CCDetailsDefault),validationErrors:(0,d.handleActions)((n={},n[le]=S,n[ue]=S,n),{errors:[]}),createPaymentStatus:(0,d.handleActions)((r={},r[se]=M(v.PENDING),r[me]=M(v.FAILED),r[ce]=M(v.COMPLETED),r),v.IDLE),createPayment:(0,d.handleActions)((a={},a[ce]=O(),a),{}),validateOrderFormStatus:(0,d.handleActions)((i={},i[de]=D(v.PENDING),i[fe]=D(v.FAILED),i[pe]=D(v.COMPLETED),i),v.IDLE),validateOrderPayment:(0,d.handleActions)((o={},o[pe]=L(),o),{}),submitOrderFormStatus:(0,d.handleActions)((l={},l[be]=B(v.PENDING),l[_e]=B(v.FAILED),l[Ee]=B(v.COMPLETED),l),v.IDLE),submitOrderPayment:(0,d.handleActions)((u={},u[Ee]=P(),u),{}),createMultiPaymentStatus:(0,d.handleActions)((s={},s[ye]=H(v.PENDING),s[ve]=H(v.FAILED),s[ge]=H(v.COMPLETED),s),v.IDLE),createMultiPayment:(0,d.handleActions)((c={},c[ge]=F(),c),{}),validateMultiOrderFormStatus:(0,d.handleActions)((p={},p[Ne]=j(v.PENDING),p[Ce]=j(v.FAILED),p[Ae]=j(v.COMPLETED),p),v.IDLE),validateMultiOrderPayment:(0,d.handleActions)((f={},f[pe]=Y(),f),{}),submitMultiOrderFormStatus:(0,d.handleActions)((b={},b[he]=V(v.PENDING),b[Ie]=V(v.FAILED),b[Te]=V(v.COMPLETED),b),v.IDLE),submitMultiOrderPayment:(0,d.handleActions)((E={},E[Te]=G(),E),{}),passKey:(0,d.handleActions)((_={},_[Re]=function(e,t){return t.payload||e},_),""),cardTokenizationError:(0,d.handleActions)((y={},y[xe]=function(e,t){return t.payload},y),""),cardTokenizationSuccess:(0,d.handleActions)((g={},g[Se]=function(e,t){return t.payload},g),""),tokenizeAndPropagateFormValuesStatus:(0,d.handleActions)((N={},N[Oe]=W(v.PENDING),N[xe]=W(v.FAILED),N[Se]=W(v.COMPLETED),N),v.IDLE),redirectUrl:(0,d.handleActions)((X={},X[Me]=k(),X),{status:"",externalRedirectUrl:""}),interacBankInfo:(0,d.handleActions)((Q={},Q[Le]=w(),Q),{status:"",bankAccountNumber:"",transitNumber:"",bankCode:"",accountHolderName:""}),interactBankFailureInfo:(0,d.handleActions)((Z={},Z[Pe]=z(),Z),{}),isLoading:(0,d.handleActions)(($={},$[De]=U,$),!1),cancelPreauthStatus:(0,d.handleActions)((J={},J[Be]=K(v.PENDING),J[ke]=K(v.COMPLETED),J[we]=K(v.FAILED),J),v.IDLE),cancelPreauthPayments:(0,d.handleActions)((ee={},ee[ke]=q(),ee),[])})},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return(0,N.combineEpics)(this.epics.combineEpics())},enumerable:!1,configurable:!0}),(0,o.__decorate)([c.Injectable,(0,o.__metadata)("design:paramtypes",[c.Store,y,f,$])],t)}(J),Fe=Ue,He=e(13),Ye=e(14),je=(0,l.forwardRef)(function(e,t){var n=e.id,r=e.name,a=e.checked,i=e.label,o=e.headingLevel,l=void 0===o?"h4":o,s=e.children,c=e.ariaDescribe,m=e.onChange;return u().createElement(u().Fragment,null,u().createElement("div",{className:"brui-rounded-20 brui-border brui-border-gray-8 transition-all brui-mb-15 brui-drop-shadow-none has-[input[type=radio].brui-size-full:checked]:payment-shadow-3sm"},u().createElement("label",{className:"payment-peer/paymentradio payment-group/paymentradio brui-flex brui-items-center brui-p-30 payment-pl-15 sm:payment-pl-30 brui-cursor-pointer"},u().createElement(Ye.RadioButton,{id:n,name:r,value:i,variant:"default",checked:a,"aria-describedby":c||void 0,ref:t,onChange:m},u().createElement("div",{className:"brui-relative brui-flex brui-justify-between"},u().createElement(Ye.Heading,{level:l,variant:"default",id:n+"-label",className:"brui-font-sans brui-mb-0 brui-text-16 brui-leading-20 brui-mt-3 sm:brui-mr-64 payment-text-gray group-has-[:checked]/paymentradio:payment-text-black"},i)))),u().createElement("div",{className:"payment-hidden peer-has-[input[type=radio]:checked]/paymentradio:payment-block payment-px-15 payment-pb-30 sm:payment-px-30"},u().createElement(Ye.Divider,{direction:"horizontal",width:1,className:"brui-mb-30 brui-bg-gray-4"}),s)))}),Ge=(0,l.forwardRef)(function(e,t){var n=e.id,r=e.label,a=e.name,i=e.describe,o=e.defaultChecked,l=void 0!==o&&o,s=e.isInterac,c=e.headingLevel,m=void 0===c?"h4":c,d=e.children,p=e.onChange,f=e.interactIconPath;return u().createElement(u().Fragment,null,u().createElement(Ye.RadioCard,{className:"payment-manage-radio-bank brui-border brui-border-gray-3 payment-py-30 sm:!brui-px-30 payment-group/radiocard",id:n,name:a,defaultChecked:l,"aria-labelledby":"label-"+n,"aria-describedby":i?"desc-"+n:"",radioPlacement:"topLeft",ref:t,onChange:p},u().createElement("div",{className:"payment-pl-[34px] sm:payment-pr-[30px] brui-relative brui-flex brui-justify-between"},u().createElement(Ye.Heading,{level:m,variant:"default",className:"brui-font-sans group-has-[input[type=radio]:checked]/radiocard:payment-font-bold brui-mb-10 sm:brui-mb-5 brui-text-16 brui-leading-20 payment-mt-5",id:"label-"+n},r),s&&u().createElement("img",{alt:"",className:"payment-ml-5 payment-relative sm:payment-absolute payment-right-0 payment-top-0 sm:payment-w-40 sm:payment-h-40 payment-w-32 payment-h-32",src:f})),u().createElement("div",{className:"sm:payment-pl-[34px] sm:payment-pr-[45px]"},i&&u().createElement(Ye.Text,{id:"desc-"+n,elementType:"p",className:"brui-text-14 brui-leading-18 brui-text-gray"},i),u().createElement("div",{className:"brui-z-10 brui-relative"},u().createElement("div",{className:"payment-hidden group-has-[input[type=radio]:checked]/radiocard:payment-block"},d)))))}),Ve=e(16),ze=function(e){e.intl,e.isErrorCase;var t=e.children,n=e.legends,r=(e.isBankPayment,e.isCreditCardPayment,e.srOnly),a=(0,o.__read)((0,l.useState)(function(){var e=!1;return l.Children.forEach(t,function(t){l.isValidElement(t)&&t.props&&t.props.defaultChecked&&t.props.showBankFieldsOnChange&&(e=!0)}),e}),2),i=a[0],u=a[1],s=function(e){u(e)};return l.createElement("fieldset",null,l.createElement("legend",{className:"brui-sr-only"},r),l.createElement("div",{className:"brui-flex brui-flex-col"},l.createElement("div",{className:"brui-flex brui-flex-col sm:brui-flex-row"},l.createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] sm:payment-mr-30 sm:payment-text-right payment-pt-13 payment-pb-13 sm:payment-pb-0 payment-mb-10 sm:payment-mb-0"},l.createElement(Ye.Label,{className:"brui-block",required:!0},n)),l.createElement("div",{className:"brui-flex brui-flex-col payment-gap-15"},l.Children.map(t,function(e,t){return l.cloneElement(e,{showOnChange:s,childIndex:t})})))),i)},qe=(0,l.forwardRef)(function(e,t){var n=e.name,r=void 0===n?"bank-payment-radio":n,a=e.value,i=e.hasError,o=(e.showOnChange,e.showBankFieldsOnChange,e.childIndex),u=e.defaultChecked,s=e.className,c=e.label,m=e.onChange,d=e.idPrefix,p=void 0===d?"":d,f=e.getExistingBankPaymentDetails,b=e.paymentDetails;return l.useEffect(function(){b&&u&&f(b||[])},[]),l.createElement(Ye.RadioButton,{className:[s,"brui-flex brui-items-center brui-absolute brui-size-full brui-opacity-0 enabled:brui-cursor-pointer disabled:brui-cursor-default brui-z-10"].join(" ").trim(),id:p+"bank-payment-radio-id-"+o,name:r,value:a,variant:"boxedInMobile",hasError:i,defaultChecked:u,ref:t,onChange:m,onClick:function(){"function"==typeof f&&f(b||[])}},l.createElement("div",{className:"brui-text-14 brui-leading-18 brui-mt-3",dangerouslySetInnerHTML:{__html:c}}))}),Ke=function(e){var t;if(13===e.length||16===e.length){if(t=new RegExp("^4"),null!=e.match(t))return"VI";if(t=new RegExp("^(5[1-5]|2(22[1-9]|2[3-9][0-9]|[3-6][0-9]{2}|7[01][0-9]|720))"),null!=e.match(t)&&16===e.length)return"MC"}else if(15===e.length&&(t=new RegExp("^(34|37)"),null!=e.match(t)))return"AX";return""},We={VISA:/^4/,MASTERCARD:/^(5[1-5]|2[2-7])/,AMEX:/^3[47]/},Xe=/^(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?\s)+(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{1}\.?\s)*[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?$/,Qe=function(e){return We.VISA.test(e)?"VISA":We.MASTERCARD.test(e)?"MASTERCARD":We.AMEX.test(e)?"AMEX":"default"},Ze={mobile:{maxHeight:"374px"},tablet:{maxHeight:"259px"},desktop:{maxHeight:"259px"}},$e="INTERNAL_SERVER_ERROR",Je=(0,l.forwardRef)(function(e,t){var n=e.intl,r=e.isCreditCardPayment,a=void 0!==r&&r,o=e.isBankPayment,l=void 0!==o&&o,s=e.isPreAuth,c=void 0!==s&&s,m=e.paymentItems,d=e.onChange,p=e.getExistingBankPaymentDetails,f=a?n.formatMessage({id:"EXISTING_CC_TITLE"}):l?n.formatMessage({id:"EXISTING_BANK_TITLE"}):null,b="".concat(n.formatMessage({id:"SELECT_REQUIRED_LEGEND"})," ").concat(f," ");return u().createElement(u().Fragment,null,a?u().createElement(u().Fragment,null,u().createElement(ze,{isCreditCardPayment:!0,legends:f,srOnly:b},m.filter(function(e){return e.IsOnPreauthorizedPayments&&null!=e.CreditCardDetails}).map(function(e,r){return e.CreditCardDetails&&u().createElement(qe,{idPrefix:"PACC",name:"credit-card-radio",value:i(e.CreditCardDetails.CreditCardType),label:"".concat(i(e.CreditCardDetails.CreditCardType),"<br>************").concat(e.CreditCardDetails.CreditCardNumberMasked,", ").concat(n.formatMessage({id:"CREDIT_CARD_VALID"})," ").concat(e.CreditCardDetails.ExpireYear),defaultChecked:!!c||void 0,ref:t,onChange:d})}),u().createElement(qe,{idPrefix:"PACC",name:"credit-card-radio",value:n.formatMessage({id:"NEW_CREDIT_ACCOUNT_LABEL"}),label:n.formatMessage({id:"NEW_CREDIT_ACCOUNT_LABEL"}),onChange:d,defaultChecked:!c&&void 0}))):l?u().createElement(u().Fragment,null,u().createElement(ze,{isBankPayment:!0,legends:f,srOnly:""},m.filter(function(e){return e.IsOnPreauthorizedPayments&&null!=e.BankAccountDetails}).map(function(e,n){return e.BankAccountDetails&&u().createElement(qe,{idPrefix:"PAD",name:"bank-card-details-radio",value:e.BankAccountDetails.BankName,label:"".concat(e.BankAccountDetails.BankName,"<br>(").concat(e.BankAccountDetails.AccountNumberMaskedDisplayView,")"),defaultChecked:n<=0||void 0,ref:t,onChange:d,getExistingBankPaymentDetails:p,paymentDetails:new Array(e)})}),u().createElement(qe,{idPrefix:"PAD",name:"bank-card-details-radio",value:n.formatMessage({id:"BANK_NEW_BANK_ACCOUNT_LABEL"}),label:n.formatMessage({id:"BANK_NEW_BANK_ACCOUNT_LABEL"}),onChange:d,getExistingBankPaymentDetails:p}))):null)}),et=(0,He.injectIntl)(Je),tt=function(e){e.target.value=e.target.value.replace(/[^0-9]/gi,"")},nt=function(){return Array.from({length:12},function(e,t){var n=t+1;return t<9&&(n="0"+(n=t+1)),n})},rt=function(){var e,t=parseInt((new Date).getFullYear().toString().substring(2)),n=t+9,r=[];for(e=t;e<=n;e++)r.push(e);return r},at={greatNews:{className:"brui-text-15 brui-text-blue",iconClass:"bi_brui",iconName:"bi_tag_note-big"},notifCardWarning:{className:"brui-text-20 brui-text-yellow",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"},notifCardInfoAlert:{className:"brui-text-20 brui-text-blue",iconClass:"bi_brui",iconName:"bi_info_notif_small"}},it=function(e){var t=e.hasNotifCard,n=void 0!==t&&t,r=e.children,a=e.label,i=e.label1,o=e.label2,l=e.label3,s=e.variant,c=e.id;return u().createElement(Ye.Card,{variant:"gray",radius:!0,className:["payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-30",n?"":"payment-hidden"].join(" ").trim()},u().createElement("div",{className:"payment-flex payment-size-20 payment-items-center payment-justify-center payment-self-start sm:payment-self-start "},u().createElement(Ye.Icon,{className:["",at[s].className].join(" ").trim(),iconClass:["",at[s].iconClass].join(" ").trim(),iconName:["",at[s].iconName].join(" ").trim()})),u().createElement("div",{id:c||"discount-offer",className:"payment-flex-grow"},u().createElement("p",{className:"brui-text-14 brui-leading-18 brui-text-gray brui-mb-10"},u().createElement("span",{className:"payment-font-bold payment-text-black"},a," "),u().createElement("span",{className:"payment-text-black"},i)),r,u().createElement("div",{className:"payment-text-12 payment-text-gray payment-leading-14"},u().createElement("strong",null,o),l)))},ot=(0,l.forwardRef)(function(e,t){var n,r,a,i,l,s,c,m,d,p,f,b,E,_,y,g,v,N,A,C,h,T,I,R=e.intl,x=e.Checked,S=e.onChange,O=e.errorBankAccountHolderName,M=e.isInteracSelected,L=e.radioCardRef,D=e.handleBankRadioManualDetailsChange,P=e.isBankManualEnterDetails,B=e.isPreauth,k=e.hasBankAccountDetails,w=e.bankitems,U=e.handleBankRadioChange,F=e.bankListInterac,H=(e.handleInteracSubmit,e.isBankChecked),Y=e.inputRefs,j=e.errorBankName,G=e.errorBankTransit,V=e.errorBankAccountNumber,z=e.radioRef,q=e.bankList,K=e.redirectUrl,W=e.interacBankInfo,X=e.checkedBillItems,Q=e.interactBankFailureInfo,Z=e.creditCardAutopayOffers,$=e.debitCardAutopayOffers,J=e.language,ee=e.managePreauth,te=e.IsAutopayCreditEnabled,ne=e.IsInteracEnabled,re=(0,o.__read)(u().useState({bankAccountHolderName:W.accountHolderName,bankAccountNumber:W.bankAccountNumber,bankTransitCode:W.transitNumber,bankCode:W.bankCode}),2),ae=re[0],ie=re[1],oe=function(){var e=[];return $&&(null==$||$.map(function(t){X&&X.map(function(n){t.Ban==n.BillName&&e.push(t)})})),e},le={label:R.formatMessage({id:"PAYMENT_METHOD"}),credits:w&&w.length>1?oe():$},ue=function(){return w&&w.length>1?oe().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):$&&$.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)},se=null!==ee?R.formatMessage({id:"UPDATE_BANK_ACCOUNT_LABEL"}):R.formatMessage({id:"BANK_ACCOUNT_LABEL"});return u().useEffect(function(){null!=W&&"SUCCESS"===W.status&&ie({bankAccountHolderName:W.accountHolderName,bankAccountNumber:W.bankAccountNumber,bankTransitCode:W.transitNumber,bankCode:W.bankCode})},[W]),u().useEffect(function(){null!=Q&&"error"==Q.dataType&&Q.data.includes($e)&&(L.manualDetails&&L.manualDetails.current&&(L.manualDetails.current.checked=!0),L.interac&&L.interac.current&&(L.interac.current.checked=!1))},[Q]),l=(0,o.__read)(u().useState({bankAccountHolder:"",bankAccountNumber:"",bankAccountNumberMasked:"",bankTransit:"",bankCode:""}),2),s=l[0],c=l[1],m=(0,o.__read)(u().useState([]),2),d=m[0],p=m[1],f=function(e){p(e)},u().useEffect(function(){d.length>0&&!M?d.map(function(e){c(function(t){var n,r,a,i,l;return(0,o.__assign)((0,o.__assign)({},t),{bankAccountHolder:(null===(n=e.BankAccountDetails)||void 0===n?void 0:n.CardHolder)||"",bankAccountNumber:(null===(r=e.BankAccountDetails)||void 0===r?void 0:r.AccountNumber)||"",bankAccountNumberMasked:(null===(a=e.BankAccountDetails)||void 0===a?void 0:a.AccountNumberMasked)||"",bankTransit:(null===(i=e.BankAccountDetails)||void 0===i?void 0:i.TransitCode)||"",bankCode:(null===(l=e.BankAccountDetails)||void 0===l?void 0:l.BankCode)||""})})}):0!=d.length||M||c(function(e){return(0,o.__assign)((0,o.__assign)({},e),{bankAccountHolder:"",bankAccountNumber:"",bankAccountNumberMasked:"",bankTransit:"",bankCode:""})})},[d]),b=function(e){var t=e.target.value;c((0,o.__assign)((0,o.__assign)({},s),{bankAccountHolder:t}))},E=function(e){var t=e.target.value;c((0,o.__assign)((0,o.__assign)({},s),{bankTransit:t}))},_=function(e){var t=e.target.value;c((0,o.__assign)((0,o.__assign)({},s),{bankAccountNumber:t}))},y=function(e){var t=e.target.value;c((0,o.__assign)((0,o.__assign)({},s),{bankAccountNumberMasked:t,bankAccountNumber:t}))},g=function(){var e;return(null===(e=null==le?void 0:le.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"INCREASE"===e.offerImpact})})}))||!1},v=Object.values(W).every(function(e){return""===e}),N=R.formatMessage({id:"CTA_INTERAC"}),A=R.formatMessage({id:"CTA_INTERAC_SR"}),C=null===(n=null==le?void 0:le.credits)||void 0===n?void 0:n.reduce(function(e,t){return t.AutopayEligibleSubscribers.forEach(function(n){e[t.banInfo.nickName]||(e[t.banInfo.nickName]=[]),n.autopayOffers.forEach(function(r){"RETAIN"!==r.offerImpact&&e[t.banInfo.nickName].push({phoneNumber:n.subscriberTelephoneNumber,discountAmount:r.discountAmount})})}),e},{}),h=null===(r=null==le?void 0:le.credits)||void 0===r?void 0:r.reduce(function(e,t){return t.AutopayEligibleSubscribers.forEach(function(n){e[t.banInfo.nickName]||(e[t.banInfo.nickName]=[]),n.autopayOffers.forEach(function(r){"RETAIN"!==r.offerImpact&&e[t.banInfo.nickName].push({phoneNumber:n.subscriberTelephoneNumber,currentDiscountAmount:r.currentdiscountAmount,discountAmount:r.discountAmount})})}),e},{}),T=null===(a=null==le?void 0:le.credits)||void 0===a?void 0:a.reduce(function(e,t){return t.AutopayEligibleSubscribers.forEach(function(n){e[t.banInfo.nickName]||(e[t.banInfo.nickName]=[]),n.autopayOffers.forEach(function(r){"RETAIN"!==r.offerImpact&&e[t.banInfo.nickName].push({phoneNumber:n.subscriberTelephoneNumber,currentDiscountAmount:r.currentdiscountAmount,discountAmount:r.discountAmount})})}),e},{}),I=null===(i=null==le?void 0:le.credits)||void 0===i?void 0:i.reduce(function(e,t){return t.AutopayEligibleSubscribers.forEach(function(n){e[t.banInfo.nickName]||(e[t.banInfo.nickName]=[]),n.autopayOffers.forEach(function(r){"RETAIN"!==r.offerImpact&&e[t.banInfo.nickName].push({phoneNumber:n.subscriberTelephoneNumber,discountAmount:r.discountAmount})})}),e},{}),u().createElement("div",{className:"brui-mb-15"},u().createElement(je,{id:"payment-radio-bank",name:"payment-radio",label:se,headingLevel:"h3",checked:x,ref:t,onChange:S},te&&u().createElement("div",null,function(){var e;return(null===(e=null==le?void 0:le.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return("GAIN"===e.offerImpact||""===e.offerImpact||null===e.offerImpact)&&"RETAIN"!==e.offerImpact})})}))||!1}()?u().createElement(it,{id:"Bank-discount-offer",hasNotifCard:Object.keys(C).length>0,variant:"greatNews",label:R.formatMessage({id:"GREAT_NEWS"}),label1:ue()>1?R.formatMessage({id:"LABEL_LOADED_OFFERS_DEBIT_TITLE"}):R.formatMessage({id:"LABEL_LOADED_OFFER_DEBIT_TITLE"}),label2:R.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING"}),label3:ue()>1?R.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE"}):R.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE"})},Object.entries(C).map(function(e,t){var n=(0,o.__read)(e,2),r=n[0],a=n[1];return u().createElement("div",{key:r},w&&w.length>1&&u().createElement("p",{id:"list-title-".concat(t),className:"payment-text-14 payment-text-gray payment-mb-5"},r,":"),u().createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10"},a.map(function(e,t){return u().createElement(Ye.ListItem,{key:t,className:"payment-text-14 payment-text-gray payment-mb-5 payment-leading-18"},e.phoneNumber," - ",u().createElement(Ye.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!0,price:e.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:J}))})))})):null,g()?u().createElement(it,{id:"Bank-discount-offer",hasNotifCard:Object.keys(h).length>0,variant:"greatNews",label:R.formatMessage({id:"GREAT_NEWS"}),label1:Object.keys(h).length>1?R.formatMessage({id:"LABEL_LOADED_OFFERS_INCREASED_CREDIT_TITLE"}):R.formatMessage({id:"LABEL_LOADED_OFFER_INCREASED_CREDIT_TITLE"})},Object.entries(h).map(function(e){var t=(0,o.__read)(e,2),n=t[0],r=t[1];return u().createElement("div",{key:n},w&&w.length>1&&u().createElement("p",{className:"payment-text-14 payment-text-gray payment-mb-5"},n,":"),u().createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10"},r.map(function(e,t){return u().createElement(Ye.ListItem,{key:t,className:"payment-text-14 payment-text-gray payment-mb-5 payment-leading-18"},e.phoneNumber," - from",u().createElement(Ye.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!0,price:e.currentDiscountAmount,variant:"defaultPrice",suffixText:"perMonth",language:J}),"to",u().createElement(Ye.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!0,price:e.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:J}))})))})):null,function(){var e;return(null===(e=null==le?void 0:le.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"REDUCE"===e.offerImpact})})}))||!1}()?u().createElement(it,{id:"Bank-discount-offer",hasNotifCard:Object.keys(T).length>0,variant:"notifCardWarning",label:Object.keys(T).length>1?R.formatMessage({id:"LABEL_LOADED_OFFERS_REDUCED_DEBIT_TITLE"}):R.formatMessage({id:"LABEL_LOADED_OFFER__REDUCED_DEBIT_TITLE"})},Object.entries(T).map(function(e){var t=(0,o.__read)(e,2),n=t[0],r=t[1];return u().createElement("div",{key:n},w&&w.length>1&&u().createElement("p",{className:"payment-text-14 payment-text-gray payment-mb-5"},n,":"),u().createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10"},r.map(function(e,t){return u().createElement(Ye.ListItem,{key:t,className:"payment-text-14 payment-text-gray payment-mb-5 payment-leading-18"},e.phoneNumber," - from",u().createElement(Ye.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!0,price:e.currentDiscountAmount,variant:"defaultPrice",suffixText:"perMonth",language:J}),"to",u().createElement(Ye.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!0,price:e.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:J}))})))})):null,function(){var e;return(null===(e=null==le?void 0:le.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"REMOVE"===e.offerImpact})})}))||!1}()?u().createElement(it,{id:"Bank-discount-offer",hasNotifCard:Object.keys(I).length>0,variant:"notifCardWarning",label:Object.keys(I).length>1?R.formatMessage({id:"LABEL_LOADED_OFFERS_REMOVED_DEBIT_TITLE"}):R.formatMessage({id:"LABEL_LOADED_OFFER__REMOVED_DEBIT_TITLE"}),label2:R.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING"}),label3:ue()>1?R.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE"}):R.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE"})},Object.entries(I).map(function(e){var t=(0,o.__read)(e,2),n=t[0],r=t[1];return u().createElement("div",{key:n},w&&w.length>1&&u().createElement("p",{className:"payment-text-14 payment-text-gray payment-mb-5"},n,":"),u().createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10"},r.map(function(e,t){return u().createElement(Ye.ListItem,{key:t,className:"payment-text-14 payment-text-gray payment-mb-5 payment-leading-18"},e.phoneNumber," - ",u().createElement(Ye.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!0,price:e.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:J}))})))})):null,$&&$.length>0&&0==ue()&&(w&&w.length>1?oe().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):Z&&Z.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0))>0?u().createElement(Ye.Text,{role:"alert",className:"payment-bg-gray-3 payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-10",elementType:"div"},u().createElement("div",{className:"payment-flex payment-size-20 payment-items-center payment-justify-center"},u().createElement(Ye.Icon,{className:"payment-text-20 payment-text-blue",iconClass:"bi_brui",iconName:"bi_info_notif_small"})),u().createElement(Ye.Text,{id:0==ue()?"Bank-discount-offer":"",className:"payment-text-14 payment-leading-20 payment-text-gray",elementType:"p"}," ",R.formatMessage({id:"SORRY_MESSAGE"}))):""),u().createElement("div",null,u().createElement("div",{role:"radiogroup","aria-labelledby":"payment-radio-bank"},u().createElement("form",{noValidate:!0},u().createElement(Ye.FormControl,null,!M&&u().createElement("div",null,ne&&u().createElement("div",null,u().createElement(Ge,{id:"radio-1",name:"bank-details-radio",label:R.formatMessage({id:"BANK_ACCOUNT_AUTOMATIC_LABEL"}),describe:R.formatMessage({id:"BANK_ACCOUNT_AUTOMATIC_DESCRIPTION"}),isInterac:!0,ref:L.interac,defaultChecked:!0,interactIconPath:R.formatMessage({id:"INTERAC_BOX_LOGO"}),onChange:D},u().createElement("div",null,u().createElement("ul",{className:"payment-gap-x-15 sm:payment-gap-x-45 payment-w-1/2 sm:payment-w-[250px] payment-flex payment-flex-wrap payment-flex-col payment-h-[90px] payment-list-disc payment-list-inside payment-mt-15 payment-ml-10"},F.map(function(e){return u().createElement(Ye.ListItem,{className:"brui-text-gray brui-leading-18"},u().createElement(Ye.Text,{className:"payment-mt-10 brui-text-14"}," ",e))})),u().createElement("div",{className:"payment-mt-15"},u().createElement("a",{href:K.externalRedirectUrl,onClick:function(){var e,t;e=new Array(X.length),X&&X.map(function(t){e.includes(t.Ban)||e.push(t.Ban)}),t=e.filter(function(e){return null!==e}),window.sessionStorage.setItem("itemsChecked",JSON.stringify(t))},className:"brui-text-15 brui-leading-17 brui-py-7 brui-px-30 brui-inline-block brui-rounded-30 \r\n                            brui-bg-blue-1 brui-text-white brui-border-blue-1 brui-border-2 enabled:hover:brui-bg-blue enabled:hover:brui-border-blue focus:brui-outline-blue-2 \r\n                            focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-bg-gray-2 \r\n                            disabled:brui-text-white disabled:brui-border-gray-2",role:"button"},u().createElement("span",{"aria-hidden":"true",dangerouslySetInnerHTML:{__html:N}}),u().createElement("span",{className:"payment-sr-only",dangerouslySetInnerHTML:{__html:A}})))))),u().createElement("div",{className:"payment-mt-15"},u().createElement(Ge,{id:"radio-2",name:"bank-details-radio",label:d.length>0?R.formatMessage({id:"EXISTING_BANK_ACCOUNT_MANUAL_DETAILS_LABEL"}):R.formatMessage({id:"BANK_ACCOUNT_MANUAL_DETAILS_LABEL"}),describe:R.formatMessage({id:"BANK_ACCOUNT_MANUAL_DETAILS_DESCRIPTION"}),ref:L.manualDetails,onChange:D,defaultChecked:!ne||void 0},u().createElement(Ye.Divider,{direction:"horizontal",width:1,className:"payment-my-30 payment-bg-gray-4"}),u().createElement("div",{className:"brui-flex brui-flex-row payment-gap-4 brui-items-center payment-mb-30 md:payment-mb-45"},u().createElement("div",{className:"brui-text-14 brui-text-gray brui-leading-18"},R.formatMessage({id:"BANK_NEED_HELP"})),u().createElement(Ve.LightBoxFindYourTransaction,null)),u().createElement("div",null,(P||!v||"error"==(null==Q?void 0:Q.dataType)||!ne)&&u().createElement("form",{noValidate:!0},B&&k&&u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement(et,{isCreditCardPayment:!1,isBankPayment:!0,isPreAuth:B,paymentItems:w,ref:z,onChange:U,getExistingBankPaymentDetails:f})),(H||!B&&!H||B||W)&&u().createElement(u().Fragment,null,u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},u().createElement(Ye.Label,{"aria-hidden":!0,id:"bank-name-label",htmlFor:"bank-name",isError:j,required:!0},R.formatMessage({id:"BANK_NAME_LABEL"})),u().createElement("span",{className:"brui-sr-only"},R.formatMessage({id:"BANK_NAME_SR_LABEL"}))),""!=s.bankCode&&u().createElement(Ye.Select,{className:"sm:!payment-w-[280px] brui-text-gray",id:"bank-name",name:"select-bank-name",hasError:j,errorMessage:R.formatMessage({id:"BANK_NAME_ERROR_LABEL"}),"aria-labelledby":"bank-name-label",ref:Y.inputBankName,defaultValue:s.bankCode,dropDownHeight:Ze,placeHolder:R.formatMessage({id:"SELECT_BANK_PLACEHOLDER"})},q.map(function(e){return""!=e.Text&&u().createElement(Ye.SelectOption,{value:e.Value,id:"option-bank-".concat(e.Value),displayName:e.Text})})),""==s.bankCode&&u().createElement(Ye.Select,{className:"sm:!payment-w-[280px] brui-text-gray",id:"bank-name",name:"select-bank-name",hasError:j,errorMessage:R.formatMessage({id:"BANK_NAME_ERROR_LABEL"}),"aria-labelledby":"bank-name-label",ref:Y.inputBankName,dropDownHeight:Ze,placeHolder:R.formatMessage({id:"SELECT_BANK_PLACEHOLDER"})},q.map(function(e){return""!=e.Text&&u().createElement(Ye.SelectOption,{value:e.Value,id:"option-bank-".concat(e.Value),displayName:e.Text})}))),u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},u().createElement(Ye.Label,{"aria-hidden":!0,htmlFor:"bank-holder-name",isError:O,required:!0},R.formatMessage({id:"BANK_HOLDER_NAME_LABEL"})),u().createElement("span",{className:"brui-sr-only"},R.formatMessage({id:"DEBIT_ACCOUNT_HOLDER_NAME_SR_TEXT"}))),u().createElement(Ye.InputText,{className:"sm:!payment-w-[280px] !brui-text-gray",id:"bank-holder-name",required:!0,isError:O,errorMessage:R.formatMessage({id:"BANK_HOLDER_NAME_ERROR_LABEL"}),minLength:5,maxLength:70,ref:Y.inputBankAccountHolder,value:s.bankAccountHolder,onChange:b})),u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},u().createElement(Ye.Label,{"aria-hidden":!0,htmlFor:"bank-transit-number",isError:G,required:!0},R.formatMessage({id:"BANK_TRANSIT_NUMBER_LABEL"})),u().createElement("span",{className:"brui-sr-only"},R.formatMessage({id:"DEBIT_ACCOUNT_TRANSIT_SR_TEXT"})),u().createElement(Ye.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray"},R.formatMessage({id:"BANK_TRANSIT_NUMBER_DESCRIPTION"}))),u().createElement(Ye.InputText,{className:"!payment-w-[140px]",id:"bank-transit-number",required:!0,isError:G,errorMessage:R.formatMessage({id:"BANK_TRANSIT_ERROR_LABEL"}),minLength:5,maxLength:5,onInput:function(e){return tt(e)},ref:Y.inputTransitNumber,value:s.bankTransit,onChange:E})),u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},u().createElement(Ye.Label,{"aria-hidden":!0,htmlFor:"bank-account-number",isError:V,required:!0},R.formatMessage({id:"BANK_ACCOUNT_NUMBER_LABEL"})),u().createElement("span",{className:"brui-sr-only"},R.formatMessage({id:"DEBIT_ACCOUNT_ACCOUNT_NUMBER_SR_TEXT"})),u().createElement(Ye.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray"},R.formatMessage({id:"BANK_ACCOUNT_NUMBER_DESCRIPTION"}))),u().createElement("div",null,d.length>0&&u().createElement(Ye.InputText,{className:"!payment-w-[140px]",id:"bank-account-number",required:!0,isError:V,minLength:5,maxLength:12,errorMessage:R.formatMessage({id:"BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL"}),onInput:function(e){return tt(e)},value:s.bankAccountNumberMasked,onChange:y}),u().createElement(Ye.InputText,{className:"!payment-w-[140px]",id:"bank-account-number",required:!0,isError:!(d.length>0)&&V,minLength:5,maxLength:12,errorMessage:R.formatMessage({id:"BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL"}),onInput:function(e){return tt(e)},ref:Y.inputBankAccountNumber,value:s.bankAccountNumber,onChange:_,type:d.length>0?"hidden":"text"}))),u().createElement(Ye.Text,{className:"brui-text-14 brui-leading-18 brui-text-gray payment-mt-30 sm:payment-mt-45 brui-inline-block"},R.formatMessage({id:"REQUIRED_LABEL"}))))))))))),M&&ne&&u().createElement("div",null,u().createElement("form",{noValidate:!0},u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},u().createElement(Ye.Label,{id:"bank-name-label",htmlFor:"bank-name",isError:!1,required:!0},R.formatMessage({id:"BANK_NAME_LABEL"}))),u().createElement(Ye.Select,{className:"sm:!payment-w-[280px] brui-text-gray",id:"bank-name",name:"select-bank-name",hasError:!1,errorMessage:"Please select at least one option","aria-labelledby":"bank-name-label",ref:Y.inputBankName,defaultValue:ae.bankCode,dropDownHeight:Ze,placeHolder:R.formatMessage({id:"SELECT_BANK_PLACEHOLDER"})},q.map(function(e){return""!=e.Text&&u().createElement(Ye.SelectOption,{value:e.Value,id:"option-bank-".concat(e.Value),displayName:e.Text})}))),u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},u().createElement(Ye.Label,{htmlFor:"bank-holder-name",isError:!1,required:!0},R.formatMessage({id:"BANK_HOLDER_NAME_LABEL"}))),u().createElement(Ye.InputText,{className:"sm:!payment-w-[280px] !brui-text-gray",id:"bank-holder-name",required:!0,isError:O,errorMessage:R.formatMessage({id:"BANK_HOLDER_NAME_ERROR_LABEL"}),minLength:5,maxLength:70,ref:Y.inputBankAccountHolder,value:ae.bankAccountHolderName,onChange:function(e){var t=e.target.value;ie((0,o.__assign)((0,o.__assign)({},ae),{bankAccountHolderName:t}))}})),u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},u().createElement(Ye.Label,{htmlFor:"bank-transit-number",isError:!1,required:!0},R.formatMessage({id:"BANK_TRANSIT_NUMBER_LABEL"})),u().createElement(Ye.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray"},R.formatMessage({id:"BANK_TRANSIT_NUMBER_DESCRIPTION"}))),u().createElement(Ye.InputText,{className:"!payment-w-[140px]",id:"bank-transit-number",required:!0,isError:!1,errorMessage:"This is required field",minLength:7,maxLength:12,onInput:function(e){return tt(e)},ref:Y.inputTransitNumber,value:ae.bankTransitCode,onChange:function(e){var t=e.target.value;ie((0,o.__assign)((0,o.__assign)({},ae),{bankTransitCode:t}))}})),u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},u().createElement(Ye.Label,{htmlFor:"bank-account-number",isError:!1,required:!0},R.formatMessage({id:"BANK_ACCOUNT_NUMBER_LABEL"})),u().createElement(Ye.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray"},R.formatMessage({id:"BANK_ACCOUNT_NUMBER_DESCRIPTION"}))),u().createElement("div",null,u().createElement(Ye.InputText,{className:"!payment-w-[140px]",id:"bank-account-number",required:!0,isError:!1,errorMessage:"This is required field",onInput:function(e){return tt(e)},ref:Y.inputBankAccountNumber,value:ae.bankAccountNumber,onChange:function(e){var t=e.target.value;ie((0,o.__assign)((0,o.__assign)({},ae),{bankAccountNumber:t}))}}),u().createElement("div",{style:{marginTop:"5px"},className:"brui-flex brui-gap-10 brui-mt-5"},u().createElement("span",{style:{marginRight:"8px"},className:"bi_small_checkmark_full bi_brui brui-text-green",role:"img","aria-hidden":"true","aria-label":" "}),u().createElement("div",{id:"account-fetched",className:"brui-flex brui-flex-col brui-text-12 brui-leading-14"},u().createElement("span",{id:"account-fetched-message",className:"brui-text-black brui-font-bold"},R.formatMessage({id:"INTERAC_FETCHED_LABEL"})),u().createElement("span",{className:"brui-text-gray"},R.formatMessage({id:"INTERAC_FETCHED_SUBTITLE"})))))),u().createElement(Ye.Text,{className:"brui-text-14 brui-leading-18 brui-text-gray payment-mt-30 sm:payment-mt-45 brui-inline-block"},R.formatMessage({id:"REQUIRED_LABEL"})))))))}),lt=function(e){return{redirectUrlAction:function(){e((0,g.getRedirectUrl)({}))}}},ut=function(e){return{redirectUrl:e.redirectUrl,interacBankInfo:e.interacBankInfo,interactBankFailureInfo:e.interactBankFailureInfo}},st=(0,s.connect)(ut,lt)((0,He.injectIntl)(ot)),ct=(0,l.forwardRef)(function(e,t){var n,r,a,i,l,s,c,m,d,p,f,b,E=e.intl,_=e.Checked,y=e.onChange,g=e.isPreauth,v=(e.hasCreditCardDetails,e.bankitems),N=(e.handleBankRadioChange,e.isBankChecked),A=e.cardNumber,C=e.handleCreditCardChange,h=e.inputRefs,T=e.cardIcons,I=e.cardType,R=e.errorCardNumber,x=e.errorCardName,S=e.errorExpiryDate,O=e.errorSecurityCode,M=e.handleMaskCVV,L=e.CVV,D=e.creditCardAutopayOffers,P=e.debitCardAutopayOffers,B=e.checkedBillItems,k=e.language,w=e.managePreauth,U=e.IsAutopayCreditEnabled,F=function(){var e=[];return D&&(null==D||D.map(function(t){B&&(null==B||B.map(function(n){t.Ban==n.BillName&&e.push(t)}))})),e},H={label:E.formatMessage({id:"PAYMENT_METHOD"}),credits:v&&v.length>1?F():D},Y=function(){return v&&v.length>1?F().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):D&&D.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)},j=null!==w?E.formatMessage({id:"UPDATE_CREDITCARD_PAYMENT"}):E.formatMessage({id:"CREDIT_CARD_LABEL"}),G=(0,o.__read)(u().useState(E.formatMessage({id:"CREDIT_CARD_YEAR_TEXT"})),2),V=G[0],z=G[1],q=(0,o.__read)(u().useState(E.formatMessage({id:"CREDIT_CARD_MONTH_TEXT"})),2),K=q[0],W=q[1];return E.formatMessage({id:"CREDIT_CARD_EXPIRY_DATE_SR_LABEL"},{month:K,year:V}),d=null===(n=null==H?void 0:H.credits)||void 0===n?void 0:n.reduce(function(e,t){return t.AutopayEligibleSubscribers.forEach(function(n){e[t.banInfo.nickName]||(e[t.banInfo.nickName]=[]),n.autopayOffers.forEach(function(r){"RETAIN"!==r.offerImpact&&e[t.banInfo.nickName].push({phoneNumber:n.subscriberTelephoneNumber,discountAmount:r.discountAmount})})}),e},{}),p=null===(r=null==H?void 0:H.credits)||void 0===r?void 0:r.reduce(function(e,t){return t.AutopayEligibleSubscribers.forEach(function(n){e[t.banInfo.nickName]||(e[t.banInfo.nickName]=[]),n.autopayOffers.forEach(function(r){"RETAIN"!==r.offerImpact&&e[t.banInfo.nickName].push({phoneNumber:n.subscriberTelephoneNumber,currentDiscountAmount:r.currentdiscountAmount,discountAmount:r.discountAmount})})}),e},{}),f=null===(a=null==H?void 0:H.credits)||void 0===a?void 0:a.reduce(function(e,t){return t.AutopayEligibleSubscribers.forEach(function(n){e[t.banInfo.nickName]||(e[t.banInfo.nickName]=[]),n.autopayOffers.forEach(function(r){"RETAIN"!==r.offerImpact&&e[t.banInfo.nickName].push({phoneNumber:n.subscriberTelephoneNumber,currentDiscountAmount:r.currentdiscountAmount,discountAmount:r.discountAmount})})}),e},{}),b=null===(i=null==H?void 0:H.credits)||void 0===i?void 0:i.reduce(function(e,t){return t.AutopayEligibleSubscribers.forEach(function(n){e[t.banInfo.nickName]||(e[t.banInfo.nickName]=[]),n.autopayOffers.forEach(function(r){"RETAIN"!==r.offerImpact&&e[t.banInfo.nickName].push({phoneNumber:n.subscriberTelephoneNumber,discountAmount:r.discountAmount})})}),e},{}),u().createElement("div",{className:"brui-mb-15"},u().createElement(je,{id:"payment-radio-credit",name:"payment-radio",label:j,headingLevel:"h3",checked:_,ref:t,onChange:y},U&&u().createElement("div",null,function(){var e;return(null===(e=null==H?void 0:H.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return("GAIN"===e.offerImpact||""===e.offerImpact||null===e.offerImpact)&&"RETAIN"!==e.offerImpact})})}))||!1}()?u().createElement("div",{role:"alert"},u().createElement(it,{id:"Credit-discount-offer",hasNotifCard:Object.keys(d).length>0,variant:"greatNews",label:E.formatMessage({id:"GREAT_NEWS"}),label1:Object.keys(d).length>1?E.formatMessage({id:"LABEL_LOADED_OFFERS_CREDIT_TITLE"}):E.formatMessage({id:"LABEL_LOADED_OFFER_CREDIT_TITLE"}),label2:E.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING"}),label3:Object.keys(d).length>1?E.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE"}):E.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE"})},Object.entries(d).map(function(e){var t=(0,o.__read)(e,2),n=t[0],r=t[1];return u().createElement("div",{key:n},v&&v.length>1&&u().createElement("p",{className:"payment-text-14 payment-text-gray payment-mb-5"},n,":"),u().createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10"},r.map(function(e,t){return u().createElement(Ye.ListItem,{key:t,className:"payment-text-14 payment-text-gray payment-mb-5 payment-leading-18"},e.phoneNumber," - ",u().createElement(Ye.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!1,price:e.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:k}))})))}))):null,function(){var e;return(null===(e=null==H?void 0:H.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"INCREASE"===e.offerImpact})})}))||!1}()?u().createElement("div",{role:"alert"},u().createElement(it,{id:"Credit-discount-offer",hasNotifCard:Object.keys(p).length>0,variant:"greatNews",label:E.formatMessage({id:"GREAT_NEWS"}),label1:Object.keys(p).length>1?E.formatMessage({id:"LABEL_LOADED_OFFERS_INCREASED_CREDIT_TITLE"}):E.formatMessage({id:"LABEL_LOADED_OFFER_INCREASED_CREDIT_TITLE"})},Object.entries(p).map(function(e){var t=(0,o.__read)(e,2),n=t[0],r=t[1];return u().createElement("div",{key:n},v&&v.length>1&&u().createElement("p",{className:"payment-text-14 payment-text-gray payment-mb-5"},n,":"),u().createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10"},r.map(function(e,t){return u().createElement(Ye.ListItem,{key:t,className:"payment-text-14 payment-text-gray payment-mb-5 payment-leading-18"},e.phoneNumber," - from",u().createElement(Ye.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!0,price:e.currentDiscountAmount,variant:"defaultPrice",suffixText:"perMonth",language:k}),"to",u().createElement(Ye.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!0,price:e.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:k}))})))}))):null,function(){var e;return(null===(e=null==H?void 0:H.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"REDUCE"===e.offerImpact})})}))||!1}()?u().createElement("div",{role:"alert"},u().createElement(it,{id:"Credit-discount-offer",hasNotifCard:Object.keys(f).length>0,variant:"notifCardWarning",label:(Object.keys(f).length,E.formatMessage({id:"LABEL_LOADED_OFFERS_REDUCED_DEBIT_TITLE"}))},Object.entries(f).map(function(e){var t=(0,o.__read)(e,2),n=t[0],r=t[1];return u().createElement("div",{key:n},v&&v.length>1&&u().createElement("p",{className:"payment-text-14 payment-text-gray payment-mb-5"},n,":"),u().createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10"},r.map(function(e,t){return u().createElement(Ye.ListItem,{key:t,className:"payment-text-14 payment-text-gray payment-mb-5 payment-leading-18"},e.phoneNumber," - from",u().createElement(Ye.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!0,price:e.currentDiscountAmount,variant:"defaultPrice",suffixText:"perMonth",language:k}),"to",u().createElement(Ye.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!0,price:e.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:k}))})))}))):null,function(){var e;return(null===(e=null==H?void 0:H.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"REMOVE"===e.offerImpact})})}))||!1}()?u().createElement("div",{role:"alert"},u().createElement(it,{id:"Credit-discount-offer",hasNotifCard:Object.keys(b).length>0,variant:"notifCardWarning",label:Y()>1?E.formatMessage({id:"LABEL_LOADED_OFFERS_REMOVED_DEBIT_TITLE"}):E.formatMessage({id:"LABEL_LOADED_OFFER_REMOVED_DEBIT_TITLE"}),label2:E.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING"}),label3:Y()>1?E.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE"}):E.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE"})},Object.entries(b).map(function(e){var t=(0,o.__read)(e,2),n=t[0],r=t[1];return u().createElement("div",{key:n},v&&v.length>1&&u().createElement("p",{className:"payment-text-14 payment-text-gray payment-mb-5"},n,":"),u().createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10"},r.map(function(e,t){return u().createElement(Ye.ListItem,{key:t,className:"payment-text-14 payment-text-gray payment-mb-5 payment-leading-18"},e.phoneNumber," - ",u().createElement(Ye.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!0,price:e.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:k}))})))}))):null,P&&P.length>0&&0==Y()&&(v&&v.length>1?F().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):P&&P.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0))>0?u().createElement(Ye.Text,{role:"alert",className:"payment-bg-gray-3 payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-10",elementType:"div"},u().createElement("div",{className:"payment-flex payment-size-20 payment-items-center payment-justify-center"},u().createElement(Ye.Icon,{className:"payment-text-20 payment-text-blue",iconClass:"bi_brui",iconName:"bi_info_notif_small"})),u().createElement(Ye.Text,{id:0==Y()?"Credit-discount-offer":"",className:"payment-text-14 payment-leading-20 payment-text-gray",elementType:"p"}," ",E.formatMessage({id:"SORRY_MESSAGE"}))):""),u().createElement("div",null,u().createElement("form",{noValidate:!0},(N||!g&&!N||g)&&u().createElement(u().Fragment,null,u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-self-start sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},u().createElement("div",null,u().createElement(Ye.Label,{"aria-hidden":!1,htmlFor:"card-number",isError:R,required:!0},u().createElement("span",{id:"cc-number-label",className:"brui-sr-only"},E.formatMessage({id:"CREDIT_CARD_NUMBER_SR_LABEL_1"})),u().createElement("span",{"aria-hidden":!0},E.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_1"})))),u().createElement(Ye.Text,{"aria-hidden":!!(null===(s=null===(l=h.inputCreditCardNumber)||void 0===l?void 0:l.current)||void 0===s?void 0:s.value),elementType:"div",className:"brui-text-12 leading-14 sm:brui-ml-14 brui-text-gray payment-mt-5"},E.formatMessage({id:"CREDIT_CARD_NUMBER_DESC_INPUT_LABEL"}))),u().createElement("div",{className:"brui-flex brui-flex-col sm:brui-flex-row brui-flex-wrap"},u().createElement(Ye.InputText,{value:A,onChange:C,onInput:function(e){return tt(e)},className:"sm:!payment-w-[280px] sm:payment-mr-30",id:"card-number",required:!0,isError:R,errorMessage:E.formatMessage({id:"ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL"}),ref:h.inputCreditCardNumber,"aria-labelledby":"cc-number-label"}),u().createElement("div",{className:"sm:payment-h-44 payment-ml-0 payment-mt-10 sm:payment-mt-[6px] brui-flex payment-items-baseline brui-gap-15","aria-label":E.formatMessage({id:"CC_IMAGE_SR_LABEL"}),role:"img"},Object.entries(T).map(function(e){var t=(0,o.__read)(e,2),n=t[0],r=t[1];return u().createElement("img",{key:n,src:r,alt:"".concat(n," card"),className:"brui-h-32 payment-mr-15 brui-object-contain","aria-hidden":"true",style:{opacity:I===n?1:.5,transition:"opacity 0.3s ease-in-out"}})})))),u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex sm:payment-self-start brui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},u().createElement("div",null,u().createElement(Ye.Label,{"aria-hidden":!1,htmlFor:"text-2",isError:x,required:!0},u().createElement("span",{id:"cc-name-label",className:"brui-sr-only"},E.formatMessage({id:"CREDIT_CARD_NAME_SR_LABEL_1"})),u().createElement("span",{"aria-hidden":!0},E.formatMessage({id:"CREDIT_CARD_NAME_LABEL_1"})))),u().createElement(Ye.Text,{"aria-hidden":!!(null===(m=null===(c=h.inputCreditCardHolderName)||void 0===c?void 0:c.current)||void 0===m?void 0:m.value),elementType:"div",className:"brui-text-12 leading-14 sm:brui-ml-14 brui-text-gray"},E.formatMessage({id:"CREDIT_CARD_NAME_DESC_INPUT_LABEL"}))),u().createElement("div",{className:"brui-flex brui-flex-col sm:brui-flex-row brui-flex-wrap"},u().createElement(Ye.InputText,{className:"sm:!payment-w-[280px]",id:"text-2","aria-labelledby":"cc-name-label",required:!0,isError:x,errorMessage:E.formatMessage({id:"ERROR_CREDIT_CARD_NAME_INPUT_LABEL"}),minLength:5,maxLength:70,ref:h.inputCreditCardHolderName}),u().createElement("div",{className:"brui-flex payment-items-baseline sm:payment-h-44 sm:payment-ml-10 payment-mt-5 sm:payment-mt-7"},u().createElement(Ve.LightBoxNoname,null)))),u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},u().createElement(Ye.Label,{"aria-hidden":!1,id:"label-3",isError:S,required:!0},u().createElement("span",{id:"expiry-month-label",className:"brui-sr-only"},E.formatMessage({id:"CREDIT_CARD_EXPIRY_DATE_LABEL"})," ","{"+K+"/"+V+"}"),u().createElement("span",{"aria-hidden":!0},E.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"})))),u().createElement("div",{className:"brui-flex-col"},u().createElement(Ye.FormGroup,{className:"brui-flex brui-flex-wrap",hasError:S,errorMessage:E.formatMessage({id:"ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL"})},u().createElement("div",{"aria-hidden":!0,className:"payment-w-[75px]"},u().createElement(Ye.Select,{name:"month",id:"select-12",defaultValue:"",placeHolder:E.formatMessage({id:"CREDIT_CARD_MONTH_PLACEHOLDER"}),onChange:function(e){W(e.target.value)},disableDropdownIcon:!0,className:"brui-text-gray",ref:h.inputCreditCardExpiryMonth,"aria-required":!0,"aria-labelledby":"expiry-month-label"},nt().map(function(e,t){return u().createElement(Ye.SelectOption,{value:e.toString(),id:"option-"+t,displayName:e.toString()})}))),u().createElement("div",{"aria-hidden":!0,className:"payment-w-[75px] payment-ml-10"},u().createElement(Ye.Select,{name:"year",id:"select-2",defaultValue:"",placeHolder:E.formatMessage({id:"CREDIT_CARD_YEAR_PLACEHOLDER"}),onChange:function(e){z(e.target.value)},disableDropdownIcon:!0,className:"brui-text-gray",ref:h.inputCreditCardExpiryYear,"aria-required":!0,"aria-labelledby":"expiry-year-label"},rt().map(function(e,t){return u().createElement(Ye.SelectOption,{value:e.toString(),id:"option-year-"+t,displayName:e.toString()})})))))),u().createElement(Ye.FormControl,{className:"sm:brui-flex-row payment-mt-30"},u().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},u().createElement(Ye.Label,{"aria-hidden":!1,htmlFor:"text-4",isError:O,required:!0},u().createElement("span",{id:"cc-security-code-label",className:"brui-sr-only"},E.formatMessage({id:"CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL"})),u().createElement("span",{"aria-hidden":!0},E.formatMessage({id:"CREDIT_CARD_SECURITY_CODE_INPUT_LABEL"})))),u().createElement("div",{className:"brui-flex sm:brui-flex-row brui-flex-wrap"},u().createElement(Ye.InputText,{onInput:function(e){return tt(e)},className:"!payment-w-[75px] payment-mr-10",id:"text-4",required:!0,isError:O,errorMessage:E.formatMessage({id:"ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL"}),errorMessageClassName:"payment-w-[75px] payment-text-nowrap",type:"password",onChange:M,ref:h.inputCreditCardSecurityCode,"aria-labelledby":"cc-security-code-label"}),u().createElement(Ye.InputText,{type:"hidden",id:"text-hidden",value:L}),u().createElement("div",{className:"brui-flex brui-items-center brui-h-44"},u().createElement(Ve.LightBoxSecurityCode,null)))))),u().createElement(Ye.Text,{className:"brui-text-14 brui-leading-18 brui-text-gray payment-mt-45 sm:payment-mt-45 brui-inline-block"},E.formatMessage({id:"REQUIRED_LABEL"})))))}),mt=(0,He.injectIntl)(ct),dt=function(e){var t=e.label,n=e.className,r=e.labelClassName,a=e.listClassName,i=e.children,l=(0,o.__rest)(e,["label","className","labelClassName","listClassName","children"]);return u().createElement("div",(0,o.__assign)({className:["payment-pb-15 last:payment-pb-0 payment-pt-15 first:payment-pt-0 payment-border-b-1 payment-border-b-gray-4 last:payment-border-none",n].join(" ").trim()},l),t&&u().createElement(Ye.Text,{className:["payment-block payment-leading-18 payment-text-14 payment-mb-15 payment-text-gray sm:payment-mt-15 payment-mt-5",r].join(" ").trim()},t),u().createElement("div",{role:"list",className:["payment-text-14 payment-leading-18",a].join(" ").trim()},i))},pt=dt,ft={priceList:"payment-flex payment-justify-between sm:payment-justify-normal payment-mb-5 last:payment-mb-0",errorList:"payment-mb-5 last:payment-mb-0 payment-text-red",accountList:"last:payment-mb-0 payment-text-gray payment-p-15 payment-pt-10 sm:payment-pt-15 payment-bg-gray-3 payment-rounded-10",accountListSimple:"payment-mb-5 last:payment-mb-0 payment-text-gray"},bt=function(e){var t=e.cardDetails,n=e.label,r=e.labelDescription,a=e.priceSettings,i=void 0===a?{showZeroDecimalPart:!0,price:0}:a,l=e.variant,s=e.className,c=(e.children,e.inputRef),m=(0,o.__rest)(e,["cardDetails","label","labelDescription","priceSettings","variant","className","children","inputRef"]),d=n+" - "+r,p=function(e){e&&e.scrollIntoView({behavior:"smooth",block:"center"})};return u().createElement("div",(0,o.__assign)({role:"listitem",className:["",ft[l],s].join(" ").trim()},m),"priceList"==l&&u().createElement(u().Fragment,null,u().createElement(Ye.Text,{className:"payment-text-14 payment-leading-18 sm:payment-min-w-[153px]"},u().createElement("strong",null,n)),u().createElement(Ye.Price,{language:i.language?i.language:"en",showZeroDecimalPart:i.showZeroDecimalPart,price:"number"==typeof r?r:0,variant:"defaultPrice",className:"!payment-text-14 payment-leading-18  payment-font-normal"})),"errorList"==l&&u().createElement(u().Fragment,null,u().createElement("span",{className:"payment-text-14","aria-hidden":"true"},"•"),u().createElement(Ye.Link,{variant:"textRed",size:"small",href:"javascript:void(0)","aria-label":d,className:"payment-font-bold payment-ml-5",onClick:function(){var e,t,n,r;if((null==c?void 0:c.current)instanceof HTMLSelectElement)for(n=null===(e=null==c?void 0:c.current)||void 0===e?void 0:e.previousElementSibling;n;){if("BUTTON"===n.tagName)return p(r=n),void r.focus();n=n.previousElementSibling}else p(null==c?void 0:c.current),null===(t=null==c?void 0:c.current)||void 0===t||t.focus()}},n," "),u().createElement("span",{className:"payment-text-gray payment-text-14"}," - ",r)),"accountList"==l&&u().createElement("div",{className:"payment-flex payment-flex-wrap payment-justify-between"},u().createElement(Ye.Text,{className:"payment-mr-5 payment-mt-5 sm:payment-mt-0","aria-hidden":"true"},u().createElement("strong",{className:"payment-text-black"},n)," ",r),u().createElement("span",{className:"payment-sr-only"},r),u().createElement(Ye.Text,{elementType:"span",className:"payment-mt-5 sm:payment-mt-0"},t)),"accountListSimple"==l&&u().createElement("div",{className:"payment-flex payment-flex-wrap payment-justify-between"},u().createElement(Ye.Text,{className:"payment-mr-5 payment-mt-5 sm:payment-mt-0"},u().createElement("strong",{className:"payment-text-black"},n)," ",r),u().createElement(Ye.Text,{elementType:"span",className:"payment-mt-5 sm:payment-mt-0"},t)))},Et=bt,_t=[{label:"Account holder name",labelDescription:"This information is required."},{label:"Transit number",labelDescription:"This information is required."},{label:"Bank name",labelDescription:"This information is required."},{label:"Account number",labelDescription:"This information is required."}],(0,He.injectIntl)(function(e){var t=e.intl;return l.createElement(Ye.Alert,{variant:"error",className:"brui-block sm:brui-flex brui-px-0 sm:brui-px-30 payment-py-30 brui-border-x-0 sm:payment-border-x-1 payment-border-y-1 brui-relative brui-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-3"},l.createElement(Ye.Text,{elementType:"div",className:"payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:" sm:brui-mt-7 brui-mb-15 brui-font-sans brui-leading-22"},l.createElement("span",{"aria-hidden":"true"},t.formatMessage({id:"ALERT_ERROR_HEADING"})),l.createElement("span",{className:"payment-sr-only"},t.formatMessage({id:"ALERT_ERROR_HEADING_SR"}))),l.createElement("div",null,l.createElement(pt,null,_t.map(function(e){return l.createElement(Et,{label:e.label,labelDescription:e.labelDescription,variant:"errorList"})})))))}),(0,He.injectIntl)(function(e){var t=e.children,n=e.intl;return l.createElement(Ye.Alert,{variant:"error",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-2"},l.createElement(Ye.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0 brui-flex-1"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:"brui-mb-5 brui-font-sans brui-text-red "},n.formatMessage({id:"ALERT_ERROR_HEADING_SOME_BALANCE"})),l.createElement("div",null,t),l.createElement(Ye.Button,{className:"brui-mt-30",variant:"primary",size:"regular"},n.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))}),yt=(0,He.injectIntl)(function(e){var t=e.children,n=e.intl;return l.createElement(Ye.Alert,{variant:"error",className:"payment-block sm:payment-flex payment-px-0 sm:payment-px-30 payment-py-30 payment-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative payment-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-3","aria-labelledby":"error-alert-1 error-alert-2 error-alert-3"},l.createElement(Ye.Text,{elementType:"div",className:"payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0"},l.createElement(Ye.Heading,{id:"error-alert-1",level:"h2",variant:"xs",className:" sm:payment-mt-7 payment-mb-15 payment-font-sans payment-leading-22"},l.createElement("span",{"aria-hidden":"true"},n.formatMessage({id:"ALERT_ERROR_HEADING"})),l.createElement("span",{className:"payment-sr-only"},n.formatMessage({id:"ALERT_ERROR_HEADING_SR"}))),l.createElement("div",null,t)))}),(0,He.injectIntl)(function(e){return e.intl,l.createElement(Ye.Alert,{variant:"error",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-2"},l.createElement(Ye.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:"brui-mb-12 brui-text-red brui-font-sans"},l.createElement("strong",null,"Your balance of $195.45 was not paid")," due to an error processing your request."),l.createElement("p",{className:"brui-text-14 brui-my-15 brui-text-gray"},"A separate one-time payment must be made to pay this balance, or risk late fees."),l.createElement(Ye.Button,{variant:"primary",size:"regular"},"Make a Payment")))}),(0,He.injectIntl)(function(e){var t=e.intl;return l.createElement(Ye.Alert,{variant:"info",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},l.createElement(Ye.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:"brui-mb-12 brui-font-sans brui-font-bold brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),l.createElement("p",{className:"brui-text-14 brui-my-15 brui-text-gray"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_DESC"})),l.createElement(Ye.Text,{elementType:"div",className:"sm:brui-flex brui-block"},l.createElement(Ye.Text,{elementType:"div",className:"brui-pr-0 sm:brui-pr-10"},l.createElement(Ye.Button,{variant:"primary",size:"regular"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))))}),(0,He.injectIntl)(function(e){var t=e.intl;return l.createElement(Ye.Alert,{variant:"success",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},l.createElement(Ye.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),l.createElement(Ye.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},l.createElement(Ye.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},l.createElement(Ye.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),l.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"}))),l.createElement(Ye.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15 brui-text-black"},l.createElement(Ye.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),l.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})))),l.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})," ",l.createElement("strong",null,"000011")),l.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC"}),l.createElement(Ye.Link,{variant:"textBlue",size:"small",href:"https://www.bell.ca/",className:""}," ",t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))}),(0,He.injectIntl)(function(e){var t=e.intl;return l.createElement(Ye.Alert,{variant:"success",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},l.createElement(Ye.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),l.createElement(Ye.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},l.createElement(Ye.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},l.createElement(Ye.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),l.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"})))),l.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})," ",l.createElement("strong",null,"000011")),l.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC"}),l.createElement(Ye.Link,{variant:"textBlue",size:"small",href:"https://www.bell.ca/",className:""}," ",t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))}),(0,He.injectIntl)(function(e){var t=e.intl;return l.createElement(Ye.Alert,{variant:"success",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},l.createElement(Ye.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),l.createElement(Ye.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},l.createElement(Ye.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},l.createElement(Ye.Icon,{className:"brui-text-12 brui-text-blue brui-mt-3",iconClass:"bi_check_light",iconName:"bi_check_light"}),l.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_CURRENT_BALANCE"}))),l.createElement(Ye.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},l.createElement(Ye.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),l.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"})))),l.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})," ",l.createElement("strong",null,"000011")),l.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC"}),l.createElement(Ye.Link,{variant:"textBlue",size:"small",href:"https://www.bell.ca/",className:""}," ",t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))}),(0,He.injectIntl)(function(e){var t=e.intl;return l.createElement(Ye.Alert,{variant:"success",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},l.createElement(Ye.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),l.createElement(Ye.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},l.createElement(Ye.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},l.createElement(Ye.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),l.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"}))),l.createElement(Ye.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15 brui-text-black"},l.createElement(Ye.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),l.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})))),l.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})," ",l.createElement("strong",null,"000011")),l.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC"}),l.createElement(Ye.Link,{variant:"textBlue",size:"small",href:"https://www.bell.ca/",className:""}," ",t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))}),(0,He.injectIntl)(function(e){var t=e.intl,n=e.children;return l.createElement(Ye.Alert,{variant:"warning",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36"},l.createElement(Ye.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:"brui-mb-5 brui-font-sans brui-font-bold"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),l.createElement("div",null,n),l.createElement(Ye.Button,{className:"brui-mt-30",variant:"primary",size:"regular"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))}),(0,He.injectIntl)(function(e){var t=e.intl;return l.createElement(Ye.Alert,{variant:"warning",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36"},l.createElement(Ye.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:"brui-mb-0 sm:brui-mb-12 brui-font-sans brui-font-bold brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),l.createElement("p",{className:"brui-leading-18 brui-text-14 brui-mt-5 brui-mb-15 sm:brui-mt-0 sm:brui-mb-0 sm:brui-my-15 brui-text-gray"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_DESC_1"}),l.createElement(Ye.Price,{language:"en",showZeroDecimalPart:!0,price:195.45,variant:"defaultPrice",className:"!brui-text-14 brui-leading-14 brui-m-5 brui-font-normal brui-inline-block"}),l.createElement("span",{className:"brui-sr-only"},"195 point 45 dollars")," ",t.formatMessage({id:"ALERT_CONFIRMATION_INFO_DESC_2"})),l.createElement(Ye.Text,{elementType:"div",className:"sm:brui-flex brui-block"},l.createElement(Ye.Text,{elementType:"div",className:"brui-pr-0 sm:brui-pr-10"},l.createElement(Ye.Button,{variant:"primary",size:"regular"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))))}),(0,He.injectIntl)(function(e){var t=e.intl;return l.createElement(Ye.Alert,{variant:"warning",className:"payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block",iconSize:"36"},l.createElement(Ye.Text,{elementType:"div",className:"payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:"payment-mb-15 payment-font-sans payment-font-bold"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),l.createElement("p",{className:"payment-text-14 payment-mb-10 payment-text-gray"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_DESC"})),l.createElement(Ye.Text,{elementType:"div",className:"payment-mb-30 payment-mt-15"},l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-justify-between sm:payment-justify-normal"},l.createElement("label",{className:"payment-text-14 sm:payment-basis-1/4"},l.createElement("strong",null,"1234567890")),l.createElement(Ye.Price,{language:"en",showZeroDecimalPart:!0,price:206.98,variant:"defaultPrice",className:"!payment-text-14 payment-leading-14 payment-m-5 payment-font-normal"}))),l.createElement(Ye.Button,{variant:"primary",size:"regular"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))}),gt=function(e){function t(e){var t,n,r,a;return(null===(a=null===(r=null===(n=null===(t=null==e?void 0:e[0])||void 0===t?void 0:t.AutopayEligibleSubscribers)||void 0===n?void 0:n[0])||void 0===r?void 0:r.autopayOffers)||void 0===a?void 0:a.length)>0}var n,r=e.intl,a=e.submitMultiOrderPayment,i=e.accountInputValue,o=e.isBankPayment,u=e.checkedBillItems,s=e.language,c=e.paymentItem,m=e.creditCardAutopayOffers,d=e.debitCardAutopayOffers,p=(e.isBanCreditPreauth,!o||u&&u[0].CreditCardDetails||c.length>1?r.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"}):r.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_V2"})),f=o?"fr"===s?"ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD_FR":"ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD":"ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC",b={label:r.formatMessage({id:"PAYMENT_METHOD"}),debits:o?c&&c.length>1?(n=[],d&&(null==d||d.map(function(e){u&&u.map(function(t){e.Ban==t.BillName&&n.push(e)})})),n):d:null,credits:o?null:c&&c.length>1?function(){var e=[];return m&&(null==m||m.map(function(t){u&&u.map(function(n){t.Ban==n.BillName&&e.push(t)})})),e}():m},E=l.useCallback(function(){var e;return b.debits?b.debits.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return e.offerImpact===A.OfferImpactType.INCREASE})})})||!1:(null===(e=null==b?void 0:b.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return e.offerImpact===A.OfferImpactType.INCREASE})})}))||!1},[b.debits,b.debits]),_=l.useCallback(function(){var e;return b.debits?b.debits.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return e.offerImpact===A.OfferImpactType.REDUCE})})})||!1:(null===(e=null==b?void 0:b.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return e.offerImpact===A.OfferImpactType.REDUCE})})}))||!1},[b.debits,b.debits]),y=l.useCallback(function(){var e;return b.debits?b.debits.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return e.offerImpact===A.OfferImpactType.GAIN})})})||!1:(null===(e=null==b?void 0:b.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return e.offerImpact===A.OfferImpactType.GAIN})})}))||!1},[b.debits,b.debits]),g=t(null==b?void 0:b.credits)||t(null==b?void 0:b.debits);return l.createElement(Ye.Alert,{variant:"success",className:"brui-border brui-relative sm:payment-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 payment-py-30 sm:payment-flex brui-block",iconSize:"36",id:"alert-3",role:""},l.createElement(Ye.Text,{id:"Confirmation-message",elementType:"div",className:"payment-pl-0 sm:payment-pl-16 payment-mt-15 sm:payment-mt-0 sm:brui-pt-0"},l.createElement(Ye.Heading,{level:"h3",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},r.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),l.createElement(Ye.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},a.map(function(e,t){var n,a,o=null===(n=i.find(function(t){return t.transactionID===e.OrderFormId}))||void 0===n?void 0:n.accountNumber,s=(null===(a=u.find(function(e){return e.Ban===o}))||void 0===a?void 0:a.NickName)||o,c=r.formatMessage({id:f},{account:s});return l.createElement(l.Fragment,null,null!=(null==e?void 0:e.otp)&&(null==e?void 0:e.otp.isSuccess)&&l.createElement(Ye.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start payment-mb-15"},l.createElement(Ye.Icon,{className:"brui-text-12 brui-text-blue payment-mt-3",iconClass:"bi_brui",iconName:"bi_check_light"}),l.createElement("span",{className:"brui-text-16 brui-leading-20 payment-ml-10",dangerouslySetInnerHTML:{__html:c}})))}),l.createElement(Ye.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start payment-mb-15"},l.createElement(Ye.Icon,{className:"brui-text-12 brui-text-blue payment-mt-3 ",iconClass:"bi_brui",iconName:"bi_check_light"}),l.createElement("span",{className:"brui-text-16 brui-leading-20 payment-ml-10"},l.createElement("div",{dangerouslySetInnerHTML:{__html:p}}))),g&&0==function(){var e;return b.debits?b.debits.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return e.offerImpact===A.OfferImpactType.REMOVE})})})||!1:(null===(e=null==b?void 0:b.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return e.offerImpact===A.OfferImpactType.REMOVE})})}))||!1}()&&(E()||_()||y())?l.createElement(Ye.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start payment-mb-15"},l.createElement(Ye.Icon,{className:"brui-text-12 brui-text-blue payment-mt-3",iconClass:"bi_brui",iconName:"bi_check_light"}),l.createElement("span",{className:"brui-text-16 brui-leading-20 payment-ml-10"},E()?r.formatMessage({id:"AUTOPAY_ALERT_INCREASE"}):_()||y()?r.formatMessage({id:"AUTOPAY_ALERT"}):"")):""),l.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},r.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_NUMBER"})," ",l.createElement("strong",null,a[0].PaymentConfirmationNumber)),l.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},l.createElement(He.FormattedMessage,{id:"ALERT_CONFIRMATION_SUCCESS_DESC",values:{email:l.createElement("strong",null,a[0].ConfirmationEmailAddress)}})," ",l.createElement(Ye.Link,{variant:"textBlue",size:"small",href:"/MyProfile/EditProfile?editField=EMAIL_ADDRESS",className:""},r.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))},vt=(0,He.injectIntl)(gt),Nt={greatNews:{className:"brui-text-15 brui-text-blue",iconClass:"bi_brui",iconName:"bi_tag_note-big"},notifCardWarning:{className:"brui-text-20 brui-text-yellow",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}},At=function(e){var t=e.intl,n=e.hasNotifCard,r=void 0!==n&&n,a=e.children,i=e.label,o=e.variant,u=t.formatMessage({id:"ALERT_GREAT_NEWS_NOTE"}),s=t.formatMessage({id:"ALERT_GREAT_NEWS_NOTE_DESC"});return l.createElement(Ye.Card,{variant:"gray",radius:!0,className:["brui-flex brui-flex-col sm:brui-flex-row brui-p-15 brui-gap-15 brui-rounded-[16px]",r?"":"brui-hidden"].join(" ").trim()},l.createElement("div",{className:"brui-flex brui-size-20 brui-items-start payment-pb-15 payment-pr-15"},l.createElement(Ye.Icon,{className:["",Nt[o].className].join(" ").trim(),iconClass:["",Nt[o].iconClass].join(" ").trim(),iconName:["",Nt[o].iconName].join(" ").trim()})),l.createElement("div",{className:"brui-flex-grow"},l.createElement("p",{className:"brui-text-14 brui-leading-18 brui-text-gray brui-mb-10"},i),a,l.createElement("div",{className:"brui-text-12 brui-text-gray brui-leading-14"},l.createElement("strong",null,u),s)))},(0,He.injectIntl)(At),Ct=function(e){var t=e.intl,n=e.isErrorCardNumber,r=e.isErrorCardName,a=e.isErrorExpiryDate,i=e.isErrorSecurityCode,o=e.isErrorBankAccountHolderName,u=e.isErrorBankAccountNumber,s=e.isErrorBankName,c=e.iserrorBankTransit,m=e.inputRefs;return l.createElement(Ye.Alert,{variant:"error",className:"payment-block sm:payment-flex brui-px-0 sm:payment-px-30 payment-py-30 brui-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative brui-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-3"},l.createElement(Ye.Text,{elementType:"div",className:"payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:" sm:payment-mt-7 brui-mb-15 brui-font-sans brui-leading-22"},l.createElement("span",{id:"error-1","aria-hidden":"true"},t.formatMessage({id:"ALERT_ERROR_HEADING"})),l.createElement("span",{className:"payment-sr-only"},t.formatMessage({id:"ALERT_ERROR_HEADING_SR"}))),l.createElement("div",null,l.createElement(pt,null,n&&l.createElement(Et,{id:"error-2",label:t.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==m?void 0:m.inputCreditCardNumber}),r&&l.createElement(Et,{id:"error-3",label:t.formatMessage({id:"CREDIT_CARD_NAME_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==m?void 0:m.inputCreditCardHolderName}),a&&l.createElement(Et,{id:"error-4",label:t.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==m?void 0:m.inputCreditCardExpiryMonth}),i&&l.createElement(Et,{id:"error-5",label:t.formatMessage({id:"CREDIT_CARD_SECURITY_CODE_INPUT_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==m?void 0:m.inputCreditCardSecurityCode}),s&&l.createElement(Et,{id:"error-2",label:t.formatMessage({id:"BANK_NAME_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==m?void 0:m.inputBankName}),o&&l.createElement(Et,{id:"error-3",label:t.formatMessage({id:"BANK_HOLDER_NAME_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==m?void 0:m.inputBankAccountHolder}),c&&l.createElement(Et,{id:"error-4",label:t.formatMessage({id:"BANK_TRANSIT_NUMBER_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==m?void 0:m.inputTransitNumber}),u&&l.createElement(Et,{id:"error-5",label:t.formatMessage({id:"BANK_ACCOUNT_NUMBER_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==m?void 0:m.inputBankAccountNumber})))))},ht=(0,He.injectIntl)(Ct),Tt=function(e){var t=e.intl,n=e.interact;return l.createElement(Ye.Alert,{variant:"error",className:"payment-block sm:payment-flex brui-px-0 sm:payment-px-30 payment-py-30 brui-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative brui-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-3"},l.createElement(Ye.Text,{elementType:"div",className:"payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0 md:payment-pt-7"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:" sm:brui-mt-7 brui-mb-15 md:payment-mb-7 brui-font-sans brui-leading-22"},l.createElement("span",{"aria-hidden":"true"},(n.includes($e),t.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC"}))),l.createElement("span",{className:"payment-sr-only"},(n.includes($e),t.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC_SR"})))),l.createElement("div",null,l.createElement("p",{className:"brui-text-14 brui-my-15 brui-text-gray"},(n.includes($e),t.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC_DESC"}))))))},It=(0,He.injectIntl)(Tt),Rt=function(e){var t=e.intl,n=e.checkedCurrentBalanceItems,r=e.submitMultiOrderPayment,a=e.accountInputValue,i=e.language,o=e.notOptedBalanceItems,u=t.formatMessage({id:"ALERT_ERROR_OTP_ALL_BALANCE"}),s=t.formatMessage({id:"ALERT_ERROR_HEADING_SOME_BALANCE"}),c=t.formatMessage({id:"ALERT_ERROR_OTP_BALANCE_DESC"}),m=t.formatMessage({id:"ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR"}),d=t.formatMessage({id:"ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL"}),p=r.filter(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)}),f=p.map(function(e){return null==e?void 0:e.OrderFormId}),b=a.filter(function(e){return f.includes(e.transactionID)}).map(function(e){return e.accountNumber}),E=null==n?void 0:n.filter(function(e){return b.includes(e.Ban)}),_=n,y=_.length>E.length,g=_.length==E.length&&p.length>1,v=1==_.length?t.formatMessage({id:"ALERT_ERROR_OTP_BALANCE"},{balance:_[0].DueStr}):"",N=1==_.length?t.formatMessage({id:"ALERT_ERROR_OTP_BALANCE_SR"},{balance:_[0].DueStr}):"";return l.createElement(Ye.Alert,{variant:"error",className:"payment-block payment-border payment-rounded-20 sm:payment-flex payment-px-15 sm:payment-px-30 payment-py-30 sm:payment-pb-45 payment-border-y-1 brui-relative brui-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-otp-fail"},l.createElement(Ye.Text,{elementType:"div",className:"payment-pl-0 payment-pt-15 sm:payment-pl-15 sm:payment-pt-0"},l.createElement(Ye.Heading,{level:"h3",variant:"xs",className:"payment-mb-5 payment-font-sans brui-text-red"},1==_.length&&l.createElement(l.Fragment,null,l.createElement("span",{"aria-hidden":"true",dangerouslySetInnerHTML:{__html:v}}),l.createElement("span",{className:"payment-sr-only"},N)),y&&l.createElement("span",{dangerouslySetInnerHTML:{__html:s}}),g&&l.createElement("span",{dangerouslySetInnerHTML:{__html:u}})),l.createElement(Ye.Text,{elementType:"div"},l.createElement("span",{className:"payment-text-14 payment-leading-18 payment-text-gray",dangerouslySetInnerHTML:{__html:c}})),(y||g)&&l.createElement("div",{className:"!payment-border-none payment-mt-15"},l.createElement(pt,{label:""},E.map(function(e){return l.createElement(Et,{label:e.NickName,labelDescription:e.Due,variant:"priceList",priceSettings:{language:i,showZeroDecimalPart:!0}})})),y&&o.length>0&&l.createElement("div",{className:"payment-border-t-gray-4 payment-mt-15"},l.createElement(pt,{label:1==o.length?m:d},o.map(function(e){return l.createElement(Et,{label:e.NickName,labelDescription:e.Due,variant:"priceList",priceSettings:{language:i,showZeroDecimalPart:!0}})})))),l.createElement(Ye.Button,{className:"payment-mt-30",variant:"primary",size:"regular",onClick:function(){location.href="".concat(t.formatMessage({id:"CTA_MAKE_PAYMENT_LINK"}))}},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))},(0,He.injectIntl)(Rt),xt=function(e){var t=e.intl.formatMessage({id:"CANCEL_FAILED_SUCCESS_SCENARIO_HEADING"});return l.createElement(Ye.Alert,{variant:"success",className:"payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex sm:payment-items-center payment-block",iconSize:"36",id:"alert-3"},l.createElement(Ye.Text,{elementType:"div",className:"payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:"payment-font-sans payment-leading-22"},l.createElement("span",{dangerouslySetInnerHTML:{__html:t}}))))},St=(0,He.injectIntl)(xt),Ot=function(e){var t=e.intl,n=e.multiBanFail,r=e.children,a=n?t.formatMessage({id:"CANCEL_FAILED_HEADING_MULTI"}):t.formatMessage({id:"CANCEL_FAILED_HEADING_SINGLE"});return l.createElement(Ye.Alert,{variant:"error",className:"payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block",iconSize:"36",id:"alert-2"},l.createElement(Ye.Text,{elementType:"div",className:"payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0 payment-flex-1"},l.createElement(Ye.Heading,{level:"h2",variant:"xs",className:"payment-mb-5 payment-font-sans payment-text-red "},l.createElement("span",{dangerouslySetInnerHTML:{__html:a}})),l.createElement("div",{className:"payment-mt-15"},r),l.createElement(Ye.Button,{className:"payment-mt-15",variant:"primary",size:"regular",onClick:function(){window.location.reload()}},t.formatMessage({id:"FAILURE_API_BAN_BUTTON"}))))},Mt=(0,He.injectIntl)(Ot),Lt=e(10),kn=c.ApplicationError,(0,o.__extends)(function(e,t){void 0===e&&(e=""),void 0===t&&(t=Dt.None);var n=kn.call(this,e)||this;return n.errorType=t,n},kn),function(e){(0,o.__extends)(function(t,n,r,a,i){void 0===i&&(i=Dt.WebAPIError);var o=e.call(this,n)||this;return o.statusCode=t,o.message=n,o.serverMessage=r,o.apiName=a,o.errorType=i,o},e)}(c.ApplicationError),function(e){e[e.None=0]="None",e[e.NoDataError=1]="NoDataError",e[e.WebAPIError=2]="WebAPIError",e[e.GetPreviousBillAPIError=3]="GetPreviousBillAPIError",e[e.Exception=4]="Exception",e[e.ApplicationError=5]="ApplicationError",e[e.NoTooltipDataError=6]="NoTooltipDataError",e[e.GetOverageAPIError=7]="GetOverageAPIError"}(Dt||(Dt={})),function(e){e.Empty="EMPTY",e.CreditCardExpireDate="CREDIT_CARD_EXPIRY",e.Invalid="INVALID"}(Pt||(Pt={})),Bt=function(){},kt=e(20),wt=function(e){var t,n,r,a,o,u,s,c,m,d,p,f,b,E,_,y,g,v,N,A,C,h,T=e.intl,I=e.className,R=e.paymentItem,x=e.isPreauth,S=e.inputValue,O=e.inputBankValue,M=e.isBankPaymentSelected,L=e.isNewbank,D=e.onEditClick,P=e.showHeading,B=(e.isSingleClickEnable,e.bankList),k=e.debitCardAutopayOffers,w=e.creditCardAutopayOffers,U=e.checkedBillItems,F=e.bankitems,H=e.isConfirmation,Y=e.isShow;return t=function(){var e=[];return M?k&&(null==k||k.map(function(t){U&&U.map(function(n){t.Ban==n.BillName&&e.push(t)})})):w&&(null==w||w.map(function(t){U&&(null==U||U.map(function(n){t.Ban==n.BillName&&e.push(t)}))})),e},n=function(){return F&&F.length>1?t().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):M?k&&k.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):w&&w.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)},r={label:T.formatMessage({id:"PAYMENT_METHOD"}),credits:F&&F.length>1?t():k},a={label:T.formatMessage({id:"PAYMENT_METHOD"}),credits:F&&F.length>1?t():w},o=function(){var e;return(null===(e=null==r?void 0:r.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"REMOVE"===e.offerImpact})})}))||!1},u=function(){var e;return(null===(e=null==r?void 0:r.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return("GAIN"===e.offerImpact||""===e.offerImpact||null===e.offerImpact)&&"RETAIN"!==e.offerImpact})})}))||!1},s=function(){var e;return(null===(e=null==r?void 0:r.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"REDUCE"===e.offerImpact})})}))||!1},c=function(){var e;return(null===(e=null==a?void 0:a.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"INCREASE"===e.offerImpact})})}))||!1},m=R.filter(function(e){return!0===e.IsOnPreauthorizedPayments&&e.CreditCardDetails}),d=m.length>0?m[0].CreditCardDetails:null,p=function(){var e,t;if(L||!1===x)return 1==M?[{label:T.formatMessage({id:"PAYMENT_METHOD"}),value:null==O?void 0:O.PaymentMethod},{label:T.formatMessage({id:"ACCOUNT_HOLDER"}),value:null==O?void 0:O.AccountHolder},{label:T.formatMessage({id:"BANK_NAME"}),value:null===(e=B.filter(function(e){return e.Value==(null==O?void 0:O.BankName)})[0])||void 0===e?void 0:e.Text},{label:T.formatMessage({id:"TRANSIT_NUMER"}),value:null==O?void 0:O.TransitNumber},{label:T.formatMessage({id:"ACCOUNT_NUMBER"}),value:(null==O?void 0:O.AccountNumber)?"*******".concat(String(O.AccountNumber).slice(-3)):null==O?void 0:O.AccountNumber}]:[{label:T.formatMessage({id:"CREDIT_CARD_TYPE_LABEL"}),value:S.cardType},{label:T.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"}),value:L&&S.cardNumber?"*******".concat(String(S.cardNumber).slice(-4)):S.cardNumber},{label:T.formatMessage({id:"CREDIT_CARD_NAME_LABEL"}),value:S.cardName},{label:T.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),value:S.expiryDate}];if(!1===L||!0===x){if(1==M)return[{label:T.formatMessage({id:"PAYMENT_METHOD"}),value:null==O?void 0:O.PaymentMethod},{label:T.formatMessage({id:"ACCOUNT_HOLDER"}),value:null==O?void 0:O.AccountHolder},{label:T.formatMessage({id:"BANK_NAME"}),value:null===(t=B.filter(function(e){return e.Value==(null==O?void 0:O.BankName)})[0])||void 0===t?void 0:t.Text},{label:T.formatMessage({id:"TRANSIT_NUMER"}),value:null==O?void 0:O.TransitNumber},{label:T.formatMessage({id:"ACCOUNT_NUMBER"}),value:(null==O?void 0:O.AccountNumber)?"*******".concat(String(O.AccountNumber).slice(-3)):null==O?void 0:O.AccountNumber}];if(S)return[{label:T.formatMessage({id:"CREDIT_CARD_TYPE_LABEL"}),value:S.cardType},{label:T.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"}),value:L&&S.cardNumber?"*******".concat(String(S.cardNumber).slice(-4)):S.cardNumber},{label:T.formatMessage({id:"CREDIT_CARD_NAME_LABEL"}),value:S.cardName},{label:T.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),value:S.expiryDate}];if(d)return[{label:T.formatMessage({id:"CREDIT_CARD_TYPE_LABEL"}),value:i(d.CreditCardType)},{label:T.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"}),value:null==d?void 0:d.CreditCardNumberMasked},{label:T.formatMessage({id:"CREDIT_CARD_NAME_LABEL"}),value:null==d?void 0:d.CardholderName},{label:T.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),value:null==d?void 0:d.ExpirationDateDisplayViewA}]}return[]},f=function(){var e;return(null===(e=null==r?void 0:r.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"INCREASE"===e.offerImpact})})}))||!1},b=function(){var e;return(null===(e=null==a?void 0:a.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"REDUCE"===e.offerImpact})})}))||!1},E={label:n()>1?T.formatMessage({id:"REVIEW_PAGE_AUTOPAY_CREDITS"}):T.formatMessage({id:"REVIEW_PAGE_AUTOPAY_CREDIT"})},_={label:n()>1?T.formatMessage({id:"LABEL_LOADED_OFFERS_REMOVED_DEBIT_SUMMARY"}):T.formatMessage({id:"LABEL_LOADED_OFFER_REMOVED_DEBIT_SUMMARY"})},y={label:n()>1?T.formatMessage({id:"LABEL_LOADED_OFFERS_REDUCED_DEBIT_TITLE"}):T.formatMessage({id:"LABEL_LOADED_OFFER_REDUCED_DEBIT_TITLE"})},g={label:n()>1?T.formatMessage({id:"LABEL_LOADED_OFFERS_INCREASED_CREDIT_TITLE"}):T.formatMessage({id:"LABEL_LOADED_OFFER_INCREASED_CREDIT_TITLE"})},v=function(){var e;return(null===(e=null==a?void 0:a.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return("GAIN"===e.offerImpact||""===e.offerImpact||null===e.offerImpact)&&"RETAIN"!==e.offerImpact})})}))||!1},N=function(){var e;return(null===(e=null==a?void 0:a.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"REMOVE"===e.offerImpact})})}))||!1},A={label:n()>1?T.formatMessage({id:"STACKED_AUTOPAY_CREDITS"}):T.formatMessage({id:"STACKED_AUTOPAY_CREDIT"})},C=u()&&o()||s()&&o()||f()&&o(),h=v()&&N()||c()&&N()||b()&&N(),l.createElement("div",{className:I},l.createElement("div",{className:"payment-border-gray-4"},l.createElement("div",{className:P?"payment-flex payment-items-center payment-justify-between payment-mt-0":"payment-hidden"},l.createElement(Ye.HeadingStep,{disableSrOnlyText:!0,status:"complete",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:T.formatMessage({id:"PAYMENT_CHANGE_HEADING"}),id:Y?"payment-manage-heading":void 0,"aria-hidden":Y?"true":void 0}),l.createElement("div",{className:"payment-pt-45"},l.createElement(Ye.IconLink,{icon:l.createElement(Ye.Icon,{iconClass:"bi_brui","aria-hidden":!0,iconName:"bi_edit_pencil",className:"brui-text-16"}),text:T.formatMessage({id:"CTA_EDIT"}),variant:"textBlue",size:"regular",href:"javascript:void(0);",position:"right","aria-label":T.formatMessage({id:"EDIT_SR_TEXT"}),className:["payment-flex payment-items-center !payment-text-14 !payment-leading-18"].join(" ").trim(),onClick:D}))),H||M||!b()?null:l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"payment-text-yellow paymnet-text-15 payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},y.label," ")),!H&&M&&f()?l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"paymnet-text-15 payment-text-blue payment-mr-5",iconClass:"bi_brui",iconName:"bi_tag_note-big"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},g.label," ")):null,!H&&M&&u()?l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"paymnet-text-15 payment-text-blue payment-mr-5",iconClass:"bi_brui",iconName:"bi_tag_note-big"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},E.label," ")):null,H||M||!v()?null:l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"paymnet-text-15 payment-text-blue payment-mr-5",iconClass:"bi_brui",iconName:"bi_tag_note-big"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},E.label," ")),!H&&M&&s()?l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"payment-text-yellow paymnet-text-15 payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},y.label," ")):null,H||M||!c()?null:l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"paymnet-text-15 payment-text-blue payment-mr-5",iconClass:"bi_brui",iconName:"bi_tag_note-big"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},g.label," ")),!H&&M&&o()?l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"payment-text-yellow paymnet-text-15 payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},_.label," ")):null,H||M||!N()?null:l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"payment-text-yellow paymnet-text-15 payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},_.label," ")),!H&&M&&(o()||u()||s()||f())&&C?l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"payment-text-yellow paymnet-text-15 payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},A.label," ")):null,!H&&!M&&(N()||v()||c()||b())&&h?l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"payment-text-yellow paymnet-text-15 payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},A.label," ")):null,M?l.createElement("div",{className:"payment-block payment-relative"},l.createElement("div",{className:"brui-mt-15"},p().map(function(e,t){return l.createElement(kt.SingleRowInformation,{className:"",label:e.label,value:e.value,"data-index":t,isAccountNumber:e.label===T.formatMessage({id:"ACCOUNT_NUMBER"}),needSRText:e.label===T.formatMessage({id:"ACCOUNT_NUMBER"}),srText:e.label===T.formatMessage({id:"ACCOUNT_NUMBER"})?T.formatMessage({id:"REVIEW_BANK_ACCOUNT_NUMBER"}):"",itemValue:e.label===T.formatMessage({id:"ACCOUNT_NUMBER"})?String(e.value).slice(-3):""})}))):l.createElement("div",{className:"payment-block payment-relative"},l.createElement("div",{className:"brui-mt-15"},p().map(function(e,t){return l.createElement(kt.SingleRowInformation,{className:"",label:e.label,value:e.label===T.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"})?"*******".concat(String(e.value).slice(-4)):e.value,isAccountNumber:e.label===T.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"}),"data-index":t,needSRText:e.label===T.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"}),srText:e.label===T.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"})?T.formatMessage({id:"REVIEW_CREDIT_CARD_NUMBER"}):"",itemValue:e.label===T.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"})?String(e.value).slice(-4):""})})))))},Ut=(0,He.injectIntl)(wt),Ft=function(e,t,n,r,a){if(r)return{s_oPYM:"Cancel payment",error:"",s_oAPT:"332-1-0"};if(e)if(t){if(n.length>0&&n[0].BankAccountDetails)return{s_oPYM:"Bank payment",error:"BANKERROR",s_oAPT:"327-2-2",s_oILI:a};if(n.length>0&&n[0].CreditCardDetails)return{s_oPYM:"Bank payment",error:"BANKERROR",s_oAPT:"331-2-2",s_oILI:a}}else{if(n.length>0&&n[0].BankAccountDetails)return{s_oPYM:"Credit card",error:"CREDITERROR",s_oAPT:"328-2-2",s_oILI:a};if(n.length>0&&n[0].CreditCardDetails)return{s_oPYM:"Credit card",error:"CREDITERROR",s_oAPT:"330-2-2",s_oILI:a}}else if(t){if(n.length>0&&n[0].BankAccountDetails)return{s_oPYM:"Bank payment",error:"",s_oAPT:"327-1-0",s_oILI:a};if(n.length>0&&n[0].CreditCardDetails)return{s_oPYM:"Bank payment",error:"",s_oAPT:"331-1-0",s_oILI:a}}else{if(n.length>0&&n[0].BankAccountDetails)return{s_oPYM:"Credit card",error:"",s_oAPT:"328-1-0",s_oILI:a};if(n.length>0&&n[0].CreditCardDetails)return{s_oPYM:"Credit card",error:"",s_oAPT:"330-1-0",s_oILI:a}}return{s_oPYM:"",error:"",s_oAPT:"",s_oILI:""}},Ht=function(e,t,n,r){if(e){if(t.length>0&&t[0].BankAccountDetails)return{s_oPYM:"Bank payment",s_oAPT:"327-2-1",s_OPID:n[0].PaymentConfirmationNumber};if(t.length>0&&t[0].CreditCardDetails)return{s_oPYM:"Bank payment",s_oAPT:"331-2-1",s_OPID:n[0].PaymentConfirmationNumber}}else if(!e){if(t.length>0&&t[0].BankAccountDetails)return{s_oPYM:"Credit card",s_oAPT:"328-2-1",s_oCCDT:{CreditCardType:r},s_OPID:n[0].PaymentConfirmationNumber};if(t.length>0&&t[0].CreditCardDetails)return{s_oPYM:"Credit card",s_oAPT:"330-2-1",s_oCCDT:{CreditCardType:r},s_OPID:n[0].PaymentConfirmationNumber}}return{s_oPYM:"",s_oCCDT:""}},Yt=function(e,t,n){if(e){if(t.length>0&&t[0].BankAccountDetails)return{s_oPYM:"Bank payment",s_oAPT:"327-2-2"};if(t.length>0&&t[0].CreditCardDetails)return{s_oPYM:"Bank payment",s_oAPT:"331-2-2"}}else if(!e){if(t.length>0&&t[0].BankAccountDetails)return{s_oPYM:"Credit card",s_oAPT:"328-2-2",s_oCCDT:{CreditCardType:n}};if(t.length>0&&t[0].CreditCardDetails)return{s_oPYM:"Credit card",s_oAPT:"330-2-2",s_oCCDT:{CreditCardType:n}}}return{s_oPYM:"",s_oAPT:"",s_oCCDT:""}},jt=function(e,t,n){if(e){if(t.length>0&&t[0].BankAccountDetails)return{s_oPYM:"Bank payment",s_oAPT:"327-2-2"};if(t.length>0&&t[0].CreditCardDetails)return{s_oPYM:"Bank payment",s_oAPT:"331-2-2"}}else if(!e){if(t.length>0&&t[0].BankAccountDetails)return{s_oPYM:"Credit card",s_oAPT:"328-2-2",s_oCCDT:{CreditCardType:n}};if(t.length>0&&t[0].CreditCardDetails)return{s_oPYM:"Credit card",s_oAPT:"330-2-2",s_oCCDT:{CreditCardType:n}}}return{s_oPYM:"",s_oAPT:"",s_oCCDT:""}},Gt=function(e){var t,n,a,u,s,c,m,d,p,f,b=e.intl,E=e.Checked,_=e.paymentItems,y=e.checkedBillItems,g=e.managePreauth,v=e.setCancelPreauthSectionClicked,N=(e.cancelPreauthSectionClicked,e.removedSubscriberOffers),C=e.province,h=e.IsAutopayCreditEnabled,T=e.setOmnitureOnPaymentSelect,I=(e.language,e.onChange);return 1===_.length?n=_:_.length>1&&(n=y.filter(function(e){return e.IsOnPreauthorizedPayments}),t=y.filter(function(e){return!e.IsOnPreauthorizedPayments})),a={MyBill:b.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:b.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:b.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:b.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:b.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:b.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:b.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:b.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})},u=function(e){var t;return(e.IsOnPreauthorizedPayments||g&&"Creditcard"===g&&e.CreditCardDetails)&&e.CreditCardDetails?t=b.formatMessage({id:"SELECT_BILLS_CC_DESC"},{CreditCardType:i(e.CreditCardDetails.CreditCardType),CCFourDigits:e.CreditCardDetails.CreditCardNumber.slice(-4),ExpiryDate:e.CreditCardDetails.ExpireMonth+"/"+e.CreditCardDetails.ExpireYear}):(e.IsOnPreauthorizedPayments||g&&"Debit"===g&&e.BankAccountDetails)&&e.BankAccountDetails&&(t=b.formatMessage({id:"SELECT_BILLS_BANK_DESC"},{BankName:e.BankAccountDetails.BankName,Code:e.BankAccountDetails.TransitCode,BankMaskedDigits:e.BankAccountDetails.AccountNumberMaskedDisplayView})),t},s=function(e){e.target.checked?(v(!0),T(Ft(!1,!0,y,!0,""))):v(!1)},c="",t&&t.length>0&&t.forEach(function(e,t){var n,r;c=0===t?null!==(n=e.NickName)&&void 0!==n?n:e.BillName:c+", "+(null!==(r=e.NickName)&&void 0!==r?r:e.BillName)||""}),m=function(e,t){var n=null==t?void 0:t.reduce(function(t,n){var r;return(null==e?void 0:e.some(function(e){return e.Ban===n.Ban}))&&(null===(r=n.AutopayEligibleSubscribers)||void 0===r||r.forEach(function(e){var r;t[n.banInfo.nickName]||(t[n.banInfo.nickName]=[]),null===(r=e.autopayOffers)||void 0===r||r.forEach(function(r){t[n.banInfo.nickName].push({phoneNumber:e.subscriberTelephoneNumber,discountAmount:r.discountAmount})})})),t},{}),r=function(e){var t=!1;if(Object.keys(e).length>0){if(Object.keys(e).length>1)return!0;Object.keys(e).forEach(function(n){e[n].length>1&&(t=!0)})}return t};return l.createElement(it,{hasNotifCard:Object.keys(n).length>0,variant:"notifCardWarning",label:r(n)?b.formatMessage({id:"LABEL_LOADED_OFFERS_REMOVED_DEBIT_TITLE"}):b.formatMessage({id:"LABEL_LOADED_OFFER_REMOVED_DEBIT_TITLE"}),label2:b.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING"}),label3:r(n)?b.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE"}):b.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE"})},Object.entries(n).map(function(e){var t=(0,o.__read)(e,2),r=t[0],a=t[1];return l.createElement("div",{key:r},Object.keys(n).length>1&&l.createElement("p",{className:"payment-text-14 payment-text-gray payment-mb-5"},r,":"),l.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10"},a.map(function(e,t){return l.createElement(Ye.ListItem,{key:t,className:"payment-text-14 payment-text-gray payment-mb-5 payment-leading-18"},e.phoneNumber," - ",l.createElement("div",{className:"brui-font-bold brui-text-18 brui-text-blue brui-text-darkblue payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal","aria-hidden":"true"},l.createElement("span",null,b.formatMessage({id:"DEBIT_PAYMENT_OFFER_AMOUNT"},{amount:e.discountAmount}))),l.createElement("span",{className:"brui-sr-only"},b.formatMessage({id:"DEBIT_PAYMENT_OFFER_AMOUNT_SR"},{amount:e.discountAmount})))})))}))},d="NB"===C||"NL"===C||"NS"===C||"PE"===C,p="MB"===C,f=b.formatMessage({id:"CANCEL_PAYMENT_LABEL"}),l.createElement("div",{className:"payment-mb-15"},l.createElement(je,{id:"payment-radio-cancel",name:"payment-radio",label:f,headingLevel:"h3",checked:E,onChange:function(e){s(e),null==I||I(e)}},l.createElement("div",null,l.createElement(Ye.Heading,{variant:"default",level:"h4",className:"payment-font-sans payment-text-black payment-text-18 payment-text-left payment-leading-22 payment-font-bold"},b.formatMessage({id:"CANCEL_PREAUTH_SUBHEADING"})),l.createElement("span",{className:"payment-text-gray payment-text-14 payment-leading-18 payment-mt-5"},b.formatMessage({id:"CANCEL_PREAUTH_SUBHEADING_INFO"}))),l.createElement("div",{className:"payment-pt-30"},l.createElement(Ye.Alert,{variant:"warning",iconSize:"36",className:"sm:payment-flex sm:payment-border sm:payment-border-gray-4 sm:payment-rounded-[16px] sm:payment-p-30"},l.createElement("div",{className:"payment-w-full payment-mt-15 sm:payment-mt-0 sm:payment-ml-15"},l.createElement("div",null,l.createElement(Ye.Heading,{variant:"default",level:"h4",className:"payment-font-sans payment-text-black payment-text-18 payment-text-left payment-leading-22 payment-font-bold"},b.formatMessage({id:"FINAL_PAYMENT"})),l.createElement(Ye.Text,{elementType:"span",className:"payment-text-14 payment-text-gray payment-leading-18 payment-mt-5"},b.formatMessage({id:"FINAL_PAYMENT_HEADING"}))),n&&n.length>0&&l.createElement("div",null,l.createElement("div",null,l.createElement(dt,{className:"payment-mt-30",listClassName:"payment-bg-gray-3 payment-rounded-10 payment-pr-15 payment-pl-15"},n.map(function(e,t){var n;return l.createElement(bt,{variant:"accountList",label:r(e.AccountType,e.IsNM1Account,a),labelDescription:null!==(n=e.NickName)&&void 0!==n?n:e.BillName,cardDetails:u(e),className:"payment-bg-gray-3 payment-pb-15 last:payment-border-0 payment-border-b payment-border-gray-4"})}))),t&&t.length>0&&""!==c&&void 0!==c&&l.createElement("div",{className:"payment-mt-30"},l.createElement("p",{className:"payment-text-gray payment-text-12 payment-leading-14",dangerouslySetInnerHTML:{__html:b.formatMessage({id:"NOT_PREAUTH_NOTE"},{account:c})}})))))),h&&l.createElement("div",{className:"payment-pt-15 sm:payment-pt-30"},!d&&!p&&y&&void 0!==N&&m(y,N),d||p||y.some(function(e){return e.IsOnPreauthorizedPayments&&e.accountType===A.PaymentItemAccountType.OneBill})?l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5"},l.createElement("p",{className:"payment-text-gray payment-text-14",dangerouslySetInnerHTML:{__html:b.formatMessage({id:"CANCEL_PREAUTH_POSSIBLE_CREDITS_NOTE"})}})):null)))},Vt=(0,He.injectIntl)(Gt),zt=function(e){var t,n,r,u,s,c,m,d,p,f,b,E,_,y,g,N,C,h,T,I,R,x,S,O,M,L,D,P,B,k,w,U,F,H,Y,j=e.intl,G=e.isHeadingStepActive,V=e.paymentItem,z=e.creditcardDetails,q=e.onCreditCardNumberChange,K=e.onCardHolderNameChange,W=e.onCreditCardExpiryDateChange,X=e.onSecurityCodeChange,Q=e.resetValidationErrors,Z=e.validationErrors,$=e.isSingleClickEnableForPAD,J=e.isSingleClickEnableForPACC,ee=(e.onCurrentSteps,e.setHeadingSteps),te=e.setCurrentSection,ne=e.currentSection,re=e.setInputValue,ae=e.inputValue,ie=e.setInputBankValue,oe=e.inputBankValue,le=e.setIsBankSelected,ue=e.validateFormOrder,se=e.checkedBillItems,ce=e.tokenizeAndPropagateFormValues,me=e.bankList,de=e.validatBankDetails,pe=e.cardTokenizationSuccess,fe=e.redirectUrl,be=e.interacBankInfo,Ee=e.accountInputValues,_e=e.interactBankFailureInfo,ye=e.creditCardAutopayOffers,ge=e.debitCardAutopayOffers,ve=e.removedSubscriberOffers,Ne=e.province,Ae=e.language,Ce=e.setOmnitureOnPaymentSelect,he=e.managePreauth,Te=e.setCancelPreauthSectionClicked,Ie=e.cancelPreauthSectionClicked,Re=e.cancelPreauthPaymentsAction,xe=e.cancelPreauthStatus,Se=e.setIsModalOpen,Oe=e.setApiSatusIsFailed,Me=e.setOmnitureOnCancelationCompleted,Le=e.IsAutopayCreditEnabled,De=e.IsInteracEnabled,Pe=e.InteracCode,Be=e.setSomeCancellationFailed,ke=e.cancelPreauthPayments,we=e.setBansCancellationFailed,Ue=e.setOmnitureOnCancelationPartiallyCompleted,Fe=e.setOmnitureOnCancelationFailed,He=e.setOmnitureOnCancelationPartiallyFailed,je=e.setCancellationPaymentFailStatus,Ge=e.setOmnitureOnInteracFailure,Ve=(0,o.__read)(l.useState(xe),2),ze=Ve[0],qe=Ve[1],We=(0,o.__read)(l.useState(ke),2),Ze=We[0],$e=We[1],Je=(0,o.__read)(l.useState(null==z?void 0:z.CreditCardNumber),2),et=Je[0],tt=Je[1],nt=(0,o.__read)(l.useState("default"),2),rt=nt[0],at=nt[1],it=(0,o.__read)(l.useState(""),2),ot=it[0],lt=it[1],ut=(0,o.__read)(l.useState(!1),2),ct=ut[0],dt=ut[1],pt=(0,o.__read)(l.useState(!1),2),ft=pt[0],bt=pt[1],Et=(0,o.__read)(l.useState(!1),2),_t=Et[0],yt=Et[1],gt=(0,o.__read)(l.useState(!1),2),vt=gt[0],Nt=gt[1],At=(0,o.__read)(l.useState(!1),2),Ct=At[0],Tt=At[1],Rt=(0,o.__read)(l.useState(!1),2),xt=Rt[0],St=Rt[1],Ot=(0,o.__read)(l.useState(!1),2),Mt=Ot[0],Lt=Ot[1],Dt=(0,o.__read)(l.useState(!1),2),Pt=Dt[0],kt=Dt[1],wt=(0,o.__read)(l.useState(!1),2),Ht=wt[0],Yt=wt[1],jt=(0,o.__read)(l.useState(j.formatMessage({id:"BANK_ACCOUNT_LABEL"})),2),Gt=jt[0],zt=jt[1],qt=(0,o.__read)(l.useState(""),2),Kt=qt[0],Wt=qt[1],Xt=(0,o.__read)(l.useState(!1),2),Qt=Xt[0],Zt=Xt[1],$t=(0,o.__read)(l.useState(!1),2),Jt=$t[0],en=$t[1],tn=(0,o.__read)(l.useState(!0),2),nn=tn[0],rn=tn[1],an=(0,o.__read)(l.useState(!1),2),on=an[0],ln=an[1],un=(0,o.__read)(l.useState({SelectedPaymentMethod:"",CardholderName:"",CreditCardToken:"",CreditCardType:"",ExpiryYear:"",ExpiryMonth:"",SecurityCode:""}),2),sn=un[0],cn=un[1],mn=(0,o.__read)(l.useState({SelectedPaymentMethod:"",BankName:"",HolderName:"",TransitCode:"",AccountNumber:"",BankCode:""}),2),dn=mn[0],pn=mn[1],fn=j.formatMessage({id:"InteracSupportedFinancialInstitutions"}),bn=fn&&fn.split(","),En=(0,o.__read)(l.useState(!1),2),_n=En[0],yn=En[1],gn=null!==he?j.formatMessage({id:"UPDATE_BANK_ACCOUNT_LABEL"}):j.formatMessage({id:"BANK_ACCOUNT_LABEL"}),vn=(0,o.__read)(l.useState(gn),2),Nn=vn[0],An=vn[1],Cn=(0,o.__read)(l.useState(!0),2),hn=Cn[0],Tn=Cn[1],In=l.useRef(null),Rn={interac:l.useRef(null),manualDetails:l.useRef(null)},xn={inputCreditCardNumber:l.useRef(null),inputCreditCardHolderName:l.useRef(null),inputCreditCardSecurityCode:l.useRef(null),inputCreditCardExpiryMonth:l.useRef(null),inputCreditCardExpiryYear:l.useRef(null),inputBankName:l.useRef(null),inputBankAccountHolder:l.useRef(null),inputTransitNumber:l.useRef(null),inputBankAccountNumber:l.useRef(null)},Sn=function(e){Wt(e.target.value)};return l.useEffect(function(){Kt===j.formatMessage({id:"NEW_CREDIT_ACCOUNT_LABEL"})||Kt===j.formatMessage({id:"BANK_NEW_BANK_ACCOUNT_LABEL"})?Yt(!0):Yt(!1)},[Kt]),f=function(e){lt(e.target.value)},b={VISA:j.formatMessage({id:"VISA_CC_PNG"}),MASTERCARD:j.formatMessage({id:"MASTER_CC_PNG"}),AMEX:j.formatMessage({id:"AMEX_CC_PNG"})},E=function(){te(A.CurrentSection.PaymentMethod)},_=function(){dt(!1),bt(!1),Nt(!1),yt(!1),kt(!1),Lt(!1),Tt(!1),St(!1)},y=j.formatMessage({id:"PAYMENT_METHOD_DEBIT"}),g=function(e){var t,n,r,i,o,l,u,s,c,m,d,p,f,b,E,g,v,A,C,h,T,I,R,x,S,M,D,P,B,k,w,U,F,H;e.preventDefault(),_(),hn?(pn({SelectedPaymentMethod:y,BankName:null===(A=me.filter(function(e){var t;return e.Value==(null===(t=xn.inputBankName.current)||void 0===t?void 0:t.value)})[0])||void 0===A?void 0:A.Text,HolderName:(null===(C=xn.inputBankAccountHolder.current)||void 0===C?void 0:C.value)?null===(h=xn.inputBankAccountHolder.current)||void 0===h?void 0:h.value:(null==L?void 0:L.CardHolder)||"",TransitCode:(null===(T=xn.inputTransitNumber.current)||void 0===T?void 0:T.value)?null===(I=xn.inputTransitNumber.current)||void 0===I?void 0:I.value:(null==L?void 0:L.TransitCode)||"",AccountNumber:(null===(R=xn.inputBankAccountNumber.current)||void 0===R?void 0:R.value)?null===(x=xn.inputBankAccountNumber.current)||void 0===x?void 0:x.value:(null==L?void 0:L.AccountNumber)||"",BankCode:(null===(S=xn.inputBankName.current)||void 0===S?void 0:S.value)?null===(M=xn.inputBankName.current)||void 0===M?void 0:M.value.slice(-3):""}),ie({PaymentMethod:y,AccountHolder:(null===(D=xn.inputBankAccountHolder.current)||void 0===D?void 0:D.value)?null===(P=xn.inputBankAccountHolder.current)||void 0===P?void 0:P.value:(null==L?void 0:L.CardHolder)||"",BankName:(null===(B=xn.inputBankName.current)||void 0===B?void 0:B.value)?null===(k=xn.inputBankName.current)||void 0===k?void 0:k.value:(null==L?void 0:L.BankName)||"",TransitNumber:(null===(w=xn.inputTransitNumber.current)||void 0===w?void 0:w.value)?null===(U=xn.inputTransitNumber.current)||void 0===U?void 0:U.value:(null==L?void 0:L.TransitCode)||"",AccountNumber:(null===(F=xn.inputBankAccountNumber.current)||void 0===F?void 0:F.value)?null===(H=xn.inputBankAccountNumber.current)||void 0===H?void 0:H.value:(null==L?void 0:L.AccountNumber)||""}),de(N())):(cn({SelectedPaymentMethod:"CreditCard",CardholderName:(null===(t=xn.inputCreditCardHolderName.current)||void 0===t?void 0:t.value)?null===(n=xn.inputCreditCardHolderName.current)||void 0===n?void 0:n.value:(null==O?void 0:O.CardholderName)||"",CreditCardToken:(null===(r=xn.inputCreditCardNumber.current)||void 0===r?void 0:r.value)?null===(i=xn.inputCreditCardNumber.current)||void 0===i?void 0:i.value:(null==O?void 0:O.CreditCardNumber)||"",CreditCardType:(null===(o=xn.inputCreditCardNumber.current)||void 0===o?void 0:o.value)?Ke(null===(l=xn.inputCreditCardNumber.current)||void 0===l?void 0:l.value):((null==O?void 0:O.CreditCardType)?a(O.CreditCardType):"")||"",ExpiryYear:(null===(u=xn.inputCreditCardExpiryYear.current)||void 0===u?void 0:u.value)?null===(s=xn.inputCreditCardExpiryYear.current)||void 0===s?void 0:s.value:(null==O?void 0:O.ExpireYear)||"",ExpiryMonth:(null===(c=xn.inputCreditCardExpiryMonth.current)||void 0===c?void 0:c.value)?null===(m=xn.inputCreditCardExpiryMonth.current)||void 0===m?void 0:m.value:(null==O?void 0:O.ExpireMonth)||"",SecurityCode:(null===(d=xn.inputCreditCardSecurityCode.current)||void 0===d?void 0:d.value)?null===(p=xn.inputCreditCardSecurityCode.current)||void 0===p?void 0:p.value:(null==O?void 0:O.SecurityCode)||""}),q(null===(f=xn.inputCreditCardNumber.current)||void 0===f?void 0:f.value),K(null===(b=xn.inputCreditCardHolderName.current)||void 0===b?void 0:b.value),W(null===(E=xn.inputCreditCardExpiryMonth.current)||void 0===E?void 0:E.value,null===(g=xn.inputCreditCardExpiryYear.current)||void 0===g?void 0:g.value),X(null===(v=xn.inputCreditCardSecurityCode.current)||void 0===v?void 0:v.value)),rn(!1),Zt(!0)},l.useEffect(function(){var e,t,n,r,a,o,l,u,s,c;Qt&&(null==Z||Z.errors.map(function(e){switch(e.field){case A.FieldType.CardNumber:dt(!0);break;case A.FieldType.CardHolderName:bt(!0);break;case A.FieldType.ExpirationDate:Nt(!0);break;case A.FieldType.SecurityCode:yt(!0);break;case A.FieldType.BankAccountHolderName:kt(!0);break;case A.FieldType.BankName:Tt(!0);break;case A.FieldType.BankTransitCode:St(!0);break;case A.FieldType.BankAccountNumber:Lt(!0)}}),(l=!!(null===(e=null==Z?void 0:Z.errors)||void 0===e?void 0:e.length)&&Z.errors.length>0)&&yn(!0),l?!l&&h&&!Ht&&R&&(l||C()):(re({cardNumber:(null===(t=xn.inputCreditCardNumber.current)||void 0===t?void 0:t.value)||"",cardType:Qe((null===(n=xn.inputCreditCardNumber.current)||void 0===n?void 0:n.value)||""),cardName:(null===(r=xn.inputCreditCardHolderName.current)||void 0===r?void 0:r.value)||"",expiryDate:"".concat((null===(a=xn.inputCreditCardExpiryMonth.current)||void 0===a?void 0:a.value)||"","/").concat((null===(o=xn.inputCreditCardExpiryYear.current)||void 0===o?void 0:o.value)||"")}),C()),l||(u=function(){var e,t,n,r,a,o,l,u,s,c,m,d,p,f;return{cardHolderName:(null===(e=xn.inputCreditCardHolderName.current)||void 0===e?void 0:e.value)?null===(t=xn.inputCreditCardHolderName.current)||void 0===t?void 0:t.value:(null==O?void 0:O.CardholderName)||"",creditCardNumber:(null===(n=xn.inputCreditCardNumber.current)||void 0===n?void 0:n.value)?null===(r=xn.inputCreditCardNumber.current)||void 0===r?void 0:r.value:(null==O?void 0:O.CreditCardNumber)||"",creditCardToken:(null===(a=xn.inputCreditCardNumber.current)||void 0===a?void 0:a.value)?null===(o=xn.inputCreditCardNumber.current)||void 0===o?void 0:o.value:(null==O?void 0:O.CreditCardNumber)||"",expirationMonth:(null===(l=xn.inputCreditCardExpiryMonth.current)||void 0===l?void 0:l.value)?null===(u=xn.inputCreditCardExpiryMonth.current)||void 0===u?void 0:u.value:(null==O?void 0:O.ExpireMonth)||"",expirationYear:(null===(s=xn.inputCreditCardExpiryYear.current)||void 0===s?void 0:s.value)?null===(c=xn.inputCreditCardExpiryYear.current)||void 0===c?void 0:c.value:(null==O?void 0:O.ExpireYear)||"",securityCode:(null===(m=xn.inputCreditCardSecurityCode.current)||void 0===m?void 0:m.value)?null===(d=xn.inputCreditCardSecurityCode.current)||void 0===d?void 0:d.value:(null==O?void 0:O.SecurityCode)||"",cardType:(null===(p=xn.inputCreditCardHolderName.current)||void 0===p?void 0:p.value)?Ke(null===(f=xn.inputCreditCardHolderName.current)||void 0===f?void 0:f.value):((null==O?void 0:O.CreditCardType)?i(O.CreditCardType):"")||""}}(),1===T.length&&(s=T[0],hn?ue(s.Ban,s.AccountType===A.PaymentItemAccountType.OneBill,dn,Ee,hn,s.subscriberId):ce(u,s.Ban,s.AccountType===A.PaymentItemAccountType.OneBill,sn,hn,s.subscriberId)),T.length>1&&se&&se.length>0&&(hn?ue(se[0].Ban,se[0].AccountType===A.PaymentItemAccountType.OneBill,dn,Ee,hn,se[0].subscriberId):ce(u,se[0].Ban,se[0].AccountType===A.PaymentItemAccountType.OneBill,sn,hn,se[0].subscriberId))),c=new Bt,Q(c),Zt(!1))},[Qt]),l.useEffect(function(){pe&&se&&se.length>0&&ue(se[0].Ban,se[0].AccountType===A.PaymentItemAccountType.OneBill,sn,Ee,hn,se[0].subscriberId)},[pe]),N=function(){var e,t,n,r,a,i,o,l,u,s={isValid:!0,validationForm:{bankNameError:{isEmpty:!1,isInvalid:!1},bankAccountHolderError:{isEmpty:!1,isInvalid:!1},transitNumberError:{isEmpty:!1,isInvalid:!1},bankAccountNumberError:{isEmpty:!1,isInvalid:!1}}};return(null===(e=xn.inputBankName.current)||void 0===e?void 0:e.value)||(s.isValid=!1,s.validationForm.bankNameError.isEmpty=!0),(null===(t=xn.inputBankAccountHolder.current)||void 0===t?void 0:t.value)?(null===(n=xn.inputBankAccountHolder.current)||void 0===n?void 0:n.value)&&(Xe.test(null===(r=xn.inputBankAccountHolder.current)||void 0===r?void 0:r.value.trim())&&(null===(a=xn.inputBankAccountHolder.current)||void 0===a?void 0:a.value.trim().length)<=70||(s.isValid=!1,s.validationForm.bankAccountHolderError.isInvalid=!0)):(s.isValid=!1,s.validationForm.bankAccountHolderError.isEmpty=!0),(null===(i=xn.inputTransitNumber.current)||void 0===i?void 0:i.value)?(null===(o=xn.inputTransitNumber.current)||void 0===o?void 0:o.value.length)<5&&(s.isValid=!1,s.validationForm.transitNumberError.isInvalid=!0):(s.isValid=!1,s.validationForm.transitNumberError.isEmpty=!0),(null===(l=xn.inputBankAccountNumber.current)||void 0===l?void 0:l.value)?(null===(u=xn.inputBankAccountNumber.current)||void 0===u?void 0:u.value.length)<7&&(s.isValid=!1,s.validationForm.bankAccountNumberError.isInvalid=!0):(s.isValid=!1,s.validationForm.bankAccountNumberError.isEmpty=!0),s},C=function(){J||$?te(A.CurrentSection.CurrentBalance):(te(A.CurrentSection.TermsAndCondition),ee(!1))},h=null==V?void 0:V.some(function(e){var t;return null!==(t=e.IsOnPreauthorizedPayments)&&void 0!==t&&t}),T=V,I=V.find(function(e){return e.BankAccountDetails}),R=V.find(function(e){return e.CreditCardDetails}),x=_e&&_e.data?_e.data:null,S=V.filter(function(e){return!0===e.IsOnPreauthorizedPayments&&e.CreditCardDetails}),O=S.length>0?S[0].CreditCardDetails:null,M=V.filter(function(e){return!0===e.IsOnPreauthorizedPayments&&e.BankAccountDetails}),L=M.length>0?M[0].BankAccountDetails:null,D=function(e){tt(e.target.value);var t=Qe(e.target.value);at(t)},P=function(){window.location.href=fe.externalRedirectUrl},B=function(e){var t,n;(null===(n=null===(t=Rn.manualDetails)||void 0===t?void 0:t.current)||void 0===n?void 0:n.checked)?ln(!0):ln(!1)},k=function(e){var t=j.formatMessage({id:"CANCEL_PAYMENT_LABEL"});An(e.target.value),zt(e.target.value),e.target.value!==t&&Te(!1)},w=null!==he?j.formatMessage({id:"UPDATE_CREDITCARD_PAYMENT"}):j.formatMessage({id:"CREDIT_CARD_LABEL"}),l.useEffect(function(){Nn===gn?(Tn(!0),le(!0)):Nn===w&&(Tn(!1),le(!1))},[Nn]),l.useEffect(function(){null!=be&&"SUCCESS"===be.status?en(!0):en(!1)},[be]),l.useEffect(function(){ne==A.CurrentSection.PaymentMethod&&se.length>0&&(Nn!==gn||Ie||x&&nn?Nn!==w||Ie||Ce(Ft(!1,!1,se,!1,Pe)):""===Pe&&Ce(Ft(!1,!0,se,!1,Pe)))},[ne,Nn]),l.useEffect(function(){!(Pe&&""!==Pe&&se.length>0)||x&&nn?nn&&x&&se.length>0&&Nn===gn&&ne==A.CurrentSection.PaymentMethod&&Ge(Ft(!0,hn,se,!1,"")):Nn===gn&&ne==A.CurrentSection.PaymentMethod&&Ce(Ft(!1,!0,se,!1,Pe))},[Pe,ne,se]),l.useEffect(function(){ne==A.CurrentSection.PaymentMethod&&_n&&(Ce(Ft(!0,hn,se,!1,Pe)),yn(!1))},[_n]),U=he&&null!==he,F=function(){Te(!1),I?zt("Bank account"):R&&zt("Credit card")},H=function(){var e,t,n,r,a,i,l,u,s,c=null===(n=se.filter(function(e){return e.IsOnPreauthorizedPayments}))||void 0===n?void 0:n.map(function(e){return e.Ban}),m={PAD:[{banId:"*********",success:!1},{banId:"*********",success:!1},{banId:"*********",success:!0}],PACC:[{banId:"*********",success:!1},{banId:"*********",success:!1},{banId:"*********",success:!0}]};if(c&&c.length>0){r=null,a=function(e){var t=m[e].map(function(e){return e.banId});if(c.some(function(e){return t.includes(e)}))return r=e,"break"};try{for(l=(i=(0,o.__values)(Object.keys(m))).next();!l.done&&"break"!==a(l.value);l=i.next());}catch(d){e={error:d}}finally{try{l&&!l.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}r?(s=(u=m[r].filter(function(e){return c.includes(e.banId)})).every(function(e){return!1===e.success}),qe(s?v.FAILED:v.COMPLETED),$e(u)):Re(c)}},l.useEffect(function(){var e,t,n;ze===v.COMPLETED?Ze&&Object.values(Ze).length>0&&(e=Object.values(Ze))&&e.length>0&&(e.every(function(e){return!0===e.success})?(Se(!0),Me()):(t=e.filter(function(e){return!e.success}).map(function(e){return e.banId}))&&t.length>0&&(n=T.filter(function(e){return t.includes(e.Ban)}),Be(!0),we(n),He(),Ue(),je("FAILED"))):ze===v.FAILED&&(Oe(!0),Fe())},[ze,Ze]),l.useEffect(function(){var e,t,n;xe===v.COMPLETED?ke&&Object.values(ke).length>0&&(e=Object.values(ke))&&e.length>0&&(e.every(function(e){return!0===e.success})?(Se(!0),Me()):(t=e.filter(function(e){return!e.success}).map(function(e){return e.banId}))&&t.length>0&&(n=T.filter(function(e){return t.includes(e.Ban)}),Be(!0),we(n),He(),Ue(),je("FAILED"))):xe===v.FAILED&&(Oe(!0),Fe())},[xe,ke]),Y=he?j.formatMessage({id:"CHANGE_PAYMENT"}):j.formatMessage({id:"SELECT_PAYMENT_METHOD_HEADING"}),l.createElement(l.Fragment,null,l.createElement("div",{className:["payment-border-b payment-border-gray-4",ne>A.CurrentSection.PaymentMethod?"payment-hidden":""].join(" ").trim()},l.createElement("div",null,l.createElement(Ye.HeadingStep,{disableSrOnlyText:!0,tabIndex:-1,status:G,subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:Y,"aria-hidden":ne==A.CurrentSection.PaymentMethod?"true":void 0,id:ne==A.CurrentSection.PaymentMethod?"payment-manage-heading":void 0})),(ct||ft||vt||_t||Pt||Mt||Ct||xt)&&ne!=A.CurrentSection.SelectBills?l.createElement("div",{className:"payment-pt-30"},l.createElement(ht,{isErrorCardNumber:ct,isErrorCardName:ft,isErrorSecurityCode:_t,isErrorExpiryDate:vt,isErrorBankAccountHolderName:Pt,isErrorBankAccountNumber:Mt,isErrorBankName:Ct,iserrorBankTransit:xt,inputRefs:xn})):l.createElement(l.Fragment,null),nn&&x&&l.createElement("div",{className:"".concat("active"===G?"":"payment-hidden"," payment-pt-30")},l.createElement(It,{interact:x})),l.createElement("div",{role:"radiogroup"},l.createElement("div",{className:"".concat("active"===G?"":"payment-hidden"," payment-pt-30")},l.createElement(st,{Checked:(null!==(r=null===(n=null==Gt?void 0:(t=Gt.trim()).toLowerCase)||void 0===n?void 0:n.call(t))&&void 0!==r?r:"")===j.formatMessage({id:"BANK_ACCOUNT_LABEL"}).trim().toLowerCase(),isInteracSelected:Jt,checkedBillItems:se,radioCardRef:Rn,handleBankRadioManualDetailsChange:B,isBankManualEnterDetails:on,isPreauth:h,hasBankAccountDetails:I,bankitems:T,handleBankRadioChange:Sn,bankListInterac:bn,handleInteracSubmit:P,isBankChecked:Ht,inputRefs:xn,errorBankName:Ct,errorBankTransit:xt,errorBankAccountNumber:Mt,errorBankAccountHolderName:Pt,radioRef:In,bankList:me,onChange:k,creditCardAutopayOffers:ye,debitCardAutopayOffers:ge,language:Ae,managePreauth:he,IsAutopayCreditEnabled:Le,IsInteracEnabled:De}),l.createElement(mt,{isPreauth:h,hasCreditCardDetails:R,bankitems:T,radioRef:In,handleBankRadioChange:Sn,isBankChecked:Ht,cardNumber:et,handleCreditCardChange:D,inputRefs:xn,cardIcons:b,cardType:rt,errorCardNumber:ct,errorCardName:ft,errorExpiryDate:vt,errorSecurityCode:_t,handleMaskCVV:f,CVV:ot,onChange:k,checkedBillItems:se,creditCardAutopayOffers:ye,debitCardAutopayOffers:ge,language:Ae,managePreauth:he,IsAutopayCreditEnabled:Le,Checked:(null!==(c=null===(s=null==Gt?void 0:(u=Gt.trim()).toLowerCase)||void 0===s?void 0:s.call(u))&&void 0!==c?c:"")===j.formatMessage({id:"CREDIT_CARD_LABEL"}).trim().toLowerCase()}),U&&l.createElement(Vt,{Checked:(null!==(p=null===(d=null==Gt?void 0:(m=Gt.trim()).toLowerCase)||void 0===d?void 0:d.call(m))&&void 0!==p?p:"")===j.formatMessage({id:"CANCEL_PAYMENT_LABEL"}).trim().toLowerCase(),paymentItems:T,checkedBillItems:se,managePreauth:he,setCancelPreauthSectionClicked:Te,cancelPreauthSectionClicked:Ie,removedSubscriberOffers:ve,province:Ne,IsAutopayCreditEnabled:Le,setOmnitureOnPaymentSelect:Ce,language:Ae,onChange:k}),Ie?l.createElement("div",{className:"brui-inline-flex brui-flex-wrap brui-items-center payment-pb-45"},l.createElement("div",{className:"payment-pr-30 payment-pt-30"},l.createElement(Ye.Button,{variant:"primary",size:"regular",onClick:H},j.formatMessage({id:"CTA_CONFIRM"}))),l.createElement("div",{className:"payment-pt-30"},l.createElement(Ye.Button,{variant:"textBlue",size:"regular",className:"!brui-text-14 brui-leading-18",onClick:F},j.formatMessage({id:"CTA_CANCEL"})))):l.createElement("div",{className:"payment-pt-15 payment-pb-45 sm:payment-pb-60"},l.createElement(Ye.Button,{variant:"primary",onClick:g,disabled:!1},j.formatMessage({id:"CTA_NEXT"})))))),l.createElement(Ut,{paymentItem:T,className:ne>A.CurrentSection.PaymentMethod?"":"payment-hidden",isPreauth:h,inputValue:ae,inputBankValue:oe,isNewbank:null!=Ht&&Ht,onEditClick:E,showHeading:!0,isBankPaymentSelected:hn,isSingleClickEnable:J||$,bankList:me,debitCardAutopayOffers:ge,creditCardAutopayOffers:ye,checkedBillItems:se,bankitems:T,isConfirmation:!1,isShow:ne>A.CurrentSection.PaymentMethod}))},qt=function(e){return{creditcardDetails:e.creditCardDetails,validationErrors:e.validationErrors,cardTokenizationSuccess:e.cardTokenizationSuccess,redirectUrl:e.redirectUrl,interacBankInfo:e.interacBankInfo,interactBankFailureInfo:e.interactBankFailureInfo,cancelPreauthStatus:e.cancelPreauthStatus,cancelPreauthPayments:e.cancelPreauthPayments}},Kt=function(e){return{onCreditCardNumberChange:function(t){var n,r,a,i,o=new A.CCDetails;o.CreditCardNumber=t,t||(r=t,(i=new Bt).errors=new Array,a=new Array,r||a.push(Pt.Empty),n=a&&a.length>0?(i.errors.push({valErrors:a,field:Lt.FieldType.CardNumber}),i):[],e((0,g.setValidationErrors)(n))),(!n||n.length<=0||!n.errors||n.errors.length<=0)&&e((0,g.onCreditCardNumberChange)(o))},onCardHolderNameChange:function(t){var n,r,a,i,o,l=new A.CCDetails;l.CardholderName=t,a=t,(o=new Bt).errors=new Array,i=new Array,a&&/^(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?\s)+(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{1}\.?\s)*[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?$/.test(a.trim())&&a.trim().length<=70||i.push(Pt.Empty),(null===(n=null==(r=i&&i.length>0?(o.errors.push({valErrors:i,field:Lt.FieldType.CardHolderName}),o):[])?void 0:r.errors)||void 0===n?void 0:n.length)>0&&e((0,g.setValidationErrors)(r)),(!r||r.length<=0||!r.errors||r.errors.length<=0)&&e((0,g.onCardHolderNameChange)(l))},onCreditCardExpiryDateChange:function(t,n){var r,a=new A.CCDetails;a.ExpireMonth=t,a.ExpireYear=n,r=function(e,t){var n,r,a,i,o=new Bt;return o.errors=new Array,n=new Array,r=""==t?t:parseInt(t),(a=""==e?e:parseInt(e))>=12&&(a=0,r+=1),i=new Date(r<=49?2e3+r:1900+r,a,0),""==r.toString()||""==a.toString()?n.push(Pt.Empty):i<new Date&&n.push(Pt.CreditCardExpireDate),n&&n.length>0?(o.errors.push({valErrors:n,field:Lt.FieldType.ExpirationDate}),o):[]}(t,n),(r.length>0||r.errors)&&e((0,g.setValidationErrors)(r)),(!r||r.length<=0||!r.errors||r.errors.length<=0)&&e((0,g.onCreditCardExpiryDateChange)(a))},onSecurityCodeChange:function(t){var n,r,a,i,o=new A.CCDetails;o.SecurityCode=t,t||(r=t,(i=new Bt).errors=new Array,a=new Array,r||a.push(Pt.Empty),n=a&&a.length>0?(i.errors.push({valErrors:a,field:Lt.FieldType.SecurityCode}),i):[],e((0,g.setValidationErrors)(n))),(!n||n.length<=0||!n.errors||n.errors.length<=0)&&e((0,g.onSecurityCodeChange)(o))},validatBankDetails:function(t){var n=function(e){var t,n=new Bt;return n.errors=new Array,(t=e.validationForm).bankNameError&&(t.bankNameError.isEmpty&&n.errors.push({valErrors:[Pt.Empty],field:Lt.FieldType.BankName}),t.bankNameError.isInvalid&&n.errors.push({valErrors:[Pt.Invalid],field:Lt.FieldType.BankName})),t.bankAccountHolderError&&(t.bankAccountHolderError.isEmpty&&n.errors.push({valErrors:[Pt.Empty],field:Lt.FieldType.BankAccountHolderName}),t.bankAccountHolderError.isInvalid&&n.errors.push({valErrors:[Pt.Invalid],field:Lt.FieldType.BankAccountHolderName})),t.transitNumberError&&(t.transitNumberError.isEmpty&&n.errors.push({valErrors:[Pt.Empty],field:Lt.FieldType.BankTransitCode}),t.transitNumberError.isInvalid&&n.errors.push({valErrors:[Pt.Invalid],field:Lt.FieldType.BankTransitCode})),t.bankAccountNumberError&&(t.bankAccountNumberError.isEmpty&&n.errors.push({valErrors:[Pt.Empty],field:Lt.FieldType.BankAccountNumber}),t.bankAccountNumberError.isInvalid&&n.errors.push({valErrors:[Pt.Invalid],field:Lt.FieldType.BankAccountNumber})),e.isValid?[]:n}(t);t.isValid?e((0,g.resetValidationErrors)({errors:[]})):e((0,g.setValidationErrors)(n))},resetValidationErrors:function(t){e((0,g.resetValidationErrors)(t))},validateFormOrder:function(t,n,r,a,i,o){e((0,g.validateMultiOrderPaymentAction)({ban:t,type:n,details:r,accountInputValue:a,isBankPaymentSelected:i,sub:o}))},tokenizeAndPropagateFormValues:function(t,n,r,a,i,o){e((0,g.tokenizeAndPropagateFormValues)({form:t,ban:n,type:r,details:a,isBankPaymentSelected:i,sub:o}))},setOmnitureOnPaymentSelect:function(t){e((0,g.OmnitureOnPaymentSelect)({data:t}))},cancelPreauthPaymentsAction:function(t){e((0,g.cancelPreauthAction)({bans:t}))},setOmnitureOnCancelationCompleted:function(t){e((0,g.OmnitureOnCancelationCompleted)(t))},setOmnitureOnCancelationPartiallyCompleted:function(t){e((0,g.OmnitureOnCancelationPartiallyCompleted)(t))},setOmnitureOnCancelationFailed:function(t){e((0,g.OmnitureOnCancelationFailed)(t))},setOmnitureOnCancelationPartiallyFailed:function(t){e((0,g.OmnitureOnCancelationPartiallyFailed)(t))},setCancellationPaymentFailStatus:function(t){return e((0,g.apiConfirmationStatus)({type:t}))},setOmnitureOnInteracFailure:function(t){e((0,g.OmnitureOnInteracFailure)(t))}}},Wt=(0,s.connect)(qt,Kt)((0,He.injectIntl)(zt)),Xt=function(e){var t=e.collapseHeightDynamic,n=e.expandHeightDynamic,r=e.intl,a=e.onSubmitClick,i=e.onCancelClick,o=(e.province,e.language),u=e.userProfileProv,s=(0,Ye.useWindowResize)(100).width,c=function(e){return"height"in e?e.height:""},m=c(t?(0,Ye.useResponsiveHeight)(s,t):{height:"90px"}),d=c(n?(0,Ye.useResponsiveHeight)(s,n):{height:"460px"}),p=r.formatMessage({id:"TERMS_AND_CONDITION_DISCLAIMER"}),f=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1"}),b=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2"}),E=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_ITEM1"}),_=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_ITEM2"}),y=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM3"}),g=r.formatMessage({id:"TERMS_AND_CON_DESC_2"}),v=r.formatMessage({id:"TERMS_AND_CON_DESC_3"}),N=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_QC"}),A=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_QC"}),C=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_ITEM1_QC"}),h=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_ITEM2_QC"}),T=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM3_QC"}),I=r.formatMessage({id:"TERMS_AND_CON_DESC_2_QC"}),R=r.formatMessage({id:"TERMS_AND_CON_DESC_3_QC"});return l.createElement("div",null,l.createElement("div",null,l.createElement(Ye.Accordion,{mode:"single"},l.createElement(Ye.AccordionItem,{key:1,index:1},l.createElement(Ye.AccordionContent,{"aria-labelledby":"terms-and-condition-trigger",id:"terms-and-condition-content",collapseHeight:m,expandHeight:d,className:"brui-text-14 brui-text-gray brui-leading-18 brui-pr-20 payment-overflow-y-scroll payment-scrollbar"},"QC"==u&&"en"==o?l.createElement("div",null,l.createElement("p",null,l.createElement("p",{className:"payment-mb-15"},r.formatMessage({id:"TERMS_AND_CON_DESC_1_QC"})),l.createElement("p",{dangerouslySetInnerHTML:{__html:N}}),l.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM1_QC"}))),l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM2_QC"}))),l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:T}}))),l.createElement("p",{dangerouslySetInnerHTML:{__html:A}}),l.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10"},l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:C}})),l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:h}}))),l.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:I}}),l.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:R}}),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_DESC_4_QC"})),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_ZIP_CODE_QC"})),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION_QC"})),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION_2_QC"})),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_TEL_QC"}))),l.createElement("p",{className:"payment-mt-10"},l.createElement("p",{className:"payment-mb-15"},r.formatMessage({id:"TERMS_AND_CON_DESC_1"})),l.createElement("p",{dangerouslySetInnerHTML:{__html:f}}),l.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM1"}))),l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM2"}))),l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:y}}))),l.createElement("p",{dangerouslySetInnerHTML:{__html:b}}),l.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:E}})),l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:_}}))),l.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:g}}),l.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:v}}),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_DESC_4"})),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_ZIP_CODE"})),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION"})),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION_2"})),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_TEL"})))):l.createElement("div",null,l.createElement("p",null,l.createElement("p",{className:"payment-mb-15"},r.formatMessage({id:"TERMS_AND_CON_DESC_1"})),l.createElement("p",{dangerouslySetInnerHTML:{__html:f}}),l.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM1"}))),l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM2"}))),l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:y}}))),l.createElement("p",{dangerouslySetInnerHTML:{__html:b}}),l.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:E}})),l.createElement("li",null,l.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:_}}))),l.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:g}}),l.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:v}}),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_DESC_4"})),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_ZIP_CODE"})),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION"})),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION_2"})),l.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_TEL"}))))),l.createElement("div",{className:"payment-pt-15"},l.createElement(Ye.AccordionTrigger,{id:"terms-and-condition-trigger","aria-controls":"terms-and-condition-content",className:"brui-text-14 brui-text-blue brui-underline brui-leading-18 hover:brui-text-blue-1 hover:brui-no-underline"},l.createElement(Ye.AccordionToggleTitle,{titleExpand:r.formatMessage({id:"CTA_EXPAND_TERMS"}),titleCollapse:r.formatMessage({id:"CTA_COLLAPSE_TERMS"})})))))),l.createElement("div",{className:"payment-bg-gray-3 payment-mx-[-15px] sm:payment-mx-[-30px] md:payment-mx-[-15px] payment-mt-30"},l.createElement(Ye.Divider,{width:1,direction:"horizontal"}),l.createElement("div",{className:"payment-px-15 sm:payment-px-30 md:payment-px-15 payment-pt-30 payment-pb-45"},l.createElement("div",{className:"brui-text-gray brui-text-14 brui-leading-18 payment-max-w-[500px]"},l.createElement("div",{dangerouslySetInnerHTML:{__html:p}})),l.createElement("div",{className:"brui-inline-flex brui-flex-wrap brui-items-center"},l.createElement("div",{className:"payment-pr-30 payment-pt-30"},l.createElement(Ye.Button,{variant:"primary",size:"regular",onClick:a},r.formatMessage({id:"CTA_CONFIRM"}))),l.createElement("div",{className:"payment-pt-30"},l.createElement(Ye.Button,{variant:"textBlue",size:"regular",className:"!brui-text-14 brui-leading-18",onClick:i},r.formatMessage({id:"CTA_CANCEL"})))))))},Qt=(0,He.injectIntl)(Xt),Zt=function(e){var t=e.intl,n=e.variant,r=void 0===n?"default":n;return l.createElement("div",{className:"payment-bg-black payment-bg-opacity-60 payment-fixed payment-w-full payment-h-full  payment-z-20 payment-left-0 payment-top-0 payment-inline-flex payment-items-center payment-justify-center"},l.createElement("div",{id:"brf-page-loader",role:"alert","aria-busy":"true","aria-live":"assertive",className:"payment-inline-flex payment-items-center payment-py-15 payment-px-30 payment-shadow-md payment-bg-white"},l.createElement("svg",{className:"payment-animate-spin payment-size-[36px] payment-mr-10",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",viewBox:"3 3 42 42"},l.createElement("defs",null,l.createElement("linearGradient",{id:"loadingIndicatorGradient1",x1:"0",x2:"0",y1:"10%",y2:"90%"},l.createElement("stop",{offset:"0","stop-color":"#04569b"}),l.createElement("stop",{offset:"1","stop-color":"#97b6d2"})),l.createElement("linearGradient",{id:"loadingIndicatorGradient2",x1:"0",x2:"0",y1:"90%",y2:"10%"},l.createElement("stop",{offset:"0","stop-color":"#97b6d2"}),l.createElement("stop",{offset:"1","stop-color":"#fff"}))),l.createElement("path",{fill:"url(#loadingIndicatorGradient1)",d:"M24,3C12,3,3,12,3,24s9,21,21,21l-0.1-2.5c-5.3,0-10.1-2.2-13.5-6C7.4,33.1,5.5,28.3,5.5,24\r\n        c0-4.4,1.9-9.5,5.3-12.9c3.5-3.6,8.6-5.6,13.2-5.6L24,3z"}),l.createElement("path",{fill:"url(#loadingIndicatorGradient2)",d:"M24,3l0,2.4c5.5,0,10.8,2.8,14.3,6.8c2.8,3.4,4.2,7.6,4.2,11.7c0,4.7-2,9.7-5.7,13.3c-3.3,3.3-8.1,5.3-12.9,5.3\r\n        l0,2.5c12,0,21-10,21-21S36,3,24,3z"})),"default"===r&&l.createElement(l.Fragment,null,t.formatMessage({id:"LOADER"})),"submitOrder"===r&&l.createElement("div",{className:"payment-text-12 payment-leading-14"},l.createElement("p",{className:"payment-font-bold"},t.formatMessage({id:"LOADER_SUBMIT"})),l.createElement("p",null,t.formatMessage({id:"LOADER_SUBMIT_DESC"})))))},$t=(0,He.injectIntl)(Zt),Jt=function(e){var t,n,r,a=e.isActive,i=e.intl,u=(e.onCurrentSteps,e.setCurrentSection),s=e.currentSection,c=e.checkedBillItems,m=e.submitFormOrder,d=e.paymentItem,p=e.province,f=e.language,b=e.accountInputValues,E=e.setOmnitureOnReview,_=e.isBankSelected,y=e.validateMultiOrderFormStatus,g=e.tokenizeAndPropagateFormValuesStatus,N=e.setApiSatusIsFailed,C=e.userProfileProv,h=e.setOmnitureOnValidationFailure,T=e.inputValue,I=e.creditCardAutopayOffers,R=e.debitCardAutopayOffers,x=e.bankitems,S=e.sorryCredit,O=e.sorryDebit,M=(0,o.__read)(l.useState(v.IDLE),2),L=M[0],D=M[1],P=(0,o.__read)(l.useState(v.IDLE),2),B=P[0],k=P[1],w=function(){var e=[];return R&&(null==R||R.map(function(t){c&&c.map(function(n){t.Ban==n.BillName&&e.push(t)})})),e},U=function(){return x&&x.length>1?w().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):R&&R.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)},F=function(){return x&&x.length>1?w().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):I&&I.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)};return S=I&&I.length>0&&0==F()&&U()>0,O=R&&R.length>0&&0==U()&&F()>0,t=function(){d.length>1&&c&&c.length>0&&m(c[0].Ban,c[0].AccountType===A.PaymentItemAccountType.OneBill,_,S,O,b,c[0].subscriberId),d&&1===d.length&&m(c[0].Ban,c[0].AccountType===A.PaymentItemAccountType.OneBill,_,S,O,b,c[0].subscriberId),u(A.CurrentSection.Confirmation)},n=function(){u(A.CurrentSection.PaymentMethod)},r=l.useRef(null),l.useEffect(function(){var e=setTimeout(function(){if(r.current&&a){var e=r.current.querySelector("h2");e&&(e.scrollIntoView({behavior:"smooth"}),e.focus())}},500);return function(){return clearTimeout(e)}},[a]),l.useEffect(function(){s!=A.CurrentSection.TermsAndCondition||L!==v.COMPLETED&&B!==v.COMPLETED||E()},[s,L,B]),l.useEffect(function(){_||(g!==v.FAILED?k(v.PENDING):k(g))},[g]),l.useEffect(function(){_?(D(y),y==v.FAILED&&(N(!0),h(jt(!0,c)))):k(y)},[y]),l.useEffect(function(){B==v.FAILED&&(N(!0),h(jt(!1,c,T.cardType)))},[B]),l.createElement(l.Fragment,null,(L===v.PENDING||B===v.PENDING)&&l.createElement($t,null),l.createElement(l.Fragment,null,l.createElement("div",{className:s==A.CurrentSection.TermsAndCondition?"payment-border-t payment-border-gray-4 payment-mt-45":"",id:"TermsAndConditionsSection"},l.createElement("div",{ref:r,className:s==A.CurrentSection.TermsAndCondition?"sm:payment-mb-[90px] payment-border-gray-4":"sm:payment-mb-[90px]  payment-border-b payment-border-gray-4"},l.createElement("div",{id:"termsAndCondDivID",className:a?"focus-visible:payment-outline-none":""},l.createElement(Ye.HeadingStep,{disableSrOnlyText:!0,tabIndex:-1,className:"focus-visible:payment-outline-none",status:a?"active":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:"QC"===p&&"en"===f?i.formatMessage({id:"TERMS_AND_CONDITION_HEADING_QC"}):i.formatMessage({id:"TERMS_AND_CONDITION_HEADING"})})),l.createElement("div",{className:["payment-pt-15 sm:payment-pt-5",a?"":"payment-hidden"].join(" ").trim()},l.createElement(Qt,{onSubmitClick:t,onCancelClick:n,collapseHeightDynamic:{mobile:{height:"234px"},tablet:{height:"90px"},desktop:{height:"90px"}},expandHeightDynamic:{mobile:{height:"415px"},tablet:{height:"460px"},desktop:{height:"460px"}},province:p,language:f,userProfileProv:C}))))))},en=function(e){return{submitMultiOrderPayment:e.submitMultiOrderPayment,validateMultiOrderFormStatus:e.validateMultiOrderFormStatus,tokenizeAndPropagateFormValuesStatus:e.tokenizeAndPropagateFormValuesStatus}},tn=function(e){return{submitFormOrder:function(t,n,r,a,i,o,l){e((0,g.submitMultiOrderPaymentAction)({ban:t,type:n,isbankSelected:r,sorryCredit:a,sorryDebit:i,details:o,sub:l}))},setOmnitureOnReview:function(){e((0,g.OmnitureOnReview)({}))},setOmnitureOnValidationFailure:function(t){return e((0,g.OmnitureOnApiFailure)({data:t}))}}},nn=(0,s.connect)(en,tn)((0,He.injectIntl)(Jt)),rn=(0,l.forwardRef)(function(e,t){var n=e.className,r=e.isDisabled,a=e.isChecked,i=e.label,l=e.id,s=e.billType,c=e.billAccountNumber,m=e.idIndex,d=e.text,p=e.priceSettings,f=e.item,b=e.isCheckedItems,E=e.setIsCheckedItems,_=(e.onChange,e.isShowLabel),y=e.paymentItems,g=e.intl,v=(0,o.__read)(u().useState([]),2),N=v[0],A=v[1],C=f.Ban;return u().useEffect(function(){var e,t=document.querySelectorAll("input[type='checkbox']"),n=[];t.forEach(function(e){var t,r,a;e.checked&&(t=e.getAttribute("data-bandetail"),a=(r=t&&JSON.parse(t))&&r.ban&&y.filter(function(e){return e.Ban===r.ban}),a&&a.length>0&&n.push(a[0]))}),n&&n.length>0&&n.map(function(e){return null!==e})&&(e=n.filter(function(e){return null!==e}),A(function(t){return t.concat((0,o.__spreadArray)([],(0,o.__read)(e),!1))}))},[]),u().useEffect(function(){if(null!==N&&N.length>0){var e=N.reduce(function(e,t){return e.find(function(e){return t.Ban===e.Ban})||e.push((0,o.__assign)({},t)),e},[]);E(e)}},[N]),u().createElement(Ye.CheckboxCard,{ref:t,id:l,"aria-labelledby":i,disabled:r,defaultChecked:a,className:["check-manage group-has-[:disabled]/inputcheckbox:payment-bg-gray-3 payment-chekcbox-disabled payment-group/checkboxcard payment-pt-30 payment-pl-30 brui-pr-30 payment-pb-40 sm:payment-pb-30 sm:payment-p30 payment-basis-unset sm:payment-basis-p48 md:payment-basis-p35 lg:payment-basis-1/3 brui-w-full payment-mr-15 brui-mb-15",n].join(" ").trim(),defaultPadding:!1,checkboxPlacement:"topLeft","data-banDetail":JSON.stringify({id:l,billAccountNumber:c,ban:C,billType:s,price:null==p?void 0:p.price}),onChange:function(e){return function(e,t){e.target.checked?E((0,o.__spreadArray)((0,o.__spreadArray)([],(0,o.__read)(b),!1),[t],!1)):E(function(e){return e.filter(function(e){return e.BillName!==t.BillName})})}(e,f)}},u().createElement("div",{className:"sm:brui-flex payment-pl-[25px] sm:payment-pl-[35px] payment-pr-[15px] sm:payment-pr-[30px] payment-flex-col payment-relative payment-top-[5px] sm:payment-top-0 sm:payment-min-h-[48px]"},u().createElement("div",{className:"brui-flex brui-w-max brui-items-center",id:"checkboxBill".concat(m,"-label-").concat(m)},u().createElement(Ye.Text,{elementType:"span",className:"brui-font-bold"},s," ",u().createElement(Ye.Text,{elementType:"span",className:"brui-text-14 brui-text-gray !payment-font-normal"},c))),_&&u().createElement("div",{className:"brui-flex brui-w-fit brui-items-center payment-mr-[15px]",id:"checkboxBillBalance-".concat(m,"-label-").concat(m)},u().createElement(Ye.Text,{elementType:"span",className:"brui-text-14 brui-text-gray brui-break-words brui-flex brui-items-center brui-leading-18"},d),p&&u().createElement(Ye.Price,{language:p.language?p.language:"en",negativeIndicator:p.negativeIndicator?p.negativeIndicator:"CR",price:p.price?p.price:0,variant:"defaultPrice",className:"!payment-text-14 brui-leading-14 payment-m-5"})),u().createElement("div",{className:"payment-flex payment-w-fit payment-items-top",id:"checkboxBill".concat(m,"-label-").concat(m)},!_&&u().createElement(u().Fragment,null,u().createElement(Ye.Icon,{className:"payment-text-yellow payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),u().createElement(Ye.Text,{elementType:"span",className:"payment-text-14 payment-text-gray payment-break-words payment-flex payment-items-center payment-leading-18"},g.formatMessage({id:"NOT_ON_PREAUTH"}))))))}),an=function(e){var t=e.intl,n=e.isActive,a=e.onIconLinkClick,i=(e.banDetails,e.isCheckedItems),o=e.isShow,l={MyBill:t.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:t.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:t.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:t.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:t.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:t.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:t.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:t.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})},s=i.map(function(e){return{label:u().createElement(He.FormattedMessage,{id:"SELECT_BILLS_ACCOUNT_TITLE",values:{accounttype:r(e.AccountType,e.IsNM1Account,l)}}),value:e.NickName}});return u().createElement("div",{className:n?"payment-mb-45 payment-block":"sm:payment-mb-15 payment-border-b payment-border-gray-4 payment-hidden"},u().createElement("div",{className:"payment-flex payment-items-center payment-justify-between"},u().createElement(Ye.HeadingStep,{disableSrOnlyText:!0,status:n?"complete":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:s.length>1?t.formatMessage({id:"SELECT_BILLS_HEADING"}):t.formatMessage({id:"SELECT_BILLS_HEADING_SINGULAR"}),id:o?"payment-manage-heading":void 0,"aria-hidden":o?"true":void 0}),u().createElement("div",{className:"payment-pt-45"},u().createElement(Ye.IconLink,{icon:u().createElement(Ye.Icon,{iconClass:"bi_brui",iconName:"bi_edit_pencil",className:"brui-text-16"}),text:t.formatMessage({id:"CTA_EDIT"}),variant:"textBlue",size:"regular",href:"javascript:void(0);",position:"right",className:["payment-flex payment-items-center !payment-text-14 !payment-leading-18",n?"":"payment-hidden"].join(" ").trim(),"aria-label":s.length>1?t.formatMessage({id:"SELECT_BILLS_HEADING"}):t.formatMessage({id:"SELECT_BILLS_HEADING_SINGULAR"}),onClick:function(){return a&&a(1)}}))),u().createElement("div",{className:["brui-pb-45",n?"":"brui-hidden"].join(" ").trim()},u().createElement("div",{className:"brui-mt-15"},s.map(function(e,t){return u().createElement(kt.SingleRowInformation,{className:t>0?"brui-mt-5":"",label:e.label,value:e.value})}))))},on=(0,He.injectIntl)(an),ln=(0,l.forwardRef)(function(e,t){var n=e.className,r=e.isDisabled,a=e.isChecked,i=e.label,l=e.id,s=e.billType,c=e.billAccountNumber,m=e.idIndex,d=e.text,p=e.priceSettings,f=e.currentItem,b=e.isCheckedBalanceItems,E=e.setIsCheckedBalanceItems;return u().createElement(Ye.CheckboxCard,{ref:t,id:l,"aria-labelledby":i,disabled:r,defaultChecked:a,className:["group-has-[:disabled]/inputcheckbox:payment-bg-gray-3 payment-chekcbox-disabled payment-group/checkboxcard payment-pt-30 payment-pl-30 brui-pr-30 payment-pb-30 sm:payment-p30 payment-basis-unset sm:payment-basis-p48 md:payment-basis-p35 brui-w-full payment-mr-15 brui-mb-15",n].join(" ").trim(),defaultPadding:!1,checkboxPlacement:"topLeft","data-banDetail":JSON.stringify({id:l,billAccountNumber:c,billType:s,price:null==p?void 0:p.price}),onChange:function(e){return t=f,void(e.target.checked?E((0,o.__spreadArray)((0,o.__spreadArray)([],(0,o.__read)(b),!1),[t],!1)):E(function(e){return e.filter(function(e){return e.BillName!==t.BillName})}));var t}},u().createElement("div",{className:"sm:payment-flex sm:payment-pl-[33px] payment-pl-[24px]"},u().createElement("div",{className:"payment-mt-2 sm:payment-mt-1 payment-flex-1 sm:payment-text-left",id:"checkboxBalance-".concat(m,"-label-").concat(m)},u().createElement(Ye.Text,{elementType:"span",className:"sm:payment-pl-5 payment-text-16 sm:payment-text-18 payment-leading-20 sm:payment-leading-22 brui payment-font-bold payment-flex payment-flex-row payment-items-center payment-gap-5"},d,p&&u().createElement(Ye.Price,{language:p.language?p.language:"en",negativeIndicator:p.negativeIndicator?p.negativeIndicator:"CR",price:p.price?p.price:0,variant:"defaultPrice",className:"!payment-text-[16px] !sm:payment-text-[18px] payment-leading-20 sm:payment-leading-22"}))),u().createElement("div",{className:"payment-mt-2 sm:payment-mt-1 payment-flex-2 sm:payment-text-right payment-leading-18",id:"checkboxBalance-".concat(m,"-label-").concat(m,"-info")},u().createElement(Ye.Text,{elementType:"span",className:"payment-text-gray payment-text-14 sm:payment-text-14"}," ","on",u().createElement(Ye.Text,{elementType:"span",className:"payment-font-bold payment-text-black"}," ",s," "),c))))}),un=e(21),cn=function(e){var t,n=e.intl,r=e.isActive,a=e.onIconLinkClick,i=e.isCheckedBalanceItems,o=e.checkedBillItems,l=e.paymentItem,s=e.isBankPaymentSelected,c=e.currentSection,m=e.language,d=void 0===m?"en":m,p=s?n.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_IN"},{balance:new Intl.NumberFormat(d,{style:"currency",currency:"USD",currencyDisplay:"narrowSymbol"}).format(0)}):c===A.CurrentSection.Confirmation?"":n.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_IN_PACC"}),f=n.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR"}),b=n.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL"});return o.length>0&&(sn=o.filter(function(e){return!i.some(function(t){return t.BanID===e.BanID})})),t=sn.filter(function(e){return e.Due>0}),u().createElement("div",null,u().createElement("div",{className:r?"payment-block payment-border-gray-4 payment-border-b payment-border-t payment-mt-45":"sm:payment-mb-15 payment-border-b payment-border-gray-4 payment-hidden"},u().createElement("div",null,u().createElement("div",{className:"payment-flex payment-items-center payment-justify-between payment-mt-0"},u().createElement(Ye.HeadingStep,{disableSrOnlyText:!0,status:"complete",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:n.formatMessage({id:"PAY_CURRENT_BALANCE_HEADING"}),id:"payment-manage-heading"}),u().createElement("div",{className:"payment-pt-45"},u().createElement(Ye.IconLink,{icon:u().createElement(Ye.Icon,{iconClass:"bi_brui",iconName:"bi_edit_pencil",className:"brui-text-16"}),text:n.formatMessage({id:"CTA_EDIT"}),variant:"textBlue",size:"regular",href:"javascript:void(0);",position:"right",className:"payment-flex payment-items-center !payment-text-14 !payment-leading-18",onClick:function(){return a&&a(1)},"aria-label":n.formatMessage({id:"PAY_CURRENT_BALANCE_HEADING"})}))),u().createElement("div",{className:"payment-pb-45"},u().createElement("div",{className:"payment-text-gray payment-text-14 payment-mt-5"},u().createElement("p",{className:"payment-leading-18",dangerouslySetInnerHTML:{__html:0===i.length?o.length-i.length>1?b:f:i.every(function(e){return o.includes(e)})||i.some(function(e){return o.includes(e)})?p:void 0}})),i.length>0&&u().createElement("div",{className:"payment-pt-15"},l.length>1?u().createElement(u().Fragment,null,i.map(function(e){return u().createElement(un.MultiBanInformation,{accountinfo:e.NickName,role:"list",childrole:"listitem",className:"payment-mb-15 last:payment-mb-0"},u().createElement(kt.SingleRowInformation,{label:n.formatMessage({id:"PAYMENT_AMOUNT"}),value:u().createElement(Ye.Price,{language:d,price:e.Due,variant:"ordinaryPrice",className:"!brui-text-14 brui-leading-18"}),needSRText:!0,srText:"".concat(e.Due," dollars"),className:"payment-text-black",isMultiBan:!0}))}),sn.length>0&&o.length!==sn.length&&u().createElement(u().Fragment,null,u().createElement("div",{className:"payment-text-gray payment-text-14 payment-mt-5"},u().createElement("p",{className:"payment-leading-18"},sn.some(function(e){return e.Due>0})&&u().createElement(u().Fragment,null,t.length>1&&n.formatMessage({id:"PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_PLURAL"}),1===t.length&&n.formatMessage({id:"PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_SINGULAR"})))))):u().createElement(u().Fragment,null,i.map(function(e){return u().createElement(kt.SingleRowInformation,{label:n.formatMessage({id:"PAYMENT_AMOUNT"}),value:u().createElement(Ye.Price,{language:d,price:e.Due,variant:"ordinaryPrice",className:"!brui-text-14 brui-leading-18"}),needSRText:!0,srText:"".concat(e.Due," dollars"),className:"payment-text-black"})})))))))},mn=(0,He.injectIntl)(cn),dn=function(e){var t,a,s,c,m,d=e.intl,p=e.paymentItem,f=e.isShow,b=(e.onCurrentSteps,e.setCurrentSection),E=e.currentSection,_=e.setCheckedBillItems,y=e.paymentItems,g=e.createMultiPaymentData,v=e.accountInputValues,N=e.setAccountValues,C=e.transactionIds,h=e.createOmnitureOnLoad,T=e.managePreauth,I=e.isCheckedBan,R=(0,o.__read)((0,l.useState)(!1),2),x=R[0],S=R[1],O=(0,o.__read)((0,l.useState)(),2),M=O[0],L=O[1],D=(0,o.__read)((0,l.useState)([]),2),P=D[0],B=D[1],k=(0,o.__read)((0,l.useState)([]),2),w=k[0],U=k[1],F=(0,o.__read)((0,l.useState)(!1),2),H=F[0],Y=F[1],j=(0,o.__read)((0,l.useState)(),2),G=j[0],V=j[1],z=(0,o.__read)((0,l.useState)(!1),2),q=z[0],K=z[1],W=(0,l.useRef)([]),X=function(){var e,t,n;null!==P&&P.length>0&&g(P[0].Ban,P[0].AccountType===A.PaymentItemAccountType.OneBill,v,P[0].subscriberId),0==(n=!1,w&&w.length>0&&(t=(e=W.current.filter(function(e){return null==e?void 0:e.checked})).map(function(e){return null==e?void 0:e.getAttribute("data-banDetail")}).filter(function(e){return null!=e}),L(t),n=!(e.length<=0)),n)?S(!0):(S(!1),b(A.CurrentSection.PaymentMethod))},Q=T&&null!==T,Z=I&&Q?new Array(I):[];return u().useEffect(function(){var e,t=sessionStorage.getItem("itemsChecked"),n=t&&JSON.parse(t);null!==n&&n.length>0?(Y(!0),V(n),U(y),e=s(y),g(y[0].Ban,y[0].AccountType===A.PaymentItemAccountType.OneBill,e,y[0].subscriberId)):(V(Z),U(y))},[]),u().useEffect(function(){H&&(null==w?void 0:w.length)>0&&(X(),sessionStorage.removeItem("itemsChecked"),Y(!1))},[H]),t=function(e){return(e.IsOnPreauthorizedPayments||T&&"Creditcard"===T&&e.CreditCardDetails)&&e.CreditCardDetails?u().createElement(u().Fragment,null,u().createElement(He.FormattedMessage,{id:"SELECT_BILLS_CC_DESC",values:{CreditCardType:i(e.CreditCardDetails.CreditCardType),CCFourDigits:e.CreditCardDetails.CreditCardNumber.slice(-4),ExpiryDate:e.CreditCardDetails.ExpireMonth+"/"+e.CreditCardDetails.ExpireYear}})):(e.IsOnPreauthorizedPayments||T&&"Debit"===T&&e.BankAccountDetails)&&e.BankAccountDetails?u().createElement(u().Fragment,null,u().createElement(He.FormattedMessage,{id:"SELECT_BILLS_BANK_DESC",values:{BankName:e.BankAccountDetails.BankName,Code:e.BankAccountDetails.TransitCode,BankMaskedDigits:e.BankAccountDetails.AccountNumberMaskedDisplayView}})):u().createElement(u().Fragment,null,d.formatMessage({id:"ACCOUNT_BALANCE"}))},a=(null==p?void 0:p.filter(function(e){return e.IsOnPreauthorizedPayments}).length)==p.length,s=function(e){return e.map(function(e){return{accountNumber:e.Ban,subNumber:e.subscriberId,transactionID:n(e.Ban,C),payBalanceAmnt:0}})},u().useEffect(function(){if(p.length>1){_(P);var e=s(P);N(e),0===P.length&&(_([]),N([])),p.length===P.length?K(!0):K(!1)}},[P]),c={MyBill:d.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:d.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:d.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:d.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:d.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:d.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:d.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:d.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})},m=function(e){return!(!e.IsNM1Account&&e.AccountType===A.PaymentItemAccountType.OneBill&&!e.IsOnPreauthorizedPayments||!(0!=e.Due&&null!=e.Due||e.IsOnPreauthorizedPayments))},u().useEffect(function(){var e=sessionStorage.getItem("itemsChecked"),t=e&&JSON.parse(e);E==A.CurrentSection.SelectBills&&(null!==t&&t.length>0||h())},[E]),u().createElement("div",{className:["payment-border-b payment-border-gray-4",f?"":"payment-hidden"].join(" ").trim()},u().createElement("div",{className:["payment-flex payment-flex-col",E==A.CurrentSection.SelectBills?"":"payment-hidden"].join(" ").trim()},u().createElement(Ye.HeadingStep,{disableSrOnlyText:!0,status:"active",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:p.filter(function(e){return null==e?void 0:e.IsOnPreauthorizedPayments}).length>1?d.formatMessage({id:"SELECT_BILLS_HEADING"}):d.formatMessage({id:"SELECT_BILLS_HEADING_SINGULAR"}),id:E==A.CurrentSection.SelectBills?"payment-manage-heading":void 0,"aria-hidden":E==A.CurrentSection.SelectBills||void 0}),u().createElement("p",{className:"payment-text-gray payment-text-14 payment-mt-5"},Q?d.formatMessage({id:"SELECT_BILLS_HEADING_DESC_MANAGE"}):d.formatMessage({id:"SELECT_BILLS_HEADING_DESC"}))),x&&u().createElement(u().Fragment,null,u().createElement("div",{className:"payment-mt-30"}),u().createElement(yt,null,u().createElement(dt,null,u().createElement(bt,{label:d.formatMessage({id:"ALERT_ERROR_SELECT_BILL_INFO"}),labelDescription:d.formatMessage({id:"ALERT_ERROR_SELECT_BILL_DESC"}),variant:"errorList",id:"error-alert-2"})))),u().createElement("div",{className:["payment-pt-0 checkbox-main",E==A.CurrentSection.SelectBills?"":"payment-hidden"].join(" ").trim()},u().createElement("div",{className:"payment-mt-30"},u().createElement(Ye.Checkbox,{id:"chxbx1",name:"checkboxname",value:"select all",variant:"default",checked:q,onChange:function(e){var t=e.target.checked;t?(K(!0),w&&w.length>0&&(B(w),W.current.forEach(function(e){e&&(e.checked=t)}))):(K(!1),w.map(function(e,t){B(function(t){return t.filter(function(t){return t.BillName!==e.BillName})})}),w&&w.length>0&&W.current.forEach(function(e){e&&(e.checked=!1)}))}},u().createElement("label",null,d.formatMessage({id:"SELECT_ALL_BAN"})))),u().createElement("div",{className:"payment-mt-30 payment-flex payment-flex-col sm:payment-flex-row payment-flex-wrap chkbx-manage-main",role:"group","aria-labelledby":"chekcboxgroup-label"},w&&w.length>0&&w.sort(function(e,t){return e.NickName>t.NickName?1:-1}).map(function(e,n){var a;return u().createElement(rn,{className:["checkboxitem payment-group/checkboxcard  payment-basis-0 sm:payment-basis-[47%] md:payment-basis-[35%] lg:payment-basis-1/3 payment-w-full sm:payment-w-auto payment-mr-15 payment-mb-15",e.IsOnPreauthorizedPayments&&!Q?"payment-bg-gray-3":""].join(" ").trim(),id:"checkboxbill-".concat(n),idIndex:n,label:"checkboxBill".concat(n,"-label-").concat(n," checkboxBillBalance-").concat(n,"-label-").concat(n),isChecked:Q&&G&&G.length>0?Boolean(null==G?void 0:G.find(function(t){return t===e.Ban})):e.IsChecked,isDisabled:e.IsOnPreauthorizedPayments&&!Q,billType:r(e.AccountType,e.IsNM1Account,c),billAccountNumber:null!==(a=e.NickName)&&void 0!==a?a:e.BillName,text:t(e),priceSettings:e.IsOnPreauthorizedPayments?void 0:{price:e.Due},ref:function(e){W.current[n]=e},item:e,isCheckedItems:P,setIsCheckedItems:B,isShowLabel:m(e),paymentItems:w,intl:d})})),x&&u().createElement(Ye.Text,{elementType:"div",className:"payment-pb-15 payment-flex payment-items-center",id:"error-alert-3"},u().createElement(Ye.Icon,{className:"payment-text-15 payment-text-red payment-mr-10",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),u().createElement(Ye.Text,{elementType:"div",className:"payment-text-red payment-text-12"},d.formatMessage({id:"ALERT_ERROR_ONE_SELECT_BILL"}))),u().createElement("div",{className:"payment-pt-15 payment-pb-45 sm:payment-pb-60"},u().createElement(Ye.Button,{variant:"primary",onClick:X,disabled:!Q&&a},d.formatMessage({id:"CTA_NEXT"})))),u().createElement(on,{isActive:E>A.CurrentSection.SelectBills,onIconLinkClick:function(e){b(A.CurrentSection.SelectBills),h()},banDetails:M||[],isCheckedItems:P,isShow:f}))},pn=function(e){return{createPayment:e.createPayment}},fn=function(e){return{createMultiPaymentData:function(t,n,r,a){return e((0,g.createMultiPaymentAction)({ban:t,type:n,details:r,sub:a}))},createOmnitureOnLoad:function(){return e((0,g.OmnitureOnLoad)({payload:"Banselected"}))}}},bn=(0,s.connect)(pn,fn)((0,He.injectIntl)(dn)),En=function(e){var t,n=e.intl,a=e.paymentItem,i=e.checkedBillItems,s=e.setCheckedCurrentBalanceItems,c=e.setCurrentSection,m=e.currentSection,d=e.language,p=e.accountInputValues,f=e.setAccountValues,b=(e.transactionIds,e.isBankPaymentSelected),E=e.setNotOptedBalanceItems,_=(e.checkedCurrentBalanceItems,e.createOmnitureOnCurrentBalance,(0,o.__read)((0,l.useState)([]),2)),y=_[0],g=_[1],v={MyBill:n.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:n.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:n.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:n.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:n.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:n.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:n.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:n.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})};return u().useEffect(function(){var e,t;e=(0,o.__spreadArray)([],(0,o.__read)(p),!1),y.length>0?(s(y),t=e.map(function(e){var t=y.find(function(t){return t.Ban===e.accountNumber&&t.subscriberId===e.subNumber});return e.payBalanceAmnt=t?t.Due:0,e}),f(t),E(i.filter(function(e){return e.Due>0&&e.AccountType!=A.PaymentItemAccountType.OneBill&&!y.includes(e)}))):(s([]),f(e),E(i.filter(function(e){return e.Due>0&&e.AccountType!=A.PaymentItemAccountType.OneBill})))},[y]),t=n.formatMessage({id:"PAY_CURRENT_BALANCE_DESC"}),u().createElement(u().Fragment,null,u().createElement("div",{className:[m==A.CurrentSection.CurrentBalance?"payment-border-b payment-border-gray-4 payment-border-t payment-mt-45":"payment-border-b payment-border-gray-4",m>A.CurrentSection.CurrentBalance?"payment-hidden":""].join(" ").trim()},u().createElement("div",null,u().createElement(Ye.HeadingStep,{disableSrOnlyText:!0,status:m==A.CurrentSection.CurrentBalance?"active":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:(i.filter(function(e){return!(null==e?void 0:e.IsOnPreauthorizedPayments)}).length,n.formatMessage({id:"PAY_CURRENT_BALANCE_HEADING"})),id:m==A.CurrentSection.CurrentBalance?"payment-manage-heading":void 0,"aria-hidden":m==A.CurrentSection.CurrentBalance?"true":void 0}),m==A.CurrentSection.CurrentBalance&&u().createElement("div",{className:"payment-flex sm:payment-items-center"},u().createElement(Ye.Icon,{className:"brui-text-blue payment-mt-10",iconClass:"bi_brui",iconName:"bi_info_notif_small"}),u().createElement("p",{className:"payment-text-gray payment-text-14 payment-leading-18 payment-mt-10 payment-ml-10",dangerouslySetInnerHTML:{__html:t}}))),u().createElement("div",{className:[m==A.CurrentSection.CurrentBalance?"":"payment-hidden"].join(" ").trim()},u().createElement("div",{className:"payment-mt-30 payment-flex payment-flex-col sm:payment-flex-row payment-flex-wrap",role:"group","aria-labelledby":"chekcboxgroup-label"},i.map(function(e,t){var a;return e.Due>0&&e.AccountType!=A.PaymentItemAccountType.OneBill?u().createElement(ln,{className:"sm:payment-px-30 payment-mb-15",id:"checkboxBill-".concat(t),idIndex:t,label:"checkboxBalance-".concat(t,"-label-").concat(t," checkboxBalance-").concat(t,"-label-").concat(t,"-info"),isDisabled:e.IsOnPreauthorizedPayments,billType:r(e.AccountType,e.IsNM1Account,v),billAccountNumber:null!==(a=e.NickName)&&void 0!==a?a:e.BillName,text:n.formatMessage({id:"PAY_MY_BALANCE"}),priceSettings:e.IsOnPreauthorizedPayments?void 0:{price:e.Due},currentItem:e,isCheckedBalanceItems:y,setIsCheckedBalanceItems:g}):null})),u().createElement("div",{className:"payment-text-gray payment-text-12 payment-mt-20"},u().createElement("p",null,n.formatMessage({id:"PAY_CURRENT_BALANCE_NOTE_1"})),u().createElement("p",null,n.formatMessage({id:"PAY_CURRENT_BALANCE_NOTE_2"}))),u().createElement("div",{className:"payment-pt-15 payment-pb-45 sm:payment-pb-60"},u().createElement(Ye.Button,{variant:"primary",onClick:function(){c(A.CurrentSection.TermsAndCondition)}},n.formatMessage({id:"CTA_NEXT"}))))),u().createElement(mn,{isActive:m>A.CurrentSection.CurrentBalance,onIconLinkClick:function(e){c(A.CurrentSection.CurrentBalance)},paymentItem:a,isCheckedBalanceItems:y,checkedBillItems:i,isBankPaymentSelected:b,currentSection:m,language:d}))},(0,He.injectIntl)(En),_n=function(e){function t(e){var t,n,r,a;return(null===(a=null===(r=null===(n=null===(t=null==e?void 0:e[0])||void 0===t?void 0:t.AutopayEligibleSubscribers)||void 0===n?void 0:n[0])||void 0===r?void 0:r.autopayOffers)||void 0===a?void 0:a.length)>0}function n(e,t){var n=e.toFixed(2),r="fr"===t?n.replace(".",","):n;return"".concat(r,"en"===t?" dollars per month":" dollars par mois")}var a,i,u,s,c,m,d,p,f,b,E=e.intl,_=e.paymentItem,y=e.checkedBillItems,g=e.showPaymentSummary,N=e.isNewbank,C=e.inputValue,h=e.inputBankValue,T=e.isShow,I=e.isBankPaymentSelected,R=e.submitMultiOrderPayment,x=e.submitMultiOrderFormStatus,S=e.accountInputValues,O=e.BankList,M=(e.showCurrentBalance,e.currentSection),L=e.language,D=(e.notOptedBalanceItems,e.setApiSatusIsFailed),P=e.creditCardAutopayOffers,B=e.debitCardAutopayOffers,k=e.bankitems,w=e.setOmnitureOnConfirmation,U=e.setApiConfirmationStatus,F=e.apiSatusIsFailed,H=e.isBanCreditPreauth,Y=e.setOmnitureOnConfirmationFailure,j={MyBill:E.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:E.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:E.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:E.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:E.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:E.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:E.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:E.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})},G={label:E.formatMessage({id:"PAYMENT_METHOD"}),debits:I?_&&_.length>1?(b=[],B&&(null==B||B.map(function(e){y&&y.map(function(t){e.Ban==t.BillName&&b.push(e)})})),b):B:null,credits:I?null:_&&_.length>1?function(){var e=[];return P&&(null==P||P.map(function(t){y&&y.map(function(n){t.Ban==n.BillName&&e.push(t)})})),e}():P},V=t(null==G?void 0:G.credits)||t(null==G?void 0:G.debits),z=function(){var e=[];return I?B&&(null==B||B.map(function(t){y&&y.map(function(n){t.Ban==n.BillName&&e.push(t)})})):P&&(null==P||P.map(function(t){y&&(null==y||y.map(function(n){t.Ban==n.BillName&&e.push(t)}))})),e},q=function(){return k&&k.length>1?z().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):I?B&&B.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):P&&P.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)},K={label:E.formatMessage({id:"PAYMENT_METHOD"}),credits:k&&k.length>1?z():B},W={label:E.formatMessage({id:"PAYMENT_METHOD"}),credits:k&&k.length>1?z():P},X=function(){var e;return(null===(e=null==K?void 0:K.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return("GAIN"===e.offerImpact||""===e.offerImpact||null===e.offerImpact)&&"RETAIN"!==e.offerImpact})})}))||!1},Q=function(){var e;return(null===(e=null==W?void 0:W.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return("GAIN"===e.offerImpact||""===e.offerImpact||null===e.offerImpact)&&"RETAIN"!==e.offerImpact})})}))||!1},Z=function(){var e;return(null===(e=null==K?void 0:K.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"REMOVE"===e.offerImpact})})}))||!1},$=function(){var e;return(null===(e=null==W?void 0:W.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"REMOVE"===e.offerImpact})})}))||!1},J=function(){var e;return(null===(e=null==K?void 0:K.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"REDUCE"===e.offerImpact})})}))||!1},ee=function(){var e;return(null===(e=null==W?void 0:W.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"INCREASE"===e.offerImpact})})}))||!1},te=function(){var e;return(null===(e=null==K?void 0:K.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"INCREASE"===e.offerImpact})})}))||!1},ne=function(){var e;return(null===(e=null==W?void 0:W.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){return"REDUCE"===e.offerImpact})})}))||!1},re={label:q()>1?E.formatMessage({id:"LABEL_LOADED_OFFERS_REDUCED_DEBIT_TITLE"}):E.formatMessage({id:"LABEL_LOADED_OFFER_REDUCED_DEBIT_TITLE"})},ae={label:q()>1?E.formatMessage({id:"LABEL_LOADED_OFFERS_DEBIT_TITLE_CONFIRMATION"}):E.formatMessage({id:"LABEL_LOADED_OFFER_DEBIT_TITLE_CONFIRMATION"})},ie={label:q()>1?E.formatMessage({id:"LABEL_LOADED_OFFERS_REMOVED_DEBIT_TITLE_CONFIRMATION"}):E.formatMessage({id:"LABEL_LOADED_OFFER_REMOVED_DEBIT_TITLE_CONFIRMATION"})},oe={label:q()>1?E.formatMessage({id:"LABEL_LOADED_OFFERS_INCREASED_CREDIT_TITLE_CONFIRMATION"}):E.formatMessage({id:"LABEL_LOADED_OFFER_INCREASED_CREDIT_TITLE_CONFIRMATION"})},le=y.map(function(e){return{label:l.createElement(He.FormattedMessage,{id:"SELECT_BILLS_ACCOUNT_TITLE",values:{accounttype:r(e.AccountType,e.IsNM1Account,j)}}),value:e.NickName,key:r(e.AccountType,e.IsNM1Account,j)}}),ue=l.useRef(null),se=function(){M==A.CurrentSection.Confirmation&&Y(I?Yt(!0,y):Yt(!1,y,C.cardType))};return l.useEffect(function(){var e,t,n,r,a,l;if(x===v.FAILED)D(!0),U("FAILED"),se();else if(1===_.length&&x===v.COMPLETED&&Object.values(R).length>0)try{for(r=(n=(0,o.__values)(Object.values(R))).next();!r.done;r=n.next()){if(""!==r.value.ErrorCodeID.trim()){D(!0),U("FAILED"),se();break}U("COMPLETED");break}}catch(i){e={error:i}}finally{try{r&&!r.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}x===v.COMPLETED&&Object.values(R).length>0&&_.length>1&&((null==(a=Object.values(R).filter(function(e){return!("Confirmation"==e.OrderFormStatus)}))?void 0:a.length)>0?(D(!0),U("FAILED"),se()):U("COMPLETED")),x===v.COMPLETED&&Object.values(R).length>0&&(F||w(I?Ht(I,y,Object.values(R)):Ht(I,y,Object.values(R),C.cardType))),x===v.COMPLETED&&ue.current&&((l=ue.current.querySelector(".payment-focus-sr"))&&(l.scrollIntoView({behavior:"smooth"}),l.focus()),setTimeout(function(){var e,t=null===(e=null==ue?void 0:ue.current)||void 0===e?void 0:e.querySelector(".payment-focus-heading");t&&(t.scrollIntoView({behavior:"smooth"}),t.focus())},100),setTimeout(function(){var e,t=null===(e=null==ue?void 0:ue.current)||void 0===e?void 0:e.querySelector(".payment-focus-sr");t&&(t.style.display="none")},1e3))},[x]),f=function(){return Object.values(R).some(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)})?E.formatMessage({id:"TRANSACTION_SUBMITTED_HEADING"}):E.formatMessage({id:"CONFIRMATION_HEADING"})},l.createElement(l.Fragment,null,x===v.PENDING&&l.createElement($t,{variant:"submitOrder"}),Object.values(R).length>0&&x===v.COMPLETED&&l.createElement("div",{className:"brui-border-gray-4",ref:ue},l.createElement("div",{className:"payment-mb-15 brui-flex brui-items-center brui-justify-between payment-mt-45",id:"ConfirmationDivID"},l.createElement("span",{className:"payment-focus-sr payment-sr-only",id:"pageConfirmationId",tabIndex:-1},E.formatMessage({id:"PAGE_TITLE_CONFIRMATON"})),l.createElement(Ye.Heading,{className:"payment-focus-heading",level:"h2",variant:"lg",tabIndex:-1,id:T?"payment-manage-heading":void 0,"aria-hidden":T?"true":void 0},f())),l.createElement("div",{className:"payment-pt-15 payment-pb-15"},l.createElement(vt,{submitMultiOrderPayment:Object.values(R),accountInputValue:S,isBankPayment:I,checkedBillItems:y,language:L,paymentItem:_,creditCardAutopayOffers:P,debitCardAutopayOffers:B,isBanCreditPreauth:H})),l.createElement("div",{className:"payment-block payment-border payment-rounded-20 payment-relative payment-px-15 sm:payment-px-30 payment-py-30 sm:payment-pb-45"},l.createElement("div",null,l.createElement(Ye.Heading,{level:"h3",variant:"md"},E.formatMessage({id:"PAYMENT_SUMMARY_TITLE"}))),l.createElement("div",{className:"payment-mt-30 sm:payment-mt-30"},l.createElement(un.SummaryInformationHeading,{title:E.formatMessage({id:"BILL_INFORMATION_TITLE"})},le.map(function(e,t){return l.createElement(kt.SingleRowInformation,{className:t>0?"payment-mt-5":"",label:e.label,value:e.value})}))),l.createElement("div",{className:"payment-mt-45"},l.createElement(un.SummaryInformationHeading,{title:E.formatMessage({id:"PAYMENT_INFORMATION_TITLE"})},l.createElement(Ut,{paymentItem:_,className:g?"":"payment-hidden",inputValue:C,isNewbank:N,isPreauth:!1,inputBankValue:h,showHeading:!1,isBankPaymentSelected:I,bankList:O,creditCardAutopayOffers:P,debitCardAutopayOffers:B,checkedBillItems:y,bankitems:k,isConfirmation:!0}))),V?I&&(X()||te()||J()||Z())?l.createElement("div",{className:"payment-mt-45"},l.createElement(un.SummaryInformationHeading,{title:E.formatMessage({id:"AUTOPAY_CREDITS_TITLE"}),role:"list"},J()?l.createElement(l.Fragment,null,l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"payment-text-yellow paymnet-text-15 payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},re.label," "))):null,X()?l.createElement(l.Fragment,null,l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"paymnet-text-15 payment-text-blue payment-mr-5",iconClass:"bi_brui",iconName:"bi_tag_note-big"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},ae.label," "))):null,Z()?l.createElement(l.Fragment,null,l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"payment-text-yellow paymnet-text-15 payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},ie.label," "))):null,te()?l.createElement(l.Fragment,null,l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"paymnet-text-15 payment-text-blue payment-mr-5",iconClass:"bi_brui",iconName:"bi_tag_note-big"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},oe.label," "))):null,X()?l.createElement(l.Fragment,null,null===(a=null==G?void 0:G.debits)||void 0===a?void 0:a.map(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.map(function(t){var r,a,i=null===(r=null==t?void 0:t.autopayOffers)||void 0===r?void 0:r.some(function(e){return"RETAIN"!==e.offerImpact});return l.createElement(l.Fragment,{key:t.subscriberTelephoneNumber},y&&y.length>1&&i?l.createElement("p",{className:"payment-text-14 payment-font-bold"},e.banInfo&&e.banInfo.nickName):null,null===(a=null==t?void 0:t.autopayOffers)||void 0===a?void 0:a.map(function(e,r){return"RETAIN"!==t.autopayOffers[0].offerImpact?l.createElement(kt.SingleRowInformation,{key:r,label:t.subscriberTelephoneNumber,value:"en"==L?"$"+e.discountAmount.toFixed(2)+"/mo.":e.discountAmount.toFixed(2).replace(".",",")+" $/mois",needSRText:!0,srText:n(e.discountAmount,L),role:"listitem",isMultiBan:y.length>1,className:y.length>1?"payment-text-gray":"payment-text-black"}):null}))})})):null,te()?l.createElement(l.Fragment,null,null===(i=null==K?void 0:K.credits)||void 0===i?void 0:i.map(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.map(function(t){var n,r,a=null===(n=null==t?void 0:t.autopayOffers)||void 0===n?void 0:n.some(function(e){return"RETAIN"!==e.offerImpact});return l.createElement(l.Fragment,{key:t.subscriberTelephoneNumber},k&&k.length>1&&a?l.createElement("p",{className:"payment-text-14 payment-text-gray"},e.banInfo&&e.banInfo.nickName,":"):null,null===(r=null==t?void 0:t.autopayOffers)||void 0===r?void 0:r.map(function(e,n){return"RETAIN"!==t.autopayOffers[0].offerImpact?l.createElement(kt.SingleRowInformation,{key:n,label:l.createElement("strong",null,t.subscriberTelephoneNumber),value:l.createElement("div",{className:"payment-inline-block"},"from"," ",l.createElement(Ye.Price,{className:"payment-text-gray payment-inline payment-lowercase !payment-text-14 payment-font-normal",price:e.currentdiscountAmount,variant:"defaultPrice",suffixText:"perMonth",language:L})," ","to  ",l.createElement(Ye.Price,{className:"payment-inline payment-lowercase !payment-text-14 payment-font-normal",price:e.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:L})),role:"listitem",isMultiBan:y.length>1,className:"payment-text-black"}):null}))})})):null,J()?l.createElement(l.Fragment,null,null===(u=null==K?void 0:K.credits)||void 0===u?void 0:u.map(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.map(function(t){var n,r,a=null===(n=null==t?void 0:t.autopayOffers)||void 0===n?void 0:n.some(function(e){return"RETAIN"!==e.offerImpact});return l.createElement(l.Fragment,{key:t.subscriberTelephoneNumber},k&&k.length>1&&a?l.createElement("p",{className:"payment-text-14 payment-text-gray"},e.banInfo&&e.banInfo.nickName,":"):null,null===(r=null==t?void 0:t.autopayOffers)||void 0===r?void 0:r.map(function(e,n){return"RETAIN"!==t.autopayOffers[0].offerImpact?l.createElement(kt.SingleRowInformation,{key:n,label:l.createElement("strong",null,t.subscriberTelephoneNumber),value:l.createElement("div",{className:"payment-inline-block"},"from"," ",l.createElement(Ye.Price,{className:"payment-text-gray payment-inline payment-lowercase !payment-text-14 payment-font-normal",price:e.currentdiscountAmount,variant:"defaultPrice",suffixText:"perMonth",language:L})," ","to  ",l.createElement(Ye.Price,{className:"payment-inline payment-lowercase !payment-text-14 payment-font-normal",price:e.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:L})),role:"listitem",isMultiBan:y.length>1,className:"payment-text-black"}):null}))})})):null,Z()?l.createElement(l.Fragment,null,null===(s=null==G?void 0:G.debits)||void 0===s?void 0:s.map(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.map(function(t){var r,a,i=null===(r=null==t?void 0:t.autopayOffers)||void 0===r?void 0:r.some(function(e){return"RETAIN"!==e.offerImpact});return l.createElement(l.Fragment,{key:t.subscriberTelephoneNumber},y&&y.length>1&&i?l.createElement("p",{className:"payment-text-14 payment-font-bold"},e.banInfo&&e.banInfo.nickName):null,null===(a=null==t?void 0:t.autopayOffers)||void 0===a?void 0:a.map(function(e,r){return"RETAIN"!==t.autopayOffers[0].offerImpact?l.createElement(kt.SingleRowInformation,{key:r,label:t.subscriberTelephoneNumber,value:"en"==L?"$"+e.discountAmount.toFixed(2)+"/mo.":e.discountAmount.toFixed(2).replace(".",",")+" $/mois",needSRText:!0,srText:n(e.discountAmount,L),role:"listitem",isMultiBan:y.length>1,className:y.length>1?"payment-text-gray":"payment-text-black"}):null}))})})):null),l.createElement("div",{className:"brui-mt-15"},l.createElement("p",{className:"brui-text-gray brui-text-12 brui-leading-14"},l.createElement("strong",null,"Note: "),E.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE"})))):Q()||ee()||ne()||$()?l.createElement("div",{className:"payment-mt-45"},l.createElement(un.SummaryInformationHeading,{title:E.formatMessage({id:"AUTOPAY_CREDITS_TITLE"}),role:"list"},ee()?l.createElement(l.Fragment,null,l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"payment-text-yellow paymnet-text-15 payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},oe.label," "))):null,ne()?l.createElement(l.Fragment,null,l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"payment-text-yellow paymnet-text-15 payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},re.label," "))):null,Q()?l.createElement(l.Fragment,null,l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"paymnet-text-15 payment-text-blue payment-mr-5",iconClass:"bi_brui",iconName:"bi_tag_note-big"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},ae.label," "))):null,$()?l.createElement(l.Fragment,null,l.createElement(Ye.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5 payment-pb-15"},l.createElement(Ye.Icon,{className:"payment-text-yellow paymnet-text-15 payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),l.createElement("p",{className:"payment-text-gray payment-text-14"},ie.label," "))):null,Q()?l.createElement(l.Fragment,null,null===(c=null==G?void 0:G.credits)||void 0===c?void 0:c.map(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.map(function(t){var r,a,i=null===(r=null==t?void 0:t.autopayOffers)||void 0===r?void 0:r.some(function(e){return"RETAIN"!==e.offerImpact});return l.createElement(l.Fragment,{key:t.subscriberTelephoneNumber},y&&y.length>1&&i?l.createElement("p",{className:"payment-text-14 payment-font-bold"},e.banInfo&&e.banInfo.nickName):null,null===(a=null==t?void 0:t.autopayOffers)||void 0===a?void 0:a.map(function(e,r){return"RETAIN"!==t.autopayOffers[0].offerImpact?l.createElement(kt.SingleRowInformation,{key:r,label:t.subscriberTelephoneNumber,value:"en"==L?"$"+e.discountAmount.toFixed(2)+"/mo.":e.discountAmount.toFixed(2).replace(".",",")+" $/mois",needSRText:!0,srText:n(e.discountAmount,L),role:"listitem",isMultiBan:y.length>1,className:y.length>1?"payment-text-gray":"payment-text-black"}):null}))})})):null,ee()?l.createElement(l.Fragment,null,null===(m=null==W?void 0:W.credits)||void 0===m?void 0:m.map(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.map(function(t){var n,r,a=null===(n=null==t?void 0:t.autopayOffers)||void 0===n?void 0:n.some(function(e){return"RETAIN"!==e.offerImpact});return l.createElement(l.Fragment,{key:t.subscriberTelephoneNumber},k&&k.length>1&&a?l.createElement("p",{className:"payment-text-14 payment-text-gray"},e.banInfo&&e.banInfo.nickName,":"):null,null===(r=null==t?void 0:t.autopayOffers)||void 0===r?void 0:r.map(function(e,n){return"RETAIN"!==t.autopayOffers[0].offerImpact?l.createElement(kt.SingleRowInformation,{key:n,label:l.createElement("strong",null,t.subscriberTelephoneNumber),value:l.createElement("div",{className:"payment-inline-block"},"from"," ",l.createElement(Ye.Price,{className:"payment-text-gray payment-inline payment-lowercase !payment-text-14 payment-font-normal",price:e.currentdiscountAmount,variant:"defaultPrice",suffixText:"perMonth",language:L})," ","to  ",l.createElement(Ye.Price,{className:"payment-inline payment-lowercase !payment-text-14 payment-font-normal",price:e.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:L})),role:"listitem",isMultiBan:y.length>1,className:"payment-text-black"}):null}))})})):null,ne()?l.createElement(l.Fragment,null,null===(d=null==W?void 0:W.credits)||void 0===d?void 0:d.map(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.map(function(t){var n,r,a=null===(n=null==t?void 0:t.autopayOffers)||void 0===n?void 0:n.some(function(e){return"RETAIN"!==e.offerImpact});return l.createElement(l.Fragment,{key:t.subscriberTelephoneNumber},k&&k.length>1&&a?l.createElement("p",{className:"payment-text-14 payment-text-gray"},e.banInfo&&e.banInfo.nickName,":"):null,null===(r=null==t?void 0:t.autopayOffers)||void 0===r?void 0:r.map(function(e,n){return"RETAIN"!==t.autopayOffers[0].offerImpact?l.createElement(kt.SingleRowInformation,{key:n,label:l.createElement("strong",null,t.subscriberTelephoneNumber),value:l.createElement("div",{className:"payment-inline-block"},"from"," ",l.createElement(Ye.Price,{className:"payment-text-gray payment-inline payment-lowercase !payment-text-14 payment-font-normal",price:e.currentdiscountAmount,variant:"defaultPrice",suffixText:"perMonth",language:L})," ","to  ",l.createElement(Ye.Price,{className:"payment-inline payment-lowercase !payment-text-14 payment-font-normal",price:e.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:L})),role:"listitem",isMultiBan:y.length>1,className:"payment-text-black"}):null}))})})):null,$()?l.createElement(l.Fragment,null,null===(p=null==G?void 0:G.credits)||void 0===p?void 0:p.map(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.map(function(t){var r,a,i=null===(r=null==t?void 0:t.autopayOffers)||void 0===r?void 0:r.some(function(e){return"RETAIN"!==e.offerImpact});return l.createElement(l.Fragment,{key:t.subscriberTelephoneNumber},y&&y.length>1&&i?l.createElement("p",{className:"payment-text-14 payment-font-bold"},e.banInfo&&e.banInfo.nickName):null,null===(a=null==t?void 0:t.autopayOffers)||void 0===a?void 0:a.map(function(e,r){return"RETAIN"!==t.autopayOffers[0].offerImpact?l.createElement(kt.SingleRowInformation,{key:r,label:t.subscriberTelephoneNumber,value:"en"==L?"$"+e.discountAmount.toFixed(2)+"/mo.":e.discountAmount.toFixed(2).replace(".",",")+" $/mois",needSRText:!0,srText:n(e.discountAmount,L),role:"listitem",isMultiBan:y.length>1,className:y.length>1?"payment-text-gray":"payment-text-black"}):null}))})})):null),l.createElement("div",{className:"brui-mt-15"},l.createElement("p",{className:"brui-text-gray brui-text-12 brui-leading-14"},l.createElement("strong",null,"Note: "),E.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE"})))):"":""),l.createElement("div",{className:"payment-mt-30 payment-pb-45 sm:payment-mb-60 border-1 payment-border-b-1 payment-border-b-[#CDCFD5] sm:payment-border-none payment-mx-[-16px] payment-px-[16px] sm:payment-mx-0 sm:payment-px-0"},l.createElement(Ye.Button,{size:"regular",variant:"secondary",onClick:function(){window.location.href="/"}},E.formatMessage({id:"BACK_TO_MY_BELL"})))))},yn=function(e){return{submitMultiOrderPayment:e.submitMultiOrderPayment,submitMultiOrderFormStatus:e.submitMultiOrderFormStatus}},gn=function(e){return{setOmnitureOnConfirmation:function(t){return e((0,g.OmnitureOnConfirmation)({data:t}))},setApiConfirmationStatus:function(t){return e((0,g.apiConfirmationStatus)({type:t}))},setOmnitureOnConfirmationFailure:function(t){return e((0,g.OmnitureOnConfirmationFailure)({data:t}))}}},vn=(0,s.connect)(yn,gn)((0,He.injectIntl)(_n)),Nn={error:"payment-text-red payment-text-[60px] payment-box-border payment-static payment-text-center",warning:"payment-text-yellow payment-text-[60px] payment-box-border payment-static payment-text-center"},An=function(e){var t=e.iconVariant,n=e.errorHeaderText,r=e.errorHeaderTextSR,a=e.errorHeaderStyle,i=e.errorTextStyle,o=e.errorText,l=e.buttonText,s=e.onButtonClick,c=e.suggestionList;return u().createElement("div",{className:"payment-text-center payment-mt-60 sm:payment-max-w-[543px] md:payment-max-w-[548px] payment-mx-auto"},u().createElement("span",{"aria-label":"Warning"},u().createElement("span",{role:"img","aria-hidden":"true"},u().createElement(Ye.Icon,{iconClass:"bi_brui",iconName:"bi_exclamation_c",className:Nn[t]}))),u().createElement(Ye.Heading,{level:"h2",variant:"default",className:["payment-text-32 payment-leading-38 payment-text-black payment-mb-5 payment-mt-30",a].join(" ").trim()},u().createElement("span",{"aria-hidden":"true"},n),u().createElement("span",{"aria-hidden":"false",className:"payment-sr-only"},r)),u().createElement("p",{className:["payment-text-gray payment-overflow-x-hidden payment-font-sans payment-text-[16px] payment-leading-[25px]",i].join(" ").trim()},o),c&&u().createElement(u().Fragment,null,u().createElement(Ye.Divider,{direction:"horizontal",width:1,className:"payment-my-45 payment-bg-gray-4"}),u().createElement("div",{className:"payment-mb-45"},u().createElement(Ye.Text,{className:"brui-text-14 payment-text-black payment-font-bold"},c.label),u().createElement("div",{className:"payment-mt-15 payment-text-left sm:payment-text-center"},c.items.map(function(e){return u().createElement("div",{className:"payment-flex sm:payment-justify-center payment-mb-5 last:payment-mb-0 payment-gap-5"},u().createElement("span",{className:"payment-mr-3",role:"img","aria-hidden":"true"},u().createElement(Ye.Icon,{iconClass:"bi_brui",iconName:"bi_check_small_flat_fin",className:"payment-text-24 payment-text-blue"})),u().createElement(Ye.Text,{className:"payment-leading-18 brui-text-14 payment-mt-3 payment-text-gray"},e))})))),u().createElement("div",{className:"payment-rounded-lg payment-m-20 payment-[focus-within:ring-4]"},u().createElement(Ye.Button,{variant:"primary",onClick:s},l)))},Cn=An,hn=function(e){var t=e.intl;return l.createElement(Cn,{iconVariant:"error",errorHeaderText:t.formatMessage({id:"FAILURE_API_BAN_HEADING"}),errorHeaderTextSR:t.formatMessage({id:"FAILURE_API_BAN_HEADING_SR"}),errorText:l.createElement(l.Fragment,null,l.createElement("p",null,t.formatMessage({id:"FAILURE_API_BAN_MAIN_DESC"}),"  "),l.createElement("p",{className:"payment-mt-8"},t.formatMessage({id:"FAILURE_API_BAN_MAIN_DESC_2"}),"  ")),buttonText:t.formatMessage({id:"FAILURE_API_BAN_BUTTON"}),suggestionList:{label:t.formatMessage({id:"FAILURE_API_BAN_SUB_DESC"}),items:[t.formatMessage({id:"FAILURE_API_BAN_SUB_DESC_LISTITEM_1"}),t.formatMessage({id:"FAILURE_API_BAN_SUB_DESC_LISTITEM_2"})]},onButtonClick:function(){window.location.reload()}})},Tn=(0,He.injectIntl)(hn),In=function(e){var t=e.isModalOpen,n=e.setIsModalOpen,r=e.intl;return u().createElement("div",null,t&&u().createElement(Ye.Modal,{className:"payment-toaster",id:"cancel-preauth-success-modal","aria-labelledby":"cancel-preauth-success-modal-title",onEscapeKeyPressed:function(){n(!1)}},u().createElement(Ye.ModalContent,{useDefaultRadius:!1},u().createElement(Ye.ModalBody,{isDefaultPadding:!1,className:"payment-py-15 payment-px-15 payment-border-b-4 payment-border-[#339043] payment-rounded-2"},u().createElement("div",{className:"payment-flex payment-gap-10 payment-leading-18 payment-w-full"},u().createElement("span",{className:"bi_brui bi_small_checkmark_fill brui-text-green brui-text-24",role:"img","aria-hidden":"true","aria-label":" "}),u().createElement("div",{id:"account-fetched",className:"brui-flex brui-flex-col brui-text-14 "},u().createElement("span",{className:"payment-mt-[3px] payment-text-gray"},r.formatMessage({id:"SUCCESS_TOAST_MESSAGE"}))),u().createElement("button",{onClick:function(){n(!1),window.location.href="/"},type:"button","aria-label":"Close dialog box",className:"brui-flex brui-rounded-2 focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 payment-h-12 payment-ml-auto",id:"no-name-on-card-close-button"},u().createElement("span",{className:"bi_close_bold bi_brui payment-text-12 payment-text-gray",role:"img","aria-hidden":"true","aria-label":" "}),u().createElement("span",{className:"brui-sr-only"},r.formatMessage({id:"CLOSE_ICON_SR"}))))))))},Rn=(0,He.injectIntl)(In),xn=function(e){var t=e.intl,n=e.someBansFailed,a=e.language,i={MyBill:t.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:t.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:t.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:t.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:t.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:t.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:t.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:t.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})},o=[{description:"",items:n.map(function(e){return{label:t.formatMessage({id:"CANCEL_FAILED_BANS_ACCOUNT_TITLE"},{accounttype:r(e.AccountType,e.IsNM1Account,i)}),accountNumber:e.NickName}})}],l=n.length>1;return u().createElement("div",null,u().createElement("div",{className:"payment-mt-30 sm:payment-mt-[60px] payment-pb-15 sm:payment-pb-15"},u().createElement(Mt,{multiBanFail:l},o&&o.map(function(e){return u().createElement(dt,{label:e.description},e.items.map(function(e){return u().createElement(bt,{label:e.label,labelDescription:e.accountNumber,variant:"accountListSimple",priceSettings:{language:a,showZeroDecimalPart:!0}})}))}))),u().createElement("div",{className:""},u().createElement(St,null)),u().createElement("div",{className:"payment-mt-15 sm:payment-mt-30 payment-mb-45 sm:payment-mb-60"},u().createElement(Ye.Button,{onClick:function(){window.location.href="/"},size:"regular",variant:"secondary"},t.formatMessage({id:"BACK_TO_MY_BELL"}))))},Sn=(0,He.injectIntl)(xn),On=function(e){var t,r,a,i,s,c,m,d,p,f,b,E,_,y,g,v,N,C,h,T,I,R,x=(null===(r=null===(t=e.Config)||void 0===t?void 0:t.getPaymentItem)||void 0===r?void 0:r.length)>1,S=(0,o.__read)((0,l.useState)(x?"inactive":"active"),2),O=S[0],M=S[1],L=(0,o.__read)((0,l.useState)(":"),2),D=L[0],P=L[1],B=(0,o.__read)((0,l.useState)(x?Lt.CurrentSection.SelectBills:Lt.CurrentSection.PaymentMethod),2),k=B[0],w=B[1],U=(0,o.__read)((0,l.useState)(A.defaultCreditCardInputValue),2),F=U[0],H=U[1],Y=(0,o.__read)((0,l.useState)(A.defaultBankInputValue),2),j=Y[0],G=Y[1],V=(0,o.__read)((0,l.useState)(!1),2),z=V[0],q=V[1],K=(0,o.__read)((0,l.useState)(!1===x?null===(a=e.Config)||void 0===a?void 0:a.getPaymentItem:[]),2),W=K[0],X=K[1],Q=(0,o.__read)((0,l.useState)(!1===x?[{accountNumber:null===(s=null===(i=e.Config)||void 0===i?void 0:i.getPaymentItem[0])||void 0===s?void 0:s.Ban,subNumber:null===(m=null===(c=e.Config)||void 0===c?void 0:c.getPaymentItem[0])||void 0===m?void 0:m.subscriberId,transactionID:n(null===(p=null===(d=e.Config)||void 0===d?void 0:d.getPaymentItem[0])||void 0===p?void 0:p.Ban,null===(f=e.Config)||void 0===f?void 0:f.transactionIdArray),payBalanceAmnt:0}]:[]),2),Z=Q[0],$=Q[1],J=(0,o.__read)((0,l.useState)(!(!F||!F.cardNumber)),1)[0],ee=(0,o.__read)((0,l.useState)(!1),2),te=ee[0],ne=ee[1],re=(0,o.__read)((0,l.useState)(!1),2),ae=re[0],ie=re[1],oe=(0,o.__read)((0,l.useState)(!1),2),le=oe[0],ue=oe[1],se=(0,o.__read)((0,l.useState)(""),2),ce=se[0],me=se[1],de=(0,o.__read)((0,l.useState)(!1),2),pe=de[0],fe=de[1],be=(0,o.__read)((0,l.useState)([]),2),Ee=be[0],_e=be[1],ye=e.localization,ge=e.Config,ve=e.isLoading,Ne=function(e){D!==e&&P(e)},Ae=function(){var e=sessionStorage.getItem("itemsChecked");e&&e.length>0&&sessionStorage.removeItem("itemsChecked")};return(0,l.useEffect)(function(){var t,n,r,a,i=new URLSearchParams(document.location.search.substring(1)).get("code");i&&null!=i?(me(i),a=(null===(t=null==ge?void 0:ge.currentUrl)||void 0===t?void 0:t.substring(0,(null===(n=null==ge?void 0:ge.currentUrl)||void 0===n?void 0:n.lastIndexOf("/"))+1))+"GetFieldModifyViewManage",window.history.pushState({path:a},"",a),e.getInteracBankInfoAction(i),1===(null===(r=null==ge?void 0:ge.getPaymentItem)||void 0===r?void 0:r.length)&&Ae()):Ae()},[]),(0,l.useEffect)(function(){e.redirectUrlAction({})},[]),(0,l.useEffect)(function(){if(te){var e=document.getElementById("container");e&&e.childElementCount>0&&e.replaceWith(e.cloneNode(!0))}},[te]),v=(0,l.useRef)(D),(0,l.useEffect)(function(){v.current=D}),(0,l.useEffect)(function(){var e,t=ge.pagetitle||"",n=t.replace(/Step/g,"");v.current!==D&&(t="".concat(t," ").concat(D),document.title=R(t)||""),k===Lt.CurrentSection.Confirmation&&(e="".concat(Lt.PageTitleCurrentSection.Confirmation," ").concat(n),document.title=R(e)||"")},[D,k]),N=null!==(E=null===(b=null==ge?void 0:ge.getPaymentItem)||void 0===b?void 0:b.some(function(e){return e.Due>0&&e.isOneTimePaymentEligible&&e.AccountType!==Lt.PaymentItemAccountType.OneBill&&!e.isLastBillOnPreauth}))&&void 0!==E&&E,C=null!==(y=null===(_=null==ge?void 0:ge.getPaymentItem)||void 0===_?void 0:_.some(function(e){return e.Due>0&&e.isOneTimeCreditCardPaymentEnabled&&e.AccountType!==Lt.PaymentItemAccountType.OneBill&&!e.isLastBillOnPreauth}))&&void 0!==y&&y,h=null!==(g=null==W?void 0:W.some(function(e){return e.Due>0&&e.AccountType!==Lt.PaymentItemAccountType.OneBill&&!e.isLastBillOnPreauth}))&&void 0!==g&&g,T="ON"===(null==ge?void 0:ge.IsAutopayCreditEnabled),I="ON"===(null==ge?void 0:ge.IsInteracEnabled),N=N&&h,C=C&&h,R=function(e){return(new DOMParser).parseFromString(e,"text/html").documentElement.textContent},u().createElement(He.IntlProvider,{locale:ye.locale,messages:ye.messages},ve?u().createElement($t,null):u().createElement(Ye.Container,null,!te&&!pe&&u().createElement(u().Fragment,null,k!==Lt.CurrentSection.Confirmation&&u().createElement(u().Fragment,null,u().createElement(bn,{paymentItem:ge.getPaymentItem,isShow:x,onCurrentSteps:Ne,setCurrentSection:w,currentSection:k,setCheckedBillItems:X,paymentItems:ge.getPaymentItem,setAccountValues:$,accountInputValues:Z,transactionIds:ge.transactionIdArray,managePreauth:ge.selectedUpdatePaymentMethod,creditCardAutopayOffers:ge.creditCardAutopayOffers,debitCardAutopayOffers:ge.debitCardAutopayOffers}),u().createElement(Wt,{paymentItem:ge.getPaymentItem,isHeadingStepActive:k===Lt.CurrentSection.PaymentMethod?"active":"inactive",isSingleClickEnableForPACC:N,isSingleClickEnableForPAD:C,onCurrentSteps:Ne,setHeadingSteps:M,paymentHeadingStepState:O,setInputValue:H,inputValue:F,setInputBankValue:G,inputBankValue:j,setIsBankSelected:q,setCurrentSection:w,currentSection:k,checkedBillItems:W,bankList:ge.getBankList,accountInputValues:Z,province:ge.province,language:ge.language,isBankSelected:z,creditCardAutopayOffers:ge.creditCardAutopayOffers,debitCardAutopayOffers:ge.debitCardAutopayOffers,removedSubscriberOffers:ge.removedSubscriberOffers,managePreauth:ge.selectedUpdatePaymentMethod,setCancelPreauthSectionClicked:ie,cancelPreauthSectionClicked:ae,setIsModalOpen:ue,setApiSatusIsFailed:ne,IsAutopayCreditEnabled:T,IsInteracEnabled:I,InteracCode:ce,setSomeCancellationFailed:fe,setBansCancellationFailed:_e}),!ae&&u().createElement(nn,{isActive:k===Lt.CurrentSection.TermsAndCondition,onCurrentSteps:Ne,setCurrentSection:w,currentSection:k,checkedBillItems:W,paymentItem:ge.getPaymentItem,province:ge.province,language:ge.language,userProfileProv:ge.userProfileProvince,accountInputValues:Z,isBankSelected:z,setApiSatusIsFailed:ne,inputValue:F,creditCardAutopayOffers:ge.creditCardAutopayOffers,debitCardAutopayOffers:ge.debitCardAutopayOffers})),k===Lt.CurrentSection.Confirmation&&u().createElement(vn,{paymentItem:ge.getPaymentItem,checkedBillItems:W,showPaymentSummary:!0,isNewbank:!1,inputValue:F,isShow:k===Lt.CurrentSection.Confirmation,inputBankValue:j,isBankPaymentSelected:z,BankList:ge.getBankList,showCurrentBalance:N||C,language:ge.language,accountInputValues:Z,currentSection:k,isBanCreditPreauth:J,creditCardAutopayOffers:ge.creditCardAutopayOffers,debitCardAutopayOffers:ge.debitCardAutopayOffers,setApiSatusIsFailed:ne,apiSatusIsFailed:te})),te&&u().createElement(Tn,null),u().createElement(Rn,{isModalOpen:le,setIsModalOpen:ue}),pe&&Ee&&Ee.length>0&&u().createElement(Sn,{someBansFailed:Ee,language:ge.language})))},Mn=function(e,t){return{localization:e.localization,Config:t.Config,isLoading:e.isLoading,cancelPreauthStatus:e.cancelPreauthStatus,cancelPreauthPayments:e.cancelPreauthPayments}},Ln=function(e){return{redirectUrlAction:function(){e((0,g.getRedirectUrl)({}))},getInteracBankInfoAction:function(t){e((0,g.getInteracBankInfo)({code:t}))}}},Dn=(0,s.connect)(Mn,Ln)(On),Pn=function(e){function t(t,n,r){var a=e.call(this)||this;return a.store=t,a.config=n,a}return(0,o.__extends)(t,e),t.prototype.init=function(){var e,t;this.config.setConfig(c.LoggerConfigKeys.SeverityLevel,this.config.logLevel),this.store.dispatch((0,g.getPassKey)({ban:this.config.getPaymentItem[0].Ban,sub:this.config.getPaymentItem[0].subscriberId})),null!=this.config.getPaymentItem&&1===this.config.getPaymentItem.length&&(t=[{accountNumber:(e=this.config.getPaymentItem[0]).Ban,subNumber:e.subscriberId,transactionID:n(e.Ban,this.config.transactionIdArray),payBalanceAmnt:0}],this.store.dispatch((0,g.createMultiPaymentAction)({ban:e.Ban,type:e.AccountType===A.PaymentItemAccountType.OneBill,details:t,sub:e.subscriberId})))},t.prototype.destroy=function(){this.store.destroy()},t.prototype.render=function(e){var t=this.store,n=this.config;e.render(l.createElement(s.Provider,{store:t},l.createElement(Dn,{Config:n})))},(0,o.__decorate)([(0,c.Widget)({namespace:"Preauth/Manage"}),(0,o.__metadata)("design:paramtypes",[Fe,y,c.Logger])],t)}(c.ViewWidget),Bn=Pn}(),t}())},function(e){"use strict";e.exports=n},function(e){"use strict";e.exports=r},function(e){"use strict";e.exports=a},function(e){"use strict";e.exports=i},function(e){"use strict";e.exports=o},function(e){"use strict";e.exports=l},function(e){"use strict";e.exports=u}],d={};return s.d=function(e,t){for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c={},function(){"use strict";function e(e,t,r){var l=n(n({},e),{"loader.staticWidgetMappings":{"bell-preauth-manage":{factory:function(){return s(3)},namespace:"Preauth/Manage"}}});(0,o.Init)(l),i.render(a.createElement(o.WidgetLoader,{widget:"bell-preauth-manage"}),document.getElementById(t))}var t,n,r,a,i,o;s.r(c),s.d(c,{initialize:function(){return e}}),t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},t(e,n)},n=function(){return n=Object.assign||function(e){var t,n,r,a;for(n=1,r=arguments.length;n<r;n++)for(a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},n.apply(this,arguments)},Object.create,Object.create,r=function(e){return r=Object.getOwnPropertyNames||function(e){var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},r(e)},"function"==typeof SuppressedError&&SuppressedError,a=s(1),i=s(2),o=s(5)}(),c}()},"object"==typeof exports&&"object"==typeof module?module.exports=factory(require("react"),require("react-dom"),require("react-redux"),require("bwtk"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"),require("react-intl")):"function"==typeof define&&define.amd?define("Bundle",["react","react-dom","react-redux","bwtk","redux","redux-actions","redux-observable","rxjs","react-intl"],factory):"object"==typeof exports?exports.Bundle=factory(require("react"),require("react-dom"),require("react-redux"),require("bwtk"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"),require("react-intl")):this.Bundle=factory(this.React,this.ReactDOM,this.ReactRedux,this.bwtk,this.Redux,this.ReduxActions,this.ReduxObservable,this.rxjs,this.ReactIntl);
//# sourceMappingURL=bell-preauth-manage-bundle-bundle.min.js.map