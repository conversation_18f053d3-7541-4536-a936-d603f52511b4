import { Id, Name, Namespace, LoggerServices, Logger } from "../../../../@types";
import { LoggerConfig } from "./LoggerConfig";
export declare class ConsoleLogger extends Logger {
    protected context: string;
    private config;
    protected labels: string[];
    static create(loggerServices: LoggerServices, id: Id, name: Name, namespace: Namespace): Logger;
    constructor(context: string, config: LoggerConfig, labels: string[]);
    debug(message?: any, ...optionalParams: any[]): void;
    error(message?: any, ...optionalParams: any[]): void;
    info(message?: any, ...optionalParams: any[]): void;
    log(message?: any, ...optionalParams: any[]): void;
    warn(message?: any, ...optionalParams: any[]): void;
    clear(): void;
    private hasSeverityLevel;
    protected call(method: string, message?: any, ...optionalParams: any[]): void;
}
