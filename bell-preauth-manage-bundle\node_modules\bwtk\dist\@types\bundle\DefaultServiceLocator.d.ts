import { DiServices, StoreServices, WidgetLoaderServices, ServiceLocator, EventStreamServices } from "../@types";
export declare class DefaultServiceLocator extends ServiceLocator {
    private injector;
    private baseinjector;
    constructor(loaderType: any);
    get di(): DiServices;
    get loader(): WidgetLoaderServices;
    get store(): StoreServices;
    get stream(): EventStreamServices;
    getService(id: any): any;
    private interceptors;
    addServiceInterceptor(id: string, interceptorOrMembers: Function | string[], interceptor?: Function): () => void;
    private appendInterceptor;
    private createMemberInterceptorMap;
    private getInterceptors;
    private getMembersInterceptors;
    private rebuildInjectorWithInterceptors;
    private createProxyBindings;
}
