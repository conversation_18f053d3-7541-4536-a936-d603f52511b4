"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.shallowEqual = exports.assignUniqueKeysToParts = exports.DEFAULT_INTL_CONFIG = exports.invariantIntlContext = void 0;
var tslib_1 = require("tslib");
var React = tslib_1.__importStar(require("react"));
var ecma402_abstract_1 = require("@formatjs/ecma402-abstract");
var intl_1 = require("@formatjs/intl");
function invariantIntlContext(intl) {
    (0, ecma402_abstract_1.invariant)(intl, '[React Intl] Could not find required `intl` object. ' +
        '<IntlProvider> needs to exist in the component ancestry.');
}
exports.invariantIntlContext = invariantIntlContext;
exports.DEFAULT_INTL_CONFIG = tslib_1.__assign(tslib_1.__assign({}, intl_1.DEFAULT_INTL_CONFIG), { textComponent: React.Fragment });
/**
 * Takes a `formatXMLElementFn`, and composes it in function, which passes
 * argument `parts` through, assigning unique key to each part, to prevent
 * "Each child in a list should have a unique "key"" React error.
 * @param formatXMLElementFn
 */
function assignUniqueKeysToParts(formatXMLElementFn) {
    return function (parts) {
        // eslint-disable-next-line prefer-rest-params
        return formatXMLElementFn(React.Children.toArray(parts));
    };
}
exports.assignUniqueKeysToParts = assignUniqueKeysToParts;
function shallowEqual(objA, objB) {
    if (objA === objB) {
        return true;
    }
    if (!objA || !objB) {
        return false;
    }
    var aKeys = Object.keys(objA);
    var bKeys = Object.keys(objB);
    var len = aKeys.length;
    if (bKeys.length !== len) {
        return false;
    }
    for (var i = 0; i < len; i++) {
        var key = aKeys[i];
        if (objA[key] !== objB[key] ||
            !Object.prototype.hasOwnProperty.call(objB, key)) {
            return false;
        }
    }
    return true;
}
exports.shallowEqual = shallowEqual;
