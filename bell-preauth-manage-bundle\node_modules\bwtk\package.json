{"name": "bwtk", "version": "6.1.0", "main": "./dist/bwtk.js", "types": "./dist/@types/index.d.ts", "author": "BELL", "license": "UNLICENSED", "private": false, "repository": {"type": "git", "url": "https://gitlab.int.bell.ca/uxp/bwtk"}, "files": ["lib/", "dist/"], "scripts": {"dev": "webpack -w", "build": "webpack", "build:dev": "webpack --env -d", "build:prod": "webpack --env -p", "test": "cross-env NODE_PATH=./lib cross-env TS_NODE_PROJECT=test/tsconfig.json mocha --opts test/mocha.opts", "test:tdd": "cross-env NODE_PATH=./lib cross-env TS_NODE_PROJECT=test/tsconfig.json mocha --opts test/mocha.opts -w", "clean": "rm -f package-lock.json && rm -f yarn.lock && rm -rf dist && rm -rf node_modules", "lint": "eslint .", "bc": "webpack && cp -R ~/Work/bwtk/dist ~/Work/WidgetToolkitExamples/node_modules/bwtk", "cp": "cp -R ~/Work/bwtk/dist ~/Work/WidgetToolkitExamples/node_modules/bwtk"}, "dependencies": {"@reduxjs/toolkit": "^2.5.0", "@types/chai": "^5.0.1", "@types/enzyme": "3.10.5", "@types/enzyme-adapter-react-16": "1.0.6", "@types/jsdom": "16.2.0", "@types/mocha": "^10.0.10", "@types/node": "13.13.4", "@types/proxyquire": "^1.3.28", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@types/redux-actions": "2.6.2", "@types/webpack": "^5.28.5", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.16.0", "ajv": "^8.17.1", "chai": "^5.1.2", "cross-env": "6.0.3", "enzyme": "3.11.0", "eslint-plugin-react": "^7.37.3", "mocha": "^11.0.1", "proxyquire": "2.1.3", "react": "18.3.1", "react-dom": "18.3.1", "react-intl": "6.4.4", "react-redux": "8.1.1", "redux": "4.2.1", "redux-actions": "2.6.5", "redux-observable": "^3.0.0-rc.2", "reflect-metadata": "0.1.13", "rxjs": "7.8.1", "terser-webpack-plugin": "^5.3.14", "tslib": "2.8.1", "typescript": "5.7.2", "webpack": "^5.99.8", "webpack-common": "git+https://gitlab.int.bell.ca/uxp/webpack-common#v6.1.0", "webpack-merge": "^6.0.1"}, "peerDependencies": {"react": "*", "react-dom": "*", "react-intl": "*", "react-redux": "*", "redux": "*", "redux-actions": "*", "redux-observable": "*", "rxjs": "*"}, "devDependencies": {"@stylistic/eslint-plugin": "^2.11.0", "@stylistic/eslint-plugin-js": "^2.11.0", "eslint": "9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-webpack-plugin": "^4.2.0", "prettier": "^3.4.1", "ts-loader": "^9.5.1", "webpack-cli": "^6.0.1"}}