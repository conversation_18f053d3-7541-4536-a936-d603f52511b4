{"name": "discontinuous-range", "version": "1.0.0", "description": "for adding, subtracting, and indexing discontinuous ranges of numbers", "main": "index.js", "scripts": {"test": "./node_modules/mocha/bin/mocha -R spec test/*-test.js"}, "repository": {"type": "git", "url": "https://github.com/dtudury/discontinuous-range.git"}, "keywords": ["discontinuous", "range", "set"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dtudury/discontinuous-range/issues"}, "homepage": "https://github.com/dtudury/discontinuous-range", "devDependencies": {"mocha": "^1.21.4"}}