module.exports = {
  meta: {
    type: "suggestion",
    docs: {
      description: "Require `===` and `!==` except when checking for `null`",
      category: "Best Practices",
      recommended: true,
    },
    fixable: "code",
    schema: [],
  },

  create(context) {
    return {
      BinaryExpression(node) {
        if (["==", "!="].includes(node.operator) && node.right.value !== null) {
          context.report({
            node,
            message: `Use \`${node.operator === "==" ? "===" : "!=="}\` instead of \`${node.operator}\` (except for null checks).`,
            fix: (fixer) => fixer.replaceTextRange([node.left.range[1], node.right.range[0]], ` ${node.operator === "==" ? "===" : "!=="} `),
          });
        }
      },
    };
  },
};
