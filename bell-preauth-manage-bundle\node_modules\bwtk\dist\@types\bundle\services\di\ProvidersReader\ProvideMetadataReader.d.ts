import { ProvideMetadata } from "../../../../@types";
export declare class ProvideMetadataReader implements ProvideMetadata {
    constructor(constructor: any);
    private readonly _metadata;
    get provides(): Function;
    get withFactory(): Function | undefined;
    private get metadata();
}
export declare class NoProvideDecoratorError extends Error {
    static Message: string;
    constructor();
}
