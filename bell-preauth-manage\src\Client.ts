import { Injectable, AjaxServices, CommonFeatures, AjaxOptions } from "bwtk";
const { BaseClient } = CommonFeatures;
import { IGetRedirectUrlEpicResponse, ICancelPreauthPaymentsEpicResponse } from "./models";

import  Config  from "./Config";
import { getCceEmailPayload } from "./utils/APIUtils";

@Injectable
export class Client extends BaseClient {
  constructor(ajax: AjaxServices, private config: Config) {
    super(ajax);
  }

  get options(): AjaxOptions {
    return {
      // url: this.config.url,
      cache: false,
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "brand": this.config.brand,
        "channel": this.config.channel,
        "Province": this.config.province,
        "userID": this.config.userID,
        "accept-language": this.config.language,
        "X-CSRF-TOKEN": this.config.CSRFToken
      }
    };
  }
  get optionsOneBill(): AjaxOptions {
    return {
      // url: this.config.url,
      cache: false,
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "brand": this.config.brand,
        "channel": this.config.channel,
        "Province": this.config.province,
        "userID": this.config.userID,
        "accept-language": this.config.language,
        "X-CSRF-TOKEN": this.config.CSRFToken,
        "PM": true
      }
    };
  }

  createMultiOrderFormData(ban: string, type?: boolean, details?: any, sub?: string | null) {
    var paymentUrl = type ?  this.config.createMultiPaymentURL.replace("Onebill", `${ban}/${null}`) : this.config.createMultiPaymentURL.replace("Onebill", `${ban}/${sub}`);
    const transactionID = this.getBanSpecificTransactionId(ban);
    var url = paymentUrl + `?TransactionId=${transactionID}&province=${this.config.province}`;
    const ajaxOptions = type ? { ...this.optionsOneBill} : { ...this.options };
    const response = this.post<any>(url, {
      AccountInputValues : details
    }, ajaxOptions);
    return response;
  }

  validateMultiOrderForm(ban: string, type: boolean, details: any,  accountInputValue: any , isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined) {
    var paymentUrl = type ? this.config.createMultiPaymentURL.replace("Onebill", `${ban}/${null}`) : this.config.createMultiPaymentURL.replace("Onebill", `${ban}/${sub}`);
    const transactionID = this.getBanSpecificTransactionId(ban);
    var url = paymentUrl + `/ValidatePayment?TransactionId=${transactionID}&province=${this.config.province}`;
    const ajaxOptions = type ? { ...this.optionsOneBill} : { ...this.options };
    const response = this.post<any>(url, isBankPaymentSelected ? {
      AccountInputValues: accountInputValue,
      ValidatePADPACInput: {
        SelectedPaymentMethod: details.SelectedPaymentMethod,
        BankName: details.BankName,
        AccountNumber: details.AccountNumber,
        HolderName: details.HolderName,
        BankCode: details.BankCode,
        TransitCode: details.TransitCode
      }
    } : {
      AccountInputValues: accountInputValue,
      ValidatePADPACInput: {
        SelectedPaymentMethod: details.SelectedPaymentMethod,
        CardholderName: details.CardholderName,
        CreditCardToken: token,
        CreditCardType: details.CreditCardType,
        ExpiryYear: details.ExpiryYear,
        ExpiryMonth: details.ExpiryMonth,
        SecurityCode: details.SecurityCode
      }
    }, ajaxOptions);
    return response;
  }

  submitMultiOrderForm(
    ban: string,
    type: boolean,
    isBankPaymentSelected: boolean,
    sorryCredit: boolean,
    sorryDebit: boolean,
    details?: any[],
    sub?: string | null
  ) {
    const paymentUrl = type
      ? this.config.createMultiPaymentURL.replace("Onebill", `${ban}/${null}`)
      : this.config.createMultiPaymentURL.replace("Onebill", `${ban}/${sub}`);
  
    const transactionID = this.getBanSpecificTransactionId(ban);
    const url = `${paymentUrl}/Submit?TransactionId=${transactionID}&province=${this.config.province}`;
    const ajaxOptions = type ? { ...this.optionsOneBill } : { ...this.options };
  
    getCceEmailPayload(ban,isBankPaymentSelected,sorryCredit,sorryDebit,this.config.debitCardAutopayOffers,this.config.creditCardAutopayOffers,details)
    // const isDebit = isBankPaymentSelected;
    // const isSorry = isDebit ? sorryDebit : sorryCredit;
    // const allOffers = isDebit
    //   ? this.config.debitCardAutopayOffers
    //   : this.config.creditCardAutopayOffers;
    // const selectedPaymentMethod = isDebit ? "D" : "C";
  
    // if (details && Array.isArray(details)) {
    //   for (let i = 0; i < details.length; i++) {
    //     const currentDetail = details[i];
  
    //     if (!isSorry) {
    //       const filteredOffers = allOffers?.filter(
    //         offer => offer.Ban === currentDetail.accountNumber
    //       ) ?? [];
  
    //       const autopayEligibleSubscribers = filteredOffers
    //         .flatMap(offer => offer.AutopayEligibleSubscribers ?? [])
    //         .map(subscriber => ({
    //           mdn: subscriber.subscriberTelephoneNumber?.replace(/\D/g, ''),
    //           autopayOffers: subscriber.autopayOffers?.map(offer => ({
    //             newDiscountAmount: offer.currentdiscountAmount ?? 0,
    //             currentDiscountAmount: offer.discountAmount ?? 0,
    //             offerImpact: offer.offerImpact ?? ''
    //           })) ?? []
    //         }));
  
    //       currentDetail.incentiveDiscountDetails = [
    //         {
    //           autopayEligibleSubscribers,
    //           selectedPaymentMethod
    //         }
    //       ];
    //     } else {
    //       currentDetail.incentiveDiscountDetails = [];
    //     }
    //   }
    // }
  
    return this.post<any>(
      url,
      {
        AccountInputValues: details
      },
      ajaxOptions
    );
  }

  getPassKeyRepsonse(action: any) {
    var paymentUrl = this.config.paymentApiUrl;
    var url = paymentUrl + `${action.payload.ban}/${action.payload.sub}/payment/CreditCard/PassKey`;
    const ajaxOptions = { ...this.options }
    const response = this.get<any>(url, null, ajaxOptions);
    return response;
  }

  getBanSpecificTransactionId(ban: string) {
    var transationIdObject = this.config.transactionIdArray.filter(a => a.Ban === ban);
    var trasactionID = transationIdObject && transationIdObject[0].TransactionId;
    return trasactionID;
  }

  // getRedirectUrl() {
  //   var url =   this.config.RedirectUrl;
  //   const ajaxOptions = { ...this.options };
  //   const uri = this.config.currentUrl.split("&amp;").join("&");
  //   const response = this.post<IGetRedirectUrlEpicResponse>(url, {
  //     OneTimeCode: "",
  //     RedirectUrl: uri
  //   },
  //     ajaxOptions);
  //   return response;
  // }

  getRedirectUrl() {
    var url =   this.config.RedirectUrl;
    var uri =   this.config?.currentUrl?.substring(0, this.config?.currentUrl?.lastIndexOf("/")+1)+"GetFieldModifyViewManage";
    const ajaxOptions = { ...this.options };
    const response = this.post<IGetRedirectUrlEpicResponse>(url, {
      OneTimeCode: "",
      RedirectUrl: uri
    },
      ajaxOptions);
    return response;
  }

  getInteracBankInfo(code: string) {
    var url = this.config.BankInfoUrl;
    const ajaxOptions = { ...this.options };
    var uri = this.config?.currentUrl?.substring(0, this.config?.currentUrl?.lastIndexOf("/")+1)+"GetFieldModifyViewManage";
    const response = this.post<IGetRedirectUrlEpicResponse>(url, {
      RedirectUrl: uri,
      OneTimeCode: code
    },
      ajaxOptions);
    return response;
  }

  cancelPreauth(bans: string[])
  {
    var url = this.config.CancelApiUrl;
    const ajaxOptions = { ...this.options }
    const response = this.post<ICancelPreauthPaymentsEpicResponse>(url, bans, ajaxOptions);
    return response;
  }
}
