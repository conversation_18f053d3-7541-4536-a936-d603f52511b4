const { RuleTester } = require("eslint");
const rule = require("../lib/rules/no-var-keyword");

const ruleTester = new RuleTester({
  languageOptions: {
    ecmaVersion: 2021,
  },
});

describe("No var Keyword", () => {
  ruleTester.run("no-var-keyword", rule, {
    valid: [{ code: "let a = 5;" }, { code: "const b = 10;" }],
    invalid: [
      {
        code: "var x = 5;",
        errors: [
          { message: "Unexpected `var`, use `let` or `const` instead." },
        ],
        output: "let x = 5;",
      },
    ],
  });
});
