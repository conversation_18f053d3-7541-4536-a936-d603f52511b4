import * as React from "react";
import { Alert, Heading, Text, Button } from "@bell/bell-ui-library";
import { DivProps } from "@bell/bell-ui-library/dist/@types/src/_types";
import { injectIntl } from "react-intl";

interface AlertErrorFailure extends DivProps {
    intl: any;
    multiBanFail: boolean;
}

const AlertErrorFailureCancelComponent = (props: AlertErrorFailure) => {
    const { intl, multiBanFail, children } = props;

    const CancellationFailedHeading = (multiBanFail ? intl.formatMessage({id: "CANCEL_FAILED_HEADING_MULTI"}) : intl.formatMessage({id: "CANCEL_FAILED_HEADING_SINGLE"})) ;

    const handleOnClick = () => {
        window.location.reload();
    }

    return (
        <Alert
            variant="error"
            className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block"
            iconSize="36"
            id="alert-2">
            <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0 payment-flex-1">
                <Heading level="h2" variant="xs" className="payment-mb-5 payment-font-sans payment-text-red ">
                    <span dangerouslySetInnerHTML={{ __html: CancellationFailedHeading}}></span>
                </Heading>
                <div className="payment-mt-15">
                    {children}
                </div>
                <Button className="payment-mt-15" variant="primary" size="regular" onClick={handleOnClick}>
                    {intl.formatMessage({id: "FAILURE_API_BAN_BUTTON"})}
                </Button>
            </Text>
        </Alert>
    );
}

export const AlertErrorFailureCancel = injectIntl(AlertErrorFailureCancelComponent);