export interface IGetRedirectUrl
{
    status: string;
    externalRedirectUrl: string;
}

export interface IGetRedirectUrlReqPayload {}

export interface IBankInfoReqPayload {
    status: string;
    bankAccountNumber: string;
    transitNumber: string;
    bankCode: string;
    accountHolderName: string;
}

export interface IBankInfoRes{
    status: string;
    bankAccountNumber: string;
    transitNumber: string;
    bankCode: string;
    accountHolderName: string;
}

export interface IBankInfoFailure {
    headers: any;
    redirected:any;
    status: any;
    statusText: any;
    type: any;
    url: any;
    dataType: any;
    data: any;
}
