import { EventStreamServices, IEventStreamParams, IEventStreamCallback, IEventStreamErrorsShortcut, IEventStreamGlobalCallback, IEventStreamVoid } from "../EventStreamServices";
export declare abstract class BasePipe {
    protected stream: EventStreamServices;
    private subscriptions;
    constructor(stream: EventStreamServices);
    subscribe(eventType: string, callback: IEventStreamCallback): IEventStreamVoid;
    subscribe(config: IEventStreamParams): IEventStreamVoid;
    subscribe(callback: IEventStreamGlobalCallback): IEventStreamVoid;
    subscribeAll(cb: IEventStreamCallback): IEventStreamVoid;
    subscribeError(callback: IEventStreamCallback): IEventStreamVoid;
    send(type: string, payload?: any): void;
    sendError(errorType: string, errorPayload?: any, widgetName?: string): void;
    sendError(error: Error, widgetName?: string): void;
    unsubscribe(): void;
    get errors(): IEventStreamErrorsShortcut;
}
