import { IActionModule, IModule } from "../interfaces/modules";
import { IDependenciesByPackageName, INpmPackageBase } from "../util/dependencies";
import { IAction, IActionConstructor } from "./base";
export declare const _requireSort: (vals: string[]) => string[];
/**
 * Webpack projects can have multiple "roots" of `node_modules` that can be
 * the source of installed versions, including things like:
 *
 * - Node library deps: `/PATH/TO/node/v6.5.0/lib`
 * - Monorepo projects: `/PATH/TO/MY_PROJECT/package1`, `/PATH/TO/MY_PROJECT/package2`
 * - ... or the simple version of just one root for a project.
 *
 * The webpack stats object doesn't contain any information about what the root
 * / roots are, so we have to infer it, which we do by pulling apart the paths
 * of each `node_modules` installed module in a source bundle.
 *
 * @param mods {IModule[]} list of modules.
 * @returns {Promise<string[]>} list of package roots.
 */
export declare const _packageRoots: (mods: IModule[]) => Promise<string[]>;
export declare const _packageName: (baseName: string) => string;
export interface IVersionsMeta {
    depended: {
        num: number;
    };
    files: {
        num: number;
    };
    installed: {
        num: number;
    };
    packages: {
        num: number;
    };
    resolved: {
        num: number;
    };
}
interface IVersionsSummary extends IVersionsMeta {
    packageRoots: string[];
    commonRoot: string | null;
}
interface IVersionsPackages extends IDependenciesByPackageName {
    [packageName: string]: {
        [version: string]: {
            [filePath: string]: {
                modules: IActionModule[];
                skews: INpmPackageBase[][];
            };
        };
    };
}
interface IVersionsAsset {
    meta: IVersionsMeta;
    packages: IVersionsPackages;
}
interface IVersionsDataAssets {
    [asset: string]: IVersionsAsset;
}
export interface IVersionsData {
    assets: IVersionsDataAssets;
    meta: IVersionsSummary;
}
export declare const create: (opts: IActionConstructor) => IAction;
export {};
