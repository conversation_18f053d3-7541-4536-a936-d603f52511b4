{"name": "react-intl", "version": "6.4.4", "description": "Internationalize React apps. This library provides React components and an API to format dates, numbers, and strings, including pluralization and handling translations.", "keywords": ["intl", "i18n", "internationalization", "locale", "localization", "globalization", "react", "reactjs", "format", "formatting", "translate", "translation"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Caridy <PERSON>ino <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <dj<PERSON><PERSON>@users.noreply.github.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "DragonRaider5 <<EMAIL>>", "dropfen <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <janic<PERSON>ples<PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <jose<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "p10ns11y <<EMAIL>>", "papasmile <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "PutziSan <<EMAIL>>", "Rifat Nabi <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "rsamec <<EMAIL>>", "R<PERSON>ya44 <rust<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "salagadoola <<EMAIL>>", "<PERSON> <<EMAIL>>", "sbertal <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "sourabh2k15 <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <32746416+step<PERSON><PERSON><PERSON><EMAIL>>", "swiftfoot <<EMAIL>>", "<PERSON> <<EMAIL>>", "telaoumatenyanis <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> (TIJ) <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "zouxuoz <<EMAIL>>"], "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://formatjs.io/docs/react-intl", "bugs": {"url": "https://github.com/formatjs/formatjs/issues"}, "repository": {"type": "git", "url": "**************:formatjs/formatjs.git"}, "main": "index.js", "module": "lib/index.js", "types": "index.d.ts", "sideEffects": false, "dependencies": {"@types/hoist-non-react-statics": "^3.3.1", "@types/react": "16 || 17 || 18", "hoist-non-react-statics": "^3.3.2", "tslib": "^2.4.0", "@formatjs/intl": "2.9.0", "@formatjs/icu-messageformat-parser": "2.6.0", "@formatjs/ecma402-abstract": "1.17.0", "intl-messageformat": "10.5.0", "@formatjs/intl-displaynames": "6.5.0", "@formatjs/intl-listformat": "7.4.0"}, "devDependencies": {"@formatjs/intl-numberformat": "8.7.0", "@formatjs/intl-relativetimeformat": "11.2.4"}, "peerDependencies": {"react": "^16.6.0 || 17 || 18", "typescript": "^4.7 || 5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "browserslist": ["ie 11"], "gitHead": "773d6ebf881357f6e4c2dd7e8984b1bd0f69b4ca"}