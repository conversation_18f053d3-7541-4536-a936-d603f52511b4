import { createAction } from "redux-actions";
import Config from "../Config";
import { PaymentItem, CCDetails, CreditCardDetailsAction, IGetRedirectUrl, IGetRedirectUrlReqPayload, IBankInfoRes } from "../models"
import { ValidationErrors } from "../models/Error";
import { FormSubmit } from "../models/FormSubmit";
import PrepareCreditCardInfo from "../mutators/PrepareCreditCardInfo";
import { CreditCardInfo } from "../models/CreditCardInfo";
import { ICancelledItem } from "../models/CancelPreauth";

export const fetchPaymentItems = createAction<void>("FETCH_PREAUTHORIZED_PAYMENT") as (payload: any) => ReduxActions.Action<void>;
export const setPaymentItems = createAction<PaymentItem>("SET_PREAUTHORIZED_PAYMENT") as (state: PaymentItem) => ReduxActions.Action<PaymentItem>;
export const fetchPaymentItemsFailed = createAction<void>("FETCH_PREAUTHORIZED_PAYMENT_FAILED") as (payload: any) => ReduxActions.Action<void>;


export const setConfig = createAction<Config>("SET_CONFIG") as (payload: Config) => ReduxActions.Action<Config>;
export const getConfig = createAction<Config>("GET_CONFIG") as (payload: Config) => ReduxActions.Action<Config>;

export const onCreditCardNumberChange = createAction<CCDetails>(CreditCardDetailsAction.ONCHANGE_CREDITCARD_NUMBER) as (state: CCDetails) => ReduxActions.Action<CCDetails>;
export const onCardHolderNameChange = createAction<CCDetails>(CreditCardDetailsAction.ONCHANGE_CARDHOLDER_NAME) as (state: CCDetails) => ReduxActions.Action<CCDetails>;
export const onSecurityCodeChange = createAction<CCDetails>(CreditCardDetailsAction.ONCHANGE_SECURITY_CODE) as (state: CCDetails) => ReduxActions.Action<CCDetails>;
export const onCreditCardExpiryDateChange = createAction<CCDetails>(CreditCardDetailsAction.ONCHANGE_EXPIRY_DATE) as (state: CCDetails) => ReduxActions.Action<CCDetails>;

export const setValidationErrors = createAction<ValidationErrors>(CreditCardDetailsAction.SET_CREDIT_CARD_VALIDATION) as (state: ValidationErrors) => ReduxActions.Action<ValidationErrors>;
export const resetValidationErrors = createAction<ValidationErrors>(CreditCardDetailsAction.RESET_CREDIT_CARD_VALIDATION) as (state: ValidationErrors) => ReduxActions.Action<ValidationErrors>


export const createPaymentAction = createAction<{ ban: string, type: boolean, sub?: string | null }>("CREATE_PAYMENT") as (payload: { ban: string, type: boolean, sub?: string | null }) => ReduxActions.Action<{ ban: string, type: boolean, sub?: string | null }>;
export const createPaymentCompleted = createAction<any>("CREATE_PAYMENT_COMPLETED") as (payload: any) => ReduxActions.Action<any>;
export const createPaymentFailed = createAction<any>("CREATE_PAYMENT_FAILED") as (payload: any) => ReduxActions.Action<any>;


export const validateOrderPaymentAction = createAction<{ ban: string, type: boolean, details: any, isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined, }>("VALIDATE_ORDER_PAYMENT") as (payload: { ban: string, type: boolean, details: any, isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined, }) => ReduxActions.Action<{ ban: string, type: boolean, details: any, isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined }>;
export const validateOrderPaymentActionCompleted = createAction<any>("VALIDATE_ORDER_PAYMENT_COMPLETED") as (payload: any) => ReduxActions.Action<any>;
export const validateOrderPaymentActionFailed = createAction<any>("VALIDATE_ORDER_PAYMENT_FAILED") as (payload: any) => ReduxActions.Action<any>;


export const submitOrderPaymentAction = createAction<{ ban: string, type: boolean, sub?: string | null }>("SUBMIT_ORDER_PAYMENT") as (payload: { ban: string, type: boolean, sub?: string | null }) => ReduxActions.Action<{ ban: string, type: boolean, sub?: string | null }>;
export const submitOrderPaymentActionCompleted = createAction<any>("SUBMIT_ORDER_PAYMENT_COMPLETED") as (payload: any) => ReduxActions.Action<any>;
export const submitOrderPaymentActionFailed = createAction<any>("SUBMIT_ORDER_PAYMENT_FAILED") as (payload: any) => ReduxActions.Action<any>;

export const createMultiPaymentAction = createAction<{ ban: string, type: boolean, details: any, sub?: string | null }>("CREATE_MULTI_PAYMENT") as (payload: { ban: string, type: boolean, details: any, sub?: string | null }) => ReduxActions.Action<{ ban: string, type: boolean, details: any, sub?: string | null }>;
export const createMultiPaymentCompleted = createAction<any>("CREATE_MULTI_PAYMENT_COMPLETED") as (payload: any) => ReduxActions.Action<any>;
export const createMultiPaymentFailed = createAction<any>("CREATE_MULTI_PAYMENT_FAILED") as (payload: any) => ReduxActions.Action<any>;

export const validateMultiOrderPaymentAction = createAction<{ ban: string, type: boolean, details: any, accountInputValue: any, isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined, }>("VALIDATE_MULTI_ORDER_PAYMENT") as (payload: { ban: string, type: boolean, details: any, accountInputValue: any, isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined, }) => ReduxActions.Action<{ ban: string, type: boolean, details: any, accountInputValue: any, isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined }>;
export const validateMultiOrderPaymentActionCompleted = createAction<any>("VALIDATE_MULTI_ORDER_PAYMENT_COMPLETED") as (payload: any) => ReduxActions.Action<any>;
export const validateMultiOrderPaymentActionFailed = createAction<any>("VALIDATE_MULTI_ORDER_PAYMENT_FAILED") as (payload: any) => ReduxActions.Action<any>;

export const submitMultiOrderPaymentAction = createAction<{ ban: string, type: boolean, isbankSelected: boolean, sorryCredit: boolean, sorryDebit: boolean, sub?: string | null }>("SUBMIT_MULTI_ORDER_PAYMENT") as (payload: { ban: string, type: boolean, isbankSelected: boolean, sorryCredit: boolean, sorryDebit: boolean, details: any, sub?: string | null }) => ReduxActions.Action<{ ban: string, type: boolean, isbankSelected: boolean, sorryCredit: boolean, sorryDebit: boolean, details: any, sub?: string | null }>;
export const submitMultiOrderPaymentActionCompleted = createAction<any>("SUBMIT_MULTI_ORDER_PAYMENT_COMPLETED") as (payload: any) => ReduxActions.Action<any>;
export const submitMultiOrderPaymentActionFailed = createAction<any>("SUBMIT_MULTI_ORDER_PAYMENT_FAILED") as (payload: any) => ReduxActions.Action<any>;

export const tokenizeAndPropagateFormValues = createAction<{ form: FormSubmit, ban: string, type: boolean, details: any, isBankPaymentSelected: boolean, sub?: string | null }>(`TOKENIZE_AND_PROPAGE_FORM_VALUES`) as (payload: { form: FormSubmit, ban: string, type: boolean, details: any, isBankPaymentSelected: boolean, sub?: string | null }) => ReduxActions.Action<{ form: FormSubmit, ban: string, type: boolean, details: any, isBankPaymentSelected: boolean, sub?: string | null }>;
export const getPassKey = createAction<{ ban: string, sub?: string | null }>(`GET_PASSKEY`) as (payload: { ban: string, sub?: string | null }) => ReduxActions.Action<{ ban: string, sub?: string | null }>;
export const setPassKey = createAction<string>(`SET_PASSKEY`) as (passKey: string) => ReduxActions.Action<string>;
export const setCreditCardInfo = createAction<CreditCardInfo>(`SET_STATE`, PrepareCreditCardInfo as any) as (data: CreditCardInfo) => ReduxActions.Action<CreditCardInfo>;
export const clearCardNumber = createAction<void>(`CLEAR_CARD_NUMBER`) as () => ReduxActions.Action<void>;
export const cardTokenizationError = createAction<string>(`TOKENIZATION_ERROR`);
// export const cardTokenizationSuccess = createAction<string>(`TOKENIZATION_SUCCESS`);
export const cardTokenizationSuccess = createAction<string>(`TOKENIZATION_SUCCESS`) as (token: string) => ReduxActions.Action<string>;

export const getRedirectUrl = createAction<IGetRedirectUrlReqPayload>("GET_REDIRECT_URL") as (payload: IGetRedirectUrlReqPayload) => ReduxActions.Action<IGetRedirectUrlReqPayload>;
export const redirectUrlSuccess = createAction<IGetRedirectUrl>("GET_REDIRECT_URL_SUCCESS") as (payload: IGetRedirectUrl) => ReduxActions.Action<IGetRedirectUrl>;
export const redirectUrlFailure = createAction<any>("GET_REDIRECT_URL_FAILED") as (payload: any) => ReduxActions.Action<any>;

export const getInteracBankInfo = createAction<{ code: string }>("GET_INTERAC_BANK_INFO") as (payload: { code: string }) => ReduxActions.Action<{ code: string }>;
export const interacBankInfoSuccess = createAction<IBankInfoRes>("GET_INTERAC_BANK_INFO_SUCCESS") as (payload: IBankInfoRes) => ReduxActions.Action<IBankInfoRes>;
export const interacBankInfoFailure = createAction<any>("GET_INTERAC_BANK_INFO_FAILED") as (payload: any) => ReduxActions.Action<any>;

export const setIsLoading = createAction<boolean>("SET_IS_LOADING") as (payload: boolean) => ReduxActions.Action<boolean>;
export const setInteractBankInfoFailure = createAction<any>("RESET_FAILED_INTERACT_BANK_INFO") as (payload: any) => ReduxActions.Action<any>;

export const OmnitureOnLoad = createAction<any>("OMNITURE_ON_LOAD") as (payload: any) => ReduxActions.Action<any>;
export const OmnitureOnPaymentSelect = createAction<any>("OMNITURE_ON_PAYMENT_SELECT") as (payload: any) => ReduxActions.Action<any>;

export const OmnitureOnFindTransactionLightBox = createAction<any>("OMNITURE_ON_FIND_TRANSACTION_LIGHT_BOX") as (payload: any) => ReduxActions.Action<any>;
export const OmnitureOnBoxNameLightBox = createAction<any>("OMNITURE_ON_BOX_NAME_LIGHT_BOX") as (payload: any) => ReduxActions.Action<any>;
export const OmnitureOnSecurityCodeLightBox = createAction<any>("OMNITURE_ON_SECURITY_CODE_LIGHT_BOX") as (payload: any) => ReduxActions.Action<any>;

export const OmnitureOnReview = createAction<any>("OMNITURE_ON_REVIEW") as (payload: any) => ReduxActions.Action<any>;
export const OmnitureOnConfirmation = createAction<any>("OMNITURE_ON_CONFIRMATION") as (payload: any) => ReduxActions.Action<any>;
export const OmnitureOnConfirmationFailure = createAction<any>("OMNITURE_ON_CONFIRMATION_FAILURE") as (payload: any) => ReduxActions.Action<any>;
export const OmnitureOnCancelationCompleted = createAction<any>("OMNITURE_ON_CANCELATION_COMPLETED") as (payload: any) => ReduxActions.Action<any>;
export const OmnitureOnCancelationPartiallyCompleted = createAction<any>("OMNITURE_ON_CANCELATION_PARTIALLY_COMPLETED") as (payload: any) => ReduxActions.Action<any>;
export const OmnitureOnCancelationFailed = createAction<any>("OMNITURE_ON_CANCELATION_FAILED") as (payload: any) => ReduxActions.Action<any>;
export const OmnitureOnCancelationPartiallyFailed = createAction<any>("OMNITURE_ON_CANCELATION_PARTIALLY_FAILED") as (payload: any) => ReduxActions.Action<any>;
export const OmnitureOnApiFailure = createAction<any>("OMNITURE_ON_VALIDATION_FAILURE") as (payload: any) => ReduxActions.Action<any>;
export const OmnitureOnInteracFailure = createAction<any>("OMNITURE_ON_INTERAC_FAILURE") as (payload: any) => ReduxActions.Action<any>;

export const apiConfirmationStatus = createAction<any>("API_CONFIRMATION_STATUS") as (payload: any) => ReduxActions.Action<any>;

export const cancelPreauthAction = createAction<{ bans: string[] }>("CANCEL_PREAUTH") as (payload: { bans: string[] }) => ReduxActions.Action<{ bans: string[] }>;
export const cancelPreauthSuccessAction = createAction<ICancelledItem[]>("CANCEL_PREAUTH_SUCCESS") as (payload: ICancelledItem[]) => ReduxActions.Action<ICancelledItem[]>;
export const cancelPreauthFailureAction = createAction<any>("CANCEL_PREAUTH_FAILED") as (payload: any) => ReduxActions.Action<any>;
