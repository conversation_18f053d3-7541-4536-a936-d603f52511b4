import { IDuplicatesData } from "../lib/actions/duplicates";
import { IVersionsData } from "../lib/actions/versions";
import { IWebpackStats } from "../lib/interfaces/webpack-stats";
interface ICompiler {
    hooks?: any;
    plugin?: (name: string, callback: () => void) => void;
}
export interface ICompilation {
    errors: Error[];
    warnings: Error[];
    getStats: () => {
        toJson: (opts: object) => IWebpackStats;
    };
}
interface IDuplicatesPluginConstructor {
    verbose?: boolean;
    emitErrors?: boolean;
    emitHandler?: (report: string) => {};
    ignoredPackages?: (string | RegExp)[];
}
export declare const _getDuplicatesVersionsData: (dupData: IDuplicatesData, pkgDataOrig: IVersionsData, addWarning: (val: string) => number) => IVersionsData;
export declare class DuplicatesPlugin {
    private opts;
    constructor({ verbose, emitErrors, emitHand<PERSON>, ignoredPackages }?: IDuplicatesPluginConstructor);
    apply(compiler: ICompiler): void;
    analyze(compilation: ICompilation, callback?: () => void): Promise<undefined>;
}
export {};
