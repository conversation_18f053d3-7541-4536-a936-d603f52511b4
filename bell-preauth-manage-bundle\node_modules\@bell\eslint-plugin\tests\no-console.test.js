const { RuleTester } = require("eslint");
const rule = require("../lib/rules/no-console");

describe("No Console Log", () => {
  const ruleTester = new RuleTester({
    languageOptions: {
      ecmaVersion: 2021,
    },
  });

  ruleTester.run("no-console-log", rule, {
    valid: [
      {
        code: "let x = 5;",
      },
    ],
    invalid: [
      {
        code: "console.log('Hello');",
        errors: [{ message: "Unexpected console.log statement." }],
        output: "", // Expected result after autofix (removes `console.log`)
      },
      {
        code: "console.log('Test message'); let a = 5;",
        errors: [{ message: "Unexpected console.log statement." }],
        output: " let a = 5;", // Expected result after removing `console.log`
      },
    ],
  });
});
